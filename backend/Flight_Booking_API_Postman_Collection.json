{"info": {"name": "Flight Booking API - Existing Structure", "description": "Complete API collection matching your existing frontend structure with exact endpoints and request formats", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000/apis", "type": "string"}, {"key": "test_url", "value": "https://app.digiyatra.in/apis", "type": "string"}, {"key": "saved_tui", "value": "", "type": "string"}, {"key": "saved_index", "value": "", "type": "string"}, {"key": "saved_booking_id", "value": "", "type": "string"}, {"key": "auth_token", "value": "Guest", "type": "string"}], "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "X-API-Key", "type": "string"}, {"key": "value", "value": "{{api_key}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic timestamp", "pm.environment.set('timestamp', new Date().toISOString());", "", "// Set random test ID", "pm.environment.set('test_id', 'TEST_' + Math.floor(Math.random() * 100000));", "", "// Set future date for testing", "const futureDate = new Date();", "futureDate.setDate(futureDate.getDate() + 30);", "pm.environment.set('future_date', futureDate.toISOString().split('T')[0]);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for all requests", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(10000);", "});", "", "pm.test('Response has valid JSON', function () {", "    pm.response.to.be.json;", "});", "", "// Save TUI if present", "if (pm.response.json().TUI) {", "    pm.environment.set('saved_tui', pm.response.json().TUI);", "}", "", "// Save FareId if present in results", "const responseJson = pm.response.json();", "if (responseJson.Results && responseJson.Results.length > 0 && responseJson.Results[0].FareId) {", "    pm.environment.set('saved_fare_id', responseJson.Results[0].FareId);", "}"]}}], "item": [{"name": "🔧 Core Flight Services", "item": [{"name": "Flight Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{future_date}}\"\n    }\n  ],\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"FareType\": \"REGULAR\",\n  \"DirectFlight\": false,\n  \"Airlines\": [\"AI\", \"6E\"],\n  \"MaxStops\": 2\n}"}, "url": {"raw": "{{base_url}}/flight-search", "host": ["{{base_url}}"], "path": ["flight-search"]}, "description": "Search for flights with comprehensive parameters"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Flight search successful', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('TUI');", "    pm.expect(jsonData).to.have.property('status', 'success');", "    pm.expect(jsonData).to.have.property('Results');", "});"]}}]}, {"name": "Flight Search List (Retrieve Results)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"TUI\": \"{{saved_tui}}\"\n}"}, "url": {"raw": "{{base_url}}/flight-search-list", "host": ["{{base_url}}"], "path": ["flight-search-list"]}, "description": "Retrieve cached flight search results using TUI"}}, {"name": "Flight Pricing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FareId\": \"{{saved_fare_id}}\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0\n}"}, "url": {"raw": "{{base_url}}/flight-pricing", "host": ["{{base_url}}"], "path": ["flight-pricing"]}, "description": "Get detailed pricing for a specific fare"}}, {"name": "Flight Pricing List (Retrieve Pricing)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"TUI\": \"{{saved_tui}}\"\n}"}, "url": {"raw": "{{base_url}}/flight-pricing-list", "host": ["{{base_url}}"], "path": ["flight-pricing-list"]}, "description": "Retrieve cached pricing results using TUI"}}, {"name": "Round Trip Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{future_date}}\"\n    },\n    {\n      \"From\": \"BOM\",\n      \"To\": \"DEL\",\n      \"OnwardDate\": \"2024-02-20\"\n    }\n  ],\n  \"ADT\": 2,\n  \"CHD\": 1,\n  \"INF\": 0,\n  \"Cabin\": \"B\",\n  \"FareType\": \"REGULAR\"\n}"}, "url": {"raw": "{{base_url}}/flight-search", "host": ["{{base_url}}"], "path": ["flight-search"]}, "description": "Search for round-trip flights with multiple passengers"}}], "description": "Core flight booking operations including search, pricing, and result retrieval"}, {"name": "⚡ Enhanced Async Operations", "item": [{"name": "Async Flight Search", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{future_date}}\"\n    }\n  ],\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"FareType\": \"REGULAR\",\n  \"async\": true\n}"}, "url": {"raw": "{{base_url}}/async/search", "host": ["{{base_url}}"], "path": ["async", "search"]}, "description": "Enhanced async flight search with optimization features"}}, {"name": "Async Flight Pricing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"FareId\": \"{{saved_fare_id}}\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"async\": true\n}"}, "url": {"raw": "{{base_url}}/async/pricing", "host": ["{{base_url}}"], "path": ["async", "pricing"]}, "description": "Enhanced async pricing with circuit breaker protection"}}, {"name": "Search Service Performance", "request": {"method": "GET", "url": {"raw": "{{base_url}}/performance/search", "host": ["{{base_url}}"], "path": ["performance", "search"]}, "description": "Get search service performance metrics and statistics"}}, {"name": "Provider Performance", "request": {"method": "GET", "url": {"raw": "{{base_url}}/performance/provider", "host": ["{{base_url}}"], "path": ["performance", "provider"]}, "description": "Get provider client performance metrics including circuit breaker status"}}], "description": "Enhanced async operations with circuit breaker protection and performance monitoring"}, {"name": "🗄️ Cache Management (Admin)", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{base_url}}/cache/statistics", "host": ["{{base_url}}"], "path": ["cache", "statistics"]}, "description": "Get comprehensive cache performance statistics across all layers"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Cache statistics retrieved', function () {", "    pm.response.to.have.status(200);", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('overall_performance');", "    pm.expect(jsonData).to.have.property('cache_layers');", "});"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"routes\": [\n    {\n      \"from\": \"DEL\",\n      \"to\": \"BOM\",\n      \"dates\": [\"{{future_date}}\", \"2024-02-16\"]\n    },\n    {\n      \"from\": \"BOM\",\n      \"to\": \"DEL\",\n      \"dates\": [\"2024-02-20\", \"2024-02-21\"]\n    }\n  ],\n  \"max_routes\": 20,\n  \"future_days\": 3,\n  \"priority\": \"high\"\n}"}, "url": {"raw": "{{base_url}}/cache/warm", "host": ["{{base_url}}"], "path": ["cache", "warm"]}, "description": "Trigger intelligent cache warming for popular routes"}}, {"name": "Cache Invalidation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"route\": \"DEL-BOM\",\n  \"date\": \"{{future_date}}\",\n  \"invalidation_type\": \"route_specific\"\n}"}, "url": {"raw": "{{base_url}}/cache/invalidate", "host": ["{{base_url}}"], "path": ["cache", "invalidate"]}, "description": "Invalidate specific cache entries for route and date"}}, {"name": "Cache Health Check", "request": {"method": "GET", "url": {"raw": "{{base_url}}/cache/health", "host": ["{{base_url}}"], "path": ["cache", "health"]}, "description": "Check cache system health and performance"}}], "description": "Multi-layer cache management with L1 Memory + L2 Redis + L3 Persistent caching"}, {"name": "🏢 Database & Analytics (Admin)", "item": [{"name": "Database Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/database/stats", "host": ["{{base_url}}"], "path": ["admin", "database", "stats"]}, "description": "Get database connection pool and query performance statistics"}}, {"name": "Analytics Dashboard", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/analytics/dashboard", "host": ["{{base_url}}"], "path": ["admin", "analytics", "dashboard"]}, "description": "Get comprehensive analytics dashboard with performance insights"}}, {"name": "Optimized Booking Creation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"passenger_details\": {\n    \"adults\": [\n      {\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"<PERSON><PERSON>\",\n        \"date_of_birth\": \"1990-01-15\",\n        \"passport_number\": \"A12345678\",\n        \"nationality\": \"IN\",\n        \"gender\": \"<PERSON>\"\n      }\n    ],\n    \"children\": [],\n    \"infants\": []\n  },\n  \"flight_details\": {\n    \"fare_id\": \"{{saved_fare_id}}\",\n    \"total_amount\": 10000,\n    \"currency\": \"INR\",\n    \"booking_class\": \"E\"\n  },\n  \"contact_details\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91-9876543210\",\n    \"address\": {\n      \"street\": \"123 Main St\",\n      \"city\": \"Delhi\",\n      \"state\": \"Delhi\",\n      \"country\": \"IN\",\n      \"postal_code\": \"110001\"\n    }\n  },\n  \"payment_details\": {\n    \"payment_method\": \"credit_card\",\n    \"card_type\": \"visa\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/booking/create-optimized", "host": ["{{base_url}}"], "path": ["admin", "booking", "create-optimized"]}, "description": "Create booking with optimized database operations and batch processing"}}, {"name": "Database Query Execution", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"query_type\": \"analytics\",\n  \"parameters\": {\n    \"metric\": \"booking_trends\",\n    \"time_range\": \"7_days\",\n    \"group_by\": \"route\"\n  }\n}"}, "url": {"raw": "{{base_url}}/admin/database/query", "host": ["{{base_url}}"], "path": ["admin", "database", "query"]}, "description": "Execute optimized database queries for analytics"}}], "description": "Advanced database operations with connection pooling and query optimization"}, {"name": "📊 Advanced Monitoring (Admin)", "item": [{"name": "Comprehensive System Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/advanced/monitoring/comprehensive-status", "host": ["{{base_url}}"], "path": ["advanced", "monitoring", "comprehensive-status"]}, "description": "Get comprehensive system health and performance status"}}, {"name": "Dashboard Service Statistics", "request": {"method": "GET", "url": {"raw": "{{base_url}}/advanced/dashboard/service-stats", "host": ["{{base_url}}"], "path": ["advanced", "dashboard", "service-stats"]}, "description": "Get real-time dashboard service statistics and WebSocket metrics"}}, {"name": "Generate Daily Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"daily_summary\",\n  \"format\": \"json\",\n  \"hours\": 24\n}"}, "url": {"raw": "{{base_url}}/advanced/reporting/generate-report", "host": ["{{base_url}}"], "path": ["advanced", "reporting", "generate-report"]}, "description": "Generate automated daily performance report"}}, {"name": "Generate HTML Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_type\": \"weekly_performance\",\n  \"format\": \"html\",\n  \"hours\": 168\n}"}, "url": {"raw": "{{base_url}}/advanced/reporting/generate-report", "host": ["{{base_url}}"], "path": ["advanced", "reporting", "generate-report"]}, "description": "Generate weekly performance report in HTML format"}}, {"name": "Predictive Analytics - Response Times", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"metric_name\": \"response_times\",\n  \"hours_ahead\": 24\n}"}, "url": {"raw": "{{base_url}}/advanced/analytics/forecast", "host": ["{{base_url}}"], "path": ["advanced", "analytics", "forecast"]}, "description": "Generate ML-based forecast for response times"}}, {"name": "Predictive Analytics - Cache Hit Rates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"metric_name\": \"cache_hit_rates\",\n  \"hours_ahead\": 12\n}"}, "url": {"raw": "{{base_url}}/advanced/analytics/forecast", "host": ["{{base_url}}"], "path": ["advanced", "analytics", "forecast"]}, "description": "Generate ML-based forecast for cache performance"}}], "description": "Advanced monitoring with real-time dashboards, automated reporting, and predictive analytics"}]}