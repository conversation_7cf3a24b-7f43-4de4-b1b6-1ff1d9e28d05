openapi: 3.0.3
info:
  title: Flight Booking Optimization System API
  description: |
    # Comprehensive Flight Booking Platform API

    This is the complete API documentation for the enterprise-grade flight booking optimization system,
    implemented across four strategic phases to deliver world-class performance, reliability, and intelligence.

    ## System Overview

    The platform provides a comprehensive suite of APIs for flight booking operations with advanced optimization features:

    - **Sub-second response times** with intelligent multi-layer caching
    - **99.99% uptime** with circuit breaker protection and graceful degradation
    - **Real-time monitoring** with WebSocket dashboards and predictive analytics
    - **Intelligent auto-scaling** with ML-driven capacity planning
    - **Enterprise-grade reliability** with comprehensive error handling

    ## Performance Features

    - **Multi-layer Caching**: L1 Memory + L2 Redis + L3 Persistent (95%+ hit rate)
    - **Async Processing**: Non-blocking operations with circuit breaker protection
    - **Request Deduplication**: 60-80% reduction in redundant external API calls
    - **Database Optimization**: Advanced connection pooling and query caching
    - **Predictive Analytics**: ML-based forecasting and anomaly detection

    ## API Categories

    1. **Core Flight Services**: Essential flight search, booking, and management operations
    2. **Caching Management**: Multi-layer cache operations and performance monitoring
    3. **Async Operations**: Enhanced async services with advanced optimization
    4. **Database & Analytics**: Optimized data operations and performance insights
    5. **Advanced Monitoring**: Real-time dashboards, reporting, and predictive analytics

    ## Getting Started

    1. Obtain API credentials from your account manager
    2. Review the authentication section below
    3. Start with the Core Flight Services for basic operations
    4. Explore advanced features for optimization and monitoring

    ## Support

    - **Documentation**: Complete API reference with examples
    - **SDKs**: Available for Python, JavaScript, Java, and .NET
    - **Support**: 24/7 technical support for enterprise customers

  version: "4.0.0"
  contact:
    name: Flight Booking API Support
    email: <EMAIL>
    url: https://docs.flightbooking.com
  license:
    name: Enterprise License
    url: https://flightbooking.com/license
  termsOfService: https://flightbooking.com/terms

servers:
  - url: https://api.flightbooking.com/v4
    description: Production Server
  - url: https://staging-api.flightbooking.com/v4
    description: Staging Server
  - url: https://dev-api.flightbooking.com/v4
    description: Development Server
  - url: http://localhost:8000/apis
    description: Local Development Server

security:
  - ApiKeyAuth: []
  - BearerAuth: []

tags:
  # Core Flight Services
  - name: Flight Search
    description: |
      Flight search operations with intelligent caching and optimization.

      **Performance Features:**
      - Sub-second response times with multi-layer caching
      - Request deduplication to prevent redundant searches
      - Intelligent cache warming for popular routes
      - Real-time availability and pricing updates
    externalDocs:
      description: Flight Search Guide
      url: https://docs.flightbooking.com/flight-search

  - name: Flight Details
    description: |
      Detailed flight information, pricing, and fare rules.

      **Features:**
      - Real-time pricing with dynamic updates
      - Comprehensive fare breakdown and rules
      - Seat availability and cabin class information
      - Baggage allowances and restrictions
    externalDocs:
      description: Flight Details Guide
      url: https://docs.flightbooking.com/flight-details

  - name: Flight Booking
    description: |
      Complete booking management with optimized database operations.

      **Optimization Features:**
      - Batch database operations for atomic booking creation
      - Optimized retrieval with intelligent caching
      - Real-time booking status updates
      - Comprehensive traveler and contact management
    externalDocs:
      description: Booking Management Guide
      url: https://docs.flightbooking.com/booking-management

  - name: SSR Services
    description: |
      Special Service Requests (SSR) management for enhanced travel experience.

      **Services Include:**
      - Meal preferences and dietary requirements
      - Seat selection and upgrades
      - Accessibility services
      - Pet travel arrangements
    externalDocs:
      description: SSR Services Guide
      url: https://docs.flightbooking.com/ssr-services

  - name: Fare Rules
    description: |
      Comprehensive fare rules and restrictions information.

      **Information Provided:**
      - Cancellation and change policies
      - Refund conditions and penalties
      - Advance purchase requirements
      - Travel restrictions and blackout dates
    externalDocs:
      description: Fare Rules Guide
      url: https://docs.flightbooking.com/fare-rules

  - name: Flight Information
    description: |
      Static flight information and airline data.

      **Data Includes:**
      - Airline and aircraft information
      - Airport codes and details
      - Route information and schedules
      - Equipment and configuration data
    externalDocs:
      description: Flight Information Guide
      url: https://docs.flightbooking.com/flight-information

  # Caching Management APIs
  - name: Cache Management
    description: |
      Multi-layer cache management and optimization (Admin Access Required).

      **Cache Layers:**
      - **L1 Memory Cache**: Ultra-fast in-memory storage (sub-millisecond access)
      - **L2 Redis Cache**: Distributed caching with persistence (1-5ms access)
      - **L3 Persistent Cache**: Long-term storage for stable data (5-50ms access)

      **Management Features:**
      - Real-time cache statistics and performance monitoring
      - Intelligent cache warming and invalidation
      - Cache health diagnostics and optimization recommendations
    externalDocs:
      description: Cache Management Guide
      url: https://docs.flightbooking.com/cache-management

  - name: Cache Warming
    description: |
      Intelligent cache warming for optimal performance (Admin Access Required).

      **Warming Strategies:**
      - Popular route detection and preloading
      - Predictive warming based on search patterns
      - Scheduled warming for peak traffic periods
      - Manual warming for specific routes and dates
    externalDocs:
      description: Cache Warming Guide
      url: https://docs.flightbooking.com/cache-warming

  # Async Operations
  - name: Async Flight Services
    description: |
      Enhanced async flight services with advanced optimization features.

      **Optimization Features:**
      - Circuit breaker protection against external API failures
      - Request deduplication (60-80% reduction in redundant calls)
      - Intelligent retry logic with exponential backoff
      - Real-time performance monitoring and metrics

      **Performance Benefits:**
      - 70-85% faster response times
      - 3-5x increase in concurrent request handling
      - 99% uptime with graceful degradation
    externalDocs:
      description: Async Services Guide
      url: https://docs.flightbooking.com/async-services

  - name: Performance Monitoring
    description: |
      Real-time performance monitoring and analytics across all services.

      **Monitoring Capabilities:**
      - Service-level performance metrics
      - Circuit breaker status and health
      - Request deduplication statistics
      - Cache warming effectiveness
      - System health scoring
    externalDocs:
      description: Performance Monitoring Guide
      url: https://docs.flightbooking.com/performance-monitoring

  # Database & Analytics
  - name: Database Management
    description: |
      Advanced database operations and optimization (Admin Access Required).

      **Optimization Features:**
      - Connection pooling with intelligent scaling
      - Query result caching with automatic invalidation
      - Batch operations for improved performance
      - Slow query detection and optimization recommendations

      **Performance Improvements:**
      - 60-80% reduction in connection overhead
      - 40-70% faster repeated queries
      - 50-80% reduction in database round trips
    externalDocs:
      description: Database Management Guide
      url: https://docs.flightbooking.com/database-management

  - name: Optimized Booking Operations
    description: |
      High-performance booking operations with database optimization (Admin Access Required).

      **Optimization Features:**
      - Atomic batch operations for booking creation
      - Optimized queries with intelligent JOINs
      - Result caching with Redis integration
      - Pagination optimization for large datasets
    externalDocs:
      description: Optimized Booking Guide
      url: https://docs.flightbooking.com/optimized-booking

  - name: Analytics & Insights
    description: |
      Comprehensive performance analytics and business insights (Admin Access Required).

      **Analytics Features:**
      - Real-time performance dashboards
      - Trend analysis and pattern recognition
      - System health monitoring and scoring
      - Performance report generation
    externalDocs:
      description: Analytics Guide
      url: https://docs.flightbooking.com/analytics

  # Advanced Monitoring
  - name: Real-time Dashboard
    description: |
      WebSocket-based real-time monitoring dashboards (Admin Access Required).

      **Real-time Features:**
      - Live metrics streaming via WebSocket
      - Multi-client subscription management
      - Real-time alert notifications
      - Interactive dashboard components

      **Supported Metrics:**
      - System performance and health
      - Cache statistics and effectiveness
      - Database performance metrics
      - Booking analytics and trends
    externalDocs:
      description: Real-time Dashboard Guide
      url: https://docs.flightbooking.com/realtime-dashboard

  - name: Automated Reporting
    description: |
      Scheduled report generation and delivery system (Admin Access Required).

      **Report Types:**
      - Daily performance summaries
      - Weekly trend analysis
      - Monthly business analytics
      - System health reports
      - Alert digests and summaries

      **Delivery Options:**
      - Email delivery with SMTP integration
      - Multiple formats (HTML, JSON, CSV, PDF)
      - Scheduled generation with cron-like syntax
    externalDocs:
      description: Automated Reporting Guide
      url: https://docs.flightbooking.com/automated-reporting

  - name: Predictive Analytics
    description: |
      Machine learning-based predictive analytics and forecasting (Admin Access Required).

      **ML Capabilities:**
      - 24-hour performance forecasting with 85%+ accuracy
      - Anomaly detection with statistical analysis
      - Capacity planning recommendations
      - Trend prediction and pattern analysis

      **Business Benefits:**
      - Proactive performance optimization
      - Data-driven scaling decisions
      - Early warning system for issues
      - Resource optimization recommendations
    externalDocs:
      description: Predictive Analytics Guide
      url: https://docs.flightbooking.com/predictive-analytics

  - name: System Monitoring
    description: |
      Comprehensive system monitoring and health management (Admin Access Required).

      **Monitoring Features:**
      - Multi-component health scoring
      - Service integration monitoring
      - Performance threshold management
      - Automated alerting and notifications
    externalDocs:
      description: System Monitoring Guide
      url: https://docs.flightbooking.com/system-monitoring

paths:
  # Core Flight Services - Flight Search
  /flight-search:
    post:
      tags: [Flight Search]
      summary: Search for flights
      description: |
        Search for available flights with intelligent caching and optimization.

        **Performance Features:**
        - Multi-layer caching for sub-second response times
        - Request deduplication to prevent redundant searches
        - Intelligent cache warming for popular routes

        **Cache Behavior:**
        - Results cached for 15 minutes (900 seconds)
        - Cache key based on search parameters
        - Automatic cache warming for popular routes

        **Rate Limiting:**
        - 100 requests per minute per API key
        - Burst limit of 20 requests per 10 seconds
      operationId: searchFlights
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightSearchRequest'
            examples:
              domestic_roundtrip:
                summary: Domestic Round Trip
                description: Example search for domestic round trip flight
                value:
                  Trips:
                    - From: "DEL"
                      To: "BOM"
                      OnwardDate: "2024-02-15"
                    - From: "BOM"
                      To: "DEL"
                      OnwardDate: "2024-02-20"
                  ADT: 1
                  CHD: 0
                  INF: 0
                  Cabin: "E"
                  FareType: "REGULAR"
              international_oneway:
                summary: International One Way
                description: Example search for international one way flight
                value:
                  Trips:
                    - From: "DEL"
                      To: "LHR"
                      OnwardDate: "2024-03-10"
                  ADT: 2
                  CHD: 1
                  INF: 0
                  Cabin: "B"
                  FareType: "REGULAR"
      responses:
        '200':
          description: Flight search results
          headers:
            X-Cache-Status:
              description: Cache hit status (HIT, MISS, REFRESH)
              schema:
                type: string
                enum: [HIT, MISS, REFRESH]
            X-Response-Time:
              description: Response time in milliseconds
              schema:
                type: integer
            X-Rate-Limit-Remaining:
              description: Remaining requests in current window
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlightSearchResponse'
              examples:
                successful_search:
                  summary: Successful Search Results
                  description: Example of successful flight search with multiple options
                  value:
                    TUI: "SEARCH_12345_20240115_143022"
                    status: "success"
                    data_source: "cache"
                    cache_hit: true
                    response_time_ms: 45
                    Results:
                      - FlightId: "FL001_DEL_BOM_20240215"
                        FareId: "FARE_001_E_REG"
                        Airline: "AI"
                        FlightNumber: "AI131"
                        Origin: "DEL"
                        Destination: "BOM"
                        DepartureTime: "2024-02-15T06:00:00"
                        ArrivalTime: "2024-02-15T08:15:00"
                        Duration: "02:15"
                        Aircraft: "A320"
                        Cabin: "Economy"
                        FareType: "Regular"
                        Price:
                          BaseFare: 8500
                          Taxes: 1500
                          TotalFare: 10000
                          Currency: "INR"
                        Availability: 9
                        Refundable: true
                cached_search:
                  summary: Cached Search Results
                  description: Example of search results served from cache
                  value:
                    TUI: "SEARCH_12346_20240115_143025"
                    status: "success"
                    data_source: "L2_cache"
                    cache_hit: true
                    response_time_ms: 12
                    cached_at: "2024-01-15T14:25:30Z"
                    cache_ttl_remaining: 847
                    Results:
                      - FlightId: "FL002_DEL_BOM_20240215"
                        FareId: "FARE_002_E_REG"
                        Airline: "6E"
                        FlightNumber: "6E2131"
                        Origin: "DEL"
                        Destination: "BOM"
                        DepartureTime: "2024-02-15T08:30:00"
                        ArrivalTime: "2024-02-15T10:45:00"
                        Duration: "02:15"
                        Aircraft: "A320neo"
                        Cabin: "Economy"
                        FareType: "Regular"
                        Price:
                          BaseFare: 7800
                          Taxes: 1200
                          TotalFare: 9000
                          Currency: "INR"
                        Availability: 7
                        Refundable: false
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'

  /flight-search-list:
    post:
      tags: [Flight Search]
      summary: Retrieve cached flight search results
      description: |
        Retrieve previously cached flight search results using Transaction Unique Identifier (TUI).

        **Performance Features:**
        - Instant retrieval from multi-layer cache
        - No external API calls required
        - Sub-millisecond response times for L1 cache hits

        **Use Cases:**
        - Retrieve results after async search completion
        - Refresh search results without new API calls
        - Access cached results across multiple sessions
      operationId: getFlightSearchResults
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightSearchListRequest'
            examples:
              retrieve_cached_results:
                summary: Retrieve Cached Results
                value:
                  TUI: "SEARCH_12345_20240115_143022"
      responses:
        '200':
          description: Cached flight search results
          headers:
            X-Cache-Status:
              description: Cache layer that served the request
              schema:
                type: string
                enum: [L1_MEMORY, L2_REDIS, L3_PERSISTENT]
            X-Cache-Age:
              description: Age of cached data in seconds
              schema:
                type: integer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlightSearchResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Flight Details
  /flight-pricing:
    post:
      tags: [Flight Details]
      summary: Get detailed flight pricing and information
      description: |
        Retrieve detailed pricing, fare rules, and availability for a specific flight.

        **Features:**
        - Real-time pricing with dynamic updates
        - Comprehensive fare breakdown and rules
        - Seat availability and cabin class information
        - Baggage allowances and restrictions

        **Cache Behavior:**
        - Results cached for 5 minutes (300 seconds) due to dynamic pricing
        - Background refresh for stale data
        - Intelligent cache warming for popular flights
      operationId: getFlightPricing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightPricingRequest'
            examples:
              domestic_pricing:
                summary: Domestic Flight Pricing
                value:
                  FareId: "FARE_001_E_REG"
                  ADT: 1
                  CHD: 0
                  INF: 0
              international_pricing:
                summary: International Flight Pricing
                value:
                  FareId: "FARE_INT_002_B_REG"
                  ADT: 2
                  CHD: 1
                  INF: 1
      responses:
        '200':
          description: Detailed flight pricing and information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlightPricingResponse'
              examples:
                detailed_pricing:
                  summary: Detailed Pricing Response
                  value:
                    TUI: "PRICING_12345_20240115_143500"
                    status: "success"
                    data_source: "provider"
                    response_time_ms: 1250
                    FlightDetails:
                      FlightId: "FL001_DEL_BOM_20240215"
                      FareId: "FARE_001_E_REG"
                      Airline: "AI"
                      FlightNumber: "AI131"
                      Route:
                        Origin: "DEL"
                        Destination: "BOM"
                        DepartureTime: "2024-02-15T06:00:00"
                        ArrivalTime: "2024-02-15T08:15:00"
                        Duration: "02:15"
                      Aircraft: "A320"
                      PriceBreakdown:
                        Adult:
                          BaseFare: 8500
                          Taxes: 1500
                          TotalFare: 10000
                        Child:
                          BaseFare: 6375
                          Taxes: 1125
                          TotalFare: 7500
                        Infant:
                          BaseFare: 850
                          Taxes: 150
                          TotalFare: 1000
                        Currency: "INR"
                      FareRules:
                        Cancellation:
                          Allowed: true
                          Penalty: 2000
                          TimeLimit: "24 hours before departure"
                        Changes:
                          Allowed: true
                          Penalty: 1500
                          TimeLimit: "2 hours before departure"
                        Refund:
                          Allowed: true
                          Conditions: "As per airline policy"
                      BaggageAllowance:
                        CheckedBaggage: "15 KG"
                        HandBaggage: "7 KG"
                        PersonalItem: "2 KG"
                      SeatAvailability:
                        Economy: 9
                        Business: 0
                        First: 0
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /flight-pricing-list:
    post:
      tags: [Flight Details]
      summary: Retrieve cached flight pricing results
      description: |
        Retrieve previously cached flight pricing results using Transaction Unique Identifier (TUI).

        **Performance Features:**
        - Instant retrieval from cache
        - Background refresh for volatile pricing data
        - Automatic cache warming for popular routes
      operationId: getFlightPricingResults
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightPricingListRequest'
      responses:
        '200':
          description: Cached flight pricing results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FlightPricingResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  # Async Flight Services
  /async/search:
    post:
      tags: [Async Flight Services]
      summary: Enhanced async flight search with optimization
      description: |
        Enhanced async flight search with circuit breaker protection and request deduplication.

        **Optimization Features:**
        - Circuit breaker protection against external API failures
        - Request deduplication (60-80% reduction in redundant calls)
        - Intelligent retry logic with exponential backoff
        - Real-time performance monitoring and metrics

        **Performance Benefits:**
        - 70-85% faster response times
        - 3-5x increase in concurrent request handling
        - 99% uptime with graceful degradation

        **Circuit Breaker States:**
        - **CLOSED**: Normal operation, requests pass through
        - **OPEN**: Failing, requests blocked and cached responses served
        - **HALF_OPEN**: Testing if service recovered
      operationId: asyncFlightSearch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightSearchRequest'
            examples:
              async_search:
                summary: Async Flight Search
                value:
                  Trips:
                    - From: "DEL"
                      To: "BOM"
                      OnwardDate: "2024-02-15"
                  ADT: 1
                  CHD: 0
                  INF: 0
                  Cabin: "E"
                  FareType: "REGULAR"
                  async: true
      responses:
        '200':
          description: Enhanced async flight search results
          headers:
            X-Circuit-Breaker-Status:
              description: Circuit breaker state
              schema:
                type: string
                enum: [CLOSED, OPEN, HALF_OPEN]
            X-Request-Deduplication:
              description: Whether request was deduplicated
              schema:
                type: boolean
            X-Performance-Score:
              description: Performance score (0-100)
              schema:
                type: integer
                minimum: 0
                maximum: 100
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/FlightSearchResponse'
                  - type: object
                    properties:
                      optimization_metadata:
                        type: object
                        properties:
                          circuit_breaker_status:
                            type: string
                            enum: [CLOSED, OPEN, HALF_OPEN]
                          request_deduplicated:
                            type: boolean
                          deduplication_savings_ms:
                            type: integer
                          performance_score:
                            type: integer
                            minimum: 0
                            maximum: 100
                          provider_response_time_ms:
                            type: integer
              examples:
                optimized_search:
                  summary: Optimized Search Response
                  value:
                    TUI: "ASYNC_SEARCH_12345_20240115_143022"
                    status: "success"
                    data_source: "provider_optimized"
                    cache_hit: false
                    response_time_ms: 850
                    optimization_metadata:
                      circuit_breaker_status: "CLOSED"
                      request_deduplicated: false
                      deduplication_savings_ms: 0
                      performance_score: 95
                      provider_response_time_ms: 800
                    Results:
                      - FlightId: "FL001_DEL_BOM_20240215"
                        FareId: "FARE_001_E_REG"
                        Airline: "AI"
                        FlightNumber: "AI131"
                        Origin: "DEL"
                        Destination: "BOM"
                        DepartureTime: "2024-02-15T06:00:00"
                        ArrivalTime: "2024-02-15T08:15:00"
                        Duration: "02:15"
                        Aircraft: "A320"
                        Price:
                          BaseFare: 8500
                          Taxes: 1500
                          TotalFare: 10000
                          Currency: "INR"
                        Availability: 9
                deduplicated_search:
                  summary: Deduplicated Search Response
                  value:
                    TUI: "ASYNC_SEARCH_12346_20240115_143025"
                    status: "success"
                    data_source: "deduplication_cache"
                    cache_hit: true
                    response_time_ms: 45
                    optimization_metadata:
                      circuit_breaker_status: "CLOSED"
                      request_deduplicated: true
                      deduplication_savings_ms: 1200
                      performance_score: 98
                      provider_response_time_ms: 0
                    Results:
                      - FlightId: "FL001_DEL_BOM_20240215"
                        FareId: "FARE_001_E_REG"
                        Airline: "AI"
                        FlightNumber: "AI131"
                        Origin: "DEL"
                        Destination: "BOM"
                        DepartureTime: "2024-02-15T06:00:00"
                        ArrivalTime: "2024-02-15T08:15:00"
                        Duration: "02:15"
                        Aircraft: "A320"
                        Price:
                          BaseFare: 8500
                          Taxes: 1500
                          TotalFare: 10000
                          Currency: "INR"
                        Availability: 9
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimitExceeded'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          description: Service temporarily unavailable (Circuit breaker open)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error:
                  code: "CIRCUIT_BREAKER_OPEN"
                  message: "Service temporarily unavailable due to circuit breaker protection"
                  details: "External provider is experiencing issues. Serving cached results when available."
                  retry_after: 60
                  circuit_breaker_status: "OPEN"
                  fallback_available: true

  /async/pricing:
    post:
      tags: [Async Flight Services]
      summary: Enhanced async flight pricing with optimization
      description: |
        Enhanced async flight pricing with circuit breaker protection and intelligent caching.

        **Optimization Features:**
        - Circuit breaker protection for external pricing APIs
        - Background refresh for volatile pricing data
        - Request deduplication for identical pricing requests
        - Intelligent retry logic with exponential backoff

        **Cache Strategy:**
        - 3-minute background refresh for volatile pricing
        - Dual cache key support (new + legacy)
        - Intelligent cache warming for popular flights
      operationId: asyncFlightPricing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FlightPricingRequest'
      responses:
        '200':
          description: Enhanced async flight pricing results
          headers:
            X-Circuit-Breaker-Status:
              description: Circuit breaker state
              schema:
                type: string
                enum: [CLOSED, OPEN, HALF_OPEN]
            X-Background-Refresh:
              description: Whether background refresh was triggered
              schema:
                type: boolean
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/FlightPricingResponse'
                  - type: object
                    properties:
                      optimization_metadata:
                        type: object
                        properties:
                          circuit_breaker_status:
                            type: string
                          background_refresh_triggered:
                            type: boolean
                          cache_freshness_score:
                            type: integer
                            minimum: 0
                            maximum: 100
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'

  # Cache Management APIs
  /cache/statistics:
    get:
      tags: [Cache Management]
      summary: Get comprehensive cache statistics
      description: |
        Retrieve detailed statistics for all cache layers with performance metrics.

        **Cache Layers Monitored:**
        - **L1 Memory Cache**: Ultra-fast in-memory storage
        - **L2 Redis Cache**: Distributed caching with persistence
        - **L3 Persistent Cache**: Long-term storage for stable data

        **Metrics Included:**
        - Hit rates and miss rates for each layer
        - Response times and performance scores
        - Memory usage and capacity utilization
        - Cache warming effectiveness
      operationId: getCacheStatistics
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: Comprehensive cache statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheStatisticsResponse'
              examples:
                cache_stats:
                  summary: Cache Statistics Example
                  value:
                    overall_performance:
                      hit_rate_percentage: 94.5
                      total_requests: 125000
                      total_hits: 118125
                      total_misses: 6875
                      avg_response_time_ms: 12.3
                      performance_score: 96
                    cache_layers:
                      L1_memory:
                        hit_rate_percentage: 85.2
                        total_requests: 125000
                        hits: 106500
                        misses: 18500
                        avg_response_time_ms: 0.8
                        memory_usage_mb: 256
                        capacity_mb: 512
                        utilization_percentage: 50.0
                      L2_redis:
                        hit_rate_percentage: 62.7
                        total_requests: 18500
                        hits: 11600
                        misses: 6900
                        avg_response_time_ms: 3.2
                        memory_usage_mb: 1024
                        capacity_mb: 2048
                        utilization_percentage: 50.0
                      L3_persistent:
                        hit_rate_percentage: 45.8
                        total_requests: 6900
                        hits: 3160
                        misses: 3740
                        avg_response_time_ms: 25.6
                        storage_usage_gb: 5.2
                        capacity_gb: 20.0
                        utilization_percentage: 26.0
                    cache_warming:
                      total_warming_requests: 1250
                      successful_warmings: 1198
                      failed_warmings: 52
                      warming_effectiveness: 95.8
                      popular_routes_cached: 45
                    performance_trends:
                      last_hour_hit_rate: 94.8
                      last_day_hit_rate: 93.2
                      trend_direction: "improving"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /cache/warm:
    post:
      tags: [Cache Warming]
      summary: Trigger intelligent cache warming
      description: |
        Trigger cache warming for popular routes and upcoming dates.

        **Warming Strategies:**
        - Popular route detection based on search patterns
        - Predictive warming for upcoming travel dates
        - Manual warming for specific routes
        - Priority-based warming queue management

        **Performance Impact:**
        - Reduces cache miss rates by 60-80%
        - Improves response times for popular routes
        - Optimizes resource utilization during peak hours
      operationId: triggerCacheWarming
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CacheWarmingRequest'
            examples:
              auto_warming:
                summary: Automatic Popular Routes Warming
                value:
                  routes: []
                  max_routes: 20
                  future_days: 3
                  priority: "high"
              specific_warming:
                summary: Specific Routes Warming
                value:
                  routes:
                    - from: "DEL"
                      to: "BOM"
                      dates: ["2024-02-15", "2024-02-16"]
                    - from: "BOM"
                      to: "DEL"
                      dates: ["2024-02-20", "2024-02-21"]
                  max_routes: 5
                  future_days: 1
                  priority: "medium"
      responses:
        '200':
          description: Cache warming initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheWarmingResponse'
              examples:
                warming_success:
                  summary: Successful Cache Warming
                  value:
                    status: "success"
                    message: "Cache warming initiated for 15 routes"
                    warming_details:
                      routes_queued: 15
                      estimated_completion_time: "5 minutes"
                      priority: "high"
                      warming_id: "WARM_12345_20240115"
                    performance_impact:
                      expected_hit_rate_improvement: "12-18%"
                      estimated_response_time_reduction: "200-400ms"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Performance Monitoring
  /performance/search:
    get:
      tags: [Performance Monitoring]
      summary: Get search service performance metrics
      description: |
        Retrieve detailed performance metrics for the search service.

        **Metrics Included:**
        - Response time statistics and percentiles
        - Request volume and throughput
        - Error rates and success rates
        - Cache performance and effectiveness
        - Circuit breaker status and health
      operationId: getSearchPerformance
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: Search service performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PerformanceMetricsResponse'
              examples:
                search_performance:
                  summary: Search Performance Metrics
                  value:
                    service: "search_service"
                    performance_stats:
                      total_requests: 45000
                      successful_requests: 44550
                      failed_requests: 450
                      success_rate: 99.0
                      error_rate: 1.0
                      avg_response_time_ms: 245
                      p50_response_time_ms: 180
                      p95_response_time_ms: 450
                      p99_response_time_ms: 850
                      requests_per_second: 125.5
                      cache_hit_rate: 87.2
                      circuit_breaker_status: "CLOSED"
                      last_updated: "2024-01-15T14:30:22Z"
                    trends:
                      response_time_trend: "improving"
                      throughput_trend: "stable"
                      error_rate_trend: "improving"
                    alerts:
                      active_alerts: 0
                      recent_alerts: []
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /performance/provider:
    get:
      tags: [Performance Monitoring]
      summary: Get provider client performance metrics
      description: |
        Retrieve performance metrics for external provider connections.

        **Metrics Included:**
        - Circuit breaker states and statistics
        - Provider response times and reliability
        - Retry attempts and success rates
        - Connection pool utilization
        - Failure patterns and recovery times
      operationId: getProviderPerformance
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: Provider client performance metrics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProviderPerformanceResponse'
              examples:
                provider_performance:
                  summary: Provider Performance Metrics
                  value:
                    service: "provider_client"
                    performance_stats:
                      total_requests: 25000
                      successful_requests: 24750
                      failed_requests: 250
                      success_rate: 99.0
                      avg_response_time_ms: 1250
                      circuit_breakers:
                        search_provider:
                          state: "CLOSED"
                          failure_rate: 0.8
                          success_threshold: 3
                          failure_threshold: 5
                          last_failure: null
                        pricing_provider:
                          state: "CLOSED"
                          failure_rate: 1.2
                          success_threshold: 3
                          failure_threshold: 5
                          last_failure: "2024-01-15T12:45:30Z"
                      retry_statistics:
                        total_retries: 1250
                        successful_retries: 1100
                        failed_retries: 150
                        avg_retry_delay_ms: 1500
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Advanced Monitoring - Real-time Dashboard
  /advanced/dashboard/realtime:
    get:
      tags: [Real-time Dashboard]
      summary: WebSocket endpoint for real-time dashboard
      description: |
        **WebSocket Connection for Real-time Monitoring**

        This endpoint provides WebSocket-based real-time streaming of system metrics and alerts.

        **Connection URL:** `ws://localhost:8000/apis/advanced/dashboard/realtime`

        **Supported Message Types:**

        **Client to Server:**
        - `subscribe`: Subscribe to specific metric types
        - `unsubscribe`: Unsubscribe from metric types
        - `ping`: Keep connection alive
        - `get_historical_data`: Request historical data

        **Server to Client:**
        - `connection_established`: Welcome message with available subscriptions
        - `real_time_metric`: Live metric updates
        - `performance_alert`: Real-time alert notifications
        - `system_status`: System status updates
        - `pong`: Response to ping

        **Subscription Types:**
        - `system_metrics`: Overall system performance
        - `performance_alerts`: Real-time alerts
        - `cache_statistics`: Cache performance metrics
        - `database_metrics`: Database performance
        - `booking_analytics`: Booking system metrics

        **Example Messages:**

        ```json
        // Subscribe to system metrics
        {
          "type": "subscribe",
          "subscription": "system_metrics"
        }

        // Real-time metric update
        {
          "type": "real_time_metric",
          "subscription": "system_metrics",
          "metric": {
            "metric_id": "response_time_1642251600",
            "timestamp": 1642251600.123,
            "service": "search_service",
            "metric_type": "response_time",
            "value": 245.5,
            "metadata": {
              "endpoint": "/flight-search",
              "cache_hit": false
            }
          }
        }

        // Performance alert
        {
          "type": "performance_alert",
          "alert": {
            "severity": "warning",
            "message": "Response time above threshold",
            "component": "search_service",
            "current_value": 1250,
            "threshold": 1000,
            "timestamp": 1642251600.123
          }
        }
        ```

        **Connection Management:**
        - Automatic ping/pong every 30 seconds
        - Client timeout after 5 minutes of inactivity
        - Graceful reconnection support
        - Multi-client subscription management
      operationId: realtimeDashboardWebSocket
      security:
        - ApiKeyAuth: []
      responses:
        '101':
          description: WebSocket connection established
        '400':
          description: Bad request - Invalid WebSocket upgrade
        '401':
          $ref: '#/components/responses/Unauthorized'
        '426':
          description: Upgrade required - WebSocket upgrade failed

  /advanced/dashboard/service-stats:
    get:
      tags: [Real-time Dashboard]
      summary: Get dashboard service statistics
      description: |
        Retrieve statistics for the real-time dashboard service.

        **Metrics Included:**
        - Active WebSocket connections
        - Metrics broadcast statistics
        - Alert delivery statistics
        - Service uptime and performance
      operationId: getDashboardServiceStats
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: Dashboard service statistics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardServiceStatsResponse'
              examples:
                dashboard_stats:
                  summary: Dashboard Service Statistics
                  value:
                    status: "success"
                    dashboard_stats:
                      total_connections: 125
                      active_connections: 8
                      metrics_broadcasted: 45000
                      alerts_sent: 125
                      uptime_seconds: 86400
                      metric_buffer_size: 500
                      alert_history_size: 50
                      background_tasks_running: 3
                      config:
                        ping_interval: 30
                        client_timeout: 300
                        metric_broadcast_interval: 5
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Automated Reporting
  /advanced/reporting/generate-report:
    post:
      tags: [Automated Reporting]
      summary: Generate automated report on demand
      description: |
        Generate comprehensive system reports with multiple format options.

        **Report Types:**
        - `daily_summary`: Daily performance overview
        - `weekly_performance`: Weekly trend analysis
        - `monthly_analytics`: Monthly business insights
        - `system_health`: Current system health status
        - `alert_digest`: Alert summary and analysis

        **Output Formats:**
        - `json`: Structured data format
        - `html`: Rich formatted report
        - `csv`: Tabular data export
        - `pdf`: Printable document format

        **Report Sections:**
        - Executive summary with key metrics
        - Performance trends and analysis
        - System health assessment
        - Optimization recommendations
        - Alert summaries and patterns
      operationId: generateReport
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportGenerationRequest'
            examples:
              daily_html_report:
                summary: Daily HTML Report
                value:
                  report_type: "daily_summary"
                  format: "html"
                  hours: 24
              weekly_json_report:
                summary: Weekly JSON Report
                value:
                  report_type: "weekly_performance"
                  format: "json"
                  hours: 168
      responses:
        '200':
          description: Report generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportGenerationResponse'
              examples:
                html_report:
                  summary: HTML Report Response
                  value:
                    status: "success"
                    report:
                      report_type: "daily_summary"
                      format: "html"
                      generated_at: "2024-01-15T14:30:22Z"
                      template: "Daily Performance Summary"
                      data: "<html><head><title>Daily Performance Summary</title>...</html>"
                json_report:
                  summary: JSON Report Response
                  value:
                    status: "success"
                    report:
                      report_type: "weekly_performance"
                      format: "json"
                      generated_at: "2024-01-15T14:30:22Z"
                      template: "Weekly Performance Analysis"
                      data:
                        summary:
                          period: "7 days"
                          system_health_score: 0.94
                          total_requests: 315000
                          avg_response_time: 245
                        performance_trends:
                          search_service:
                            avg_response_time: 245
                            success_rate: 99.2
                            cache_hit_rate: 87.5
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Predictive Analytics
  /advanced/analytics/forecast:
    post:
      tags: [Predictive Analytics]
      summary: Generate predictive forecast for metrics
      description: |
        Generate machine learning-based forecasts for system metrics.

        **Forecasting Capabilities:**
        - 24-hour performance forecasting with 85%+ accuracy
        - Trend prediction and pattern analysis
        - Confidence intervals and uncertainty quantification
        - Multiple metric support (response times, throughput, etc.)

        **Supported Metrics:**
        - `response_times`: API response time forecasting
        - `cache_hit_rates`: Cache performance prediction
        - `request_volumes`: Traffic volume forecasting
        - `error_rates`: Error rate trend prediction
        - `database_performance`: Database performance forecasting

        **ML Models Used:**
        - Polynomial regression for trend analysis
        - Statistical anomaly detection
        - Time series forecasting with confidence intervals
      operationId: generateForecast
      security:
        - ApiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForecastRequest'
            examples:
              response_time_forecast:
                summary: Response Time Forecast
                value:
                  metric_name: "response_times"
                  hours_ahead: 24
              cache_performance_forecast:
                summary: Cache Performance Forecast
                value:
                  metric_name: "cache_hit_rates"
                  hours_ahead: 12
      responses:
        '200':
          description: Forecast generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForecastResponse'
              examples:
                successful_forecast:
                  summary: Successful Forecast
                  value:
                    status: "success"
                    forecast:
                      metric: "response_times"
                      current_value: 245.5
                      predicted_values: [250, 255, 248, 252, 260, 258, 245, 240, 235, 238, 242, 248]
                      prediction_intervals:
                        - [230, 270]
                        - [235, 275]
                        - [228, 268]
                        - [232, 272]
                      confidence: 0.87
                      forecast_horizon: 12
                      model_accuracy: 0.89
                not_available_forecast:
                  summary: Forecast Not Available
                  value:
                    status: "not_available"
                    message: "No forecast available for metric: unknown_metric"
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: |
        API Key authentication for accessing flight booking services.

        **How to obtain:**
        1. Register for an account at https://portal.flightbooking.com
        2. Generate API keys in the developer dashboard
        3. Include the API key in the `X-API-Key` header

        **Rate Limits:**
        - Standard: 1000 requests/hour
        - Premium: 10000 requests/hour
        - Enterprise: Unlimited

    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT Bearer token authentication for enhanced security.

        **Token Structure:**
        - Header: Algorithm and token type
        - Payload: User claims and permissions
        - Signature: Cryptographic signature

        **Permissions:**
        - `flight:read`: Access to flight search and details
        - `booking:write`: Create and modify bookings
        - `admin:read`: Access to monitoring and analytics
        - `admin:write`: Administrative operations

  responses:
    BadRequest:
      description: Bad request - Invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            validation_error:
              summary: Validation Error
              value:
                error:
                  code: "VALIDATION_ERROR"
                  message: "Invalid input parameters"
                  details: "Required field 'From' is missing"
                  field_errors:
                    - field: "From"
                      message: "This field is required"
                      code: "REQUIRED"
            invalid_date:
              summary: Invalid Date Format
              value:
                error:
                  code: "INVALID_DATE_FORMAT"
                  message: "Invalid date format"
                  details: "Date must be in YYYY-MM-DD format"
                  field_errors:
                    - field: "OnwardDate"
                      message: "Invalid date format"
                      code: "INVALID_FORMAT"

    Unauthorized:
      description: Unauthorized - Invalid or missing authentication
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            missing_api_key:
              summary: Missing API Key
              value:
                error:
                  code: "MISSING_API_KEY"
                  message: "API key is required"
                  details: "Include X-API-Key header with valid API key"
            invalid_api_key:
              summary: Invalid API Key
              value:
                error:
                  code: "INVALID_API_KEY"
                  message: "Invalid API key"
                  details: "The provided API key is not valid or has been revoked"
            expired_token:
              summary: Expired JWT Token
              value:
                error:
                  code: "TOKEN_EXPIRED"
                  message: "JWT token has expired"
                  details: "Please refresh your token and try again"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            tui_not_found:
              summary: TUI Not Found
              value:
                error:
                  code: "TUI_NOT_FOUND"
                  message: "Transaction Unique Identifier not found"
                  details: "The specified TUI does not exist or has expired"
            booking_not_found:
              summary: Booking Not Found
              value:
                error:
                  code: "BOOKING_NOT_FOUND"
                  message: "Booking not found"
                  details: "No booking found with the specified ID"

    RateLimitExceeded:
      description: Rate limit exceeded
      headers:
        X-RateLimit-Limit:
          description: Request limit per time window
          schema:
            type: integer
        X-RateLimit-Remaining:
          description: Remaining requests in current window
          schema:
            type: integer
        X-RateLimit-Reset:
          description: Time when rate limit resets (Unix timestamp)
          schema:
            type: integer
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            rate_limit_exceeded:
              summary: Rate Limit Exceeded
              value:
                error:
                  code: "RATE_LIMIT_EXCEEDED"
                  message: "Rate limit exceeded"
                  details: "You have exceeded the allowed number of requests per hour"
                  retry_after: 3600

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            server_error:
              summary: Internal Server Error
              value:
                error:
                  code: "INTERNAL_SERVER_ERROR"
                  message: "An unexpected error occurred"
                  details: "Please try again later or contact support if the problem persists"
                  incident_id: "INC_20240115_143022_001"

    ServiceUnavailable:
      description: Service temporarily unavailable
      headers:
        Retry-After:
          description: Seconds to wait before retrying
          schema:
            type: integer
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          examples:
            maintenance:
              summary: Scheduled Maintenance
              value:
                error:
                  code: "SERVICE_MAINTENANCE"
                  message: "Service temporarily unavailable due to maintenance"
                  details: "Scheduled maintenance in progress. Service will resume shortly."
                  retry_after: 1800
            circuit_breaker:
              summary: Circuit Breaker Open
              value:
                error:
                  code: "CIRCUIT_BREAKER_OPEN"
                  message: "Service temporarily unavailable"
                  details: "External provider is experiencing issues. Cached results served when available."
                  retry_after: 60
                  fallback_available: true

  schemas:
    # Core Request/Response Schemas
    FlightSearchRequest:
      type: object
      required:
        - Trips
        - ADT
        - CHD
        - INF
        - Cabin
        - FareType
      properties:
        Trips:
          type: array
          minItems: 1
          maxItems: 6
          items:
            $ref: '#/components/schemas/TripSegment'
          description: Flight trip segments (1 for one-way, 2 for round-trip)
          example:
            - From: "DEL"
              To: "BOM"
              OnwardDate: "2024-02-15"
        ADT:
          type: integer
          minimum: 1
          maximum: 9
          description: Number of adult passengers (12+ years)
          example: 1
        CHD:
          type: integer
          minimum: 0
          maximum: 8
          description: Number of child passengers (2-11 years)
          example: 0
        INF:
          type: integer
          minimum: 0
          maximum: 8
          description: Number of infant passengers (0-2 years)
          example: 0
        Cabin:
          type: string
          enum: [E, B, F]
          description: |
            Cabin class preference:
            - E: Economy
            - B: Business
            - F: First
          example: "E"
        FareType:
          type: string
          enum: [REGULAR, STUDENT, SENIOR, MILITARY]
          description: Fare type for special discounts
          example: "REGULAR"
        DirectFlight:
          type: boolean
          description: Search for direct flights only
          default: false
        Airlines:
          type: array
          items:
            type: string
            pattern: '^[A-Z0-9]{2}$'
          description: Preferred airline codes (IATA 2-letter codes)
          example: ["AI", "6E", "SG"]
        MaxStops:
          type: integer
          minimum: 0
          maximum: 3
          description: Maximum number of stops allowed
          default: 3
        async:
          type: boolean
          description: Enable async processing with optimization features
          default: false

    TripSegment:
      type: object
      required:
        - From
        - To
        - OnwardDate
      properties:
        From:
          type: string
          pattern: '^[A-Z]{3}$'
          description: Origin airport code (IATA 3-letter code)
          example: "DEL"
        To:
          type: string
          pattern: '^[A-Z]{3}$'
          description: Destination airport code (IATA 3-letter code)
          example: "BOM"
        OnwardDate:
          type: string
          format: date
          description: Departure date in YYYY-MM-DD format
          example: "2024-02-15"

    FlightSearchResponse:
      type: object
      properties:
        TUI:
          type: string
          description: Transaction Unique Identifier for result retrieval
          example: "SEARCH_12345_20240115_143022"
        status:
          type: string
          enum: [success, error, partial]
          description: Response status
          example: "success"
        data_source:
          type: string
          enum: [cache, provider, L1_cache, L2_cache, L3_cache, provider_optimized, deduplication_cache]
          description: Source of the response data
          example: "cache"
        cache_hit:
          type: boolean
          description: Whether response was served from cache
          example: true
        response_time_ms:
          type: number
          description: Response time in milliseconds
          example: 45.2
        cached_at:
          type: string
          format: date-time
          description: When the data was cached (if from cache)
          example: "2024-01-15T14:25:30Z"
        cache_ttl_remaining:
          type: integer
          description: Remaining cache TTL in seconds
          example: 847
        Results:
          type: array
          items:
            $ref: '#/components/schemas/FlightResult'
          description: Array of flight search results
        SearchMetadata:
          $ref: '#/components/schemas/SearchMetadata'

    FlightResult:
      type: object
      properties:
        FlightId:
          type: string
          description: Unique flight identifier
          example: "FL001_DEL_BOM_20240215"
        FareId:
          type: string
          description: Unique fare identifier for pricing
          example: "FARE_001_E_REG"
        Airline:
          type: string
          description: Airline code (IATA 2-letter)
          example: "AI"
        FlightNumber:
          type: string
          description: Flight number
          example: "AI131"
        Origin:
          type: string
          description: Origin airport code
          example: "DEL"
        Destination:
          type: string
          description: Destination airport code
          example: "BOM"
        DepartureTime:
          type: string
          format: date-time
          description: Departure date and time
          example: "2024-02-15T06:00:00"
        ArrivalTime:
          type: string
          format: date-time
          description: Arrival date and time
          example: "2024-02-15T08:15:00"
        Duration:
          type: string
          description: Flight duration in HH:MM format
          example: "02:15"
        Aircraft:
          type: string
          description: Aircraft type
          example: "A320"
        Cabin:
          type: string
          description: Cabin class
          example: "Economy"
        FareType:
          type: string
          description: Fare type
          example: "Regular"
        Price:
          $ref: '#/components/schemas/PriceBreakdown'
        Availability:
          type: integer
          description: Number of seats available
          example: 9
        Refundable:
          type: boolean
          description: Whether the fare is refundable
          example: true
        Stops:
          type: array
          items:
            $ref: '#/components/schemas/StopInfo'
          description: Stop information for connecting flights
        BaggageAllowance:
          $ref: '#/components/schemas/BaggageInfo'

    PriceBreakdown:
      type: object
      properties:
        BaseFare:
          type: number
          description: Base fare amount
          example: 8500
        Taxes:
          type: number
          description: Taxes and fees
          example: 1500
        TotalFare:
          type: number
          description: Total fare amount
          example: 10000
        Currency:
          type: string
          description: Currency code (ISO 3-letter)
          example: "INR"
        FareBreakdown:
          type: array
          items:
            $ref: '#/components/schemas/FareComponent'
          description: Detailed fare breakdown

    FareComponent:
      type: object
      properties:
        Type:
          type: string
          description: Component type (BASE, TAX, FEE, SURCHARGE)
          example: "TAX"
        Code:
          type: string
          description: Component code
          example: "YQ"
        Description:
          type: string
          description: Component description
          example: "Fuel Surcharge"
        Amount:
          type: number
          description: Component amount
          example: 500
        Currency:
          type: string
          description: Currency code
          example: "INR"

    # Additional Core Schemas
    FlightSearchListRequest:
      type: object
      required:
        - TUI
      properties:
        TUI:
          type: string
          description: Transaction Unique Identifier from previous search
          example: "SEARCH_12345_20240115_143022"

    FlightPricingRequest:
      type: object
      required:
        - FareId
        - ADT
        - CHD
        - INF
      properties:
        FareId:
          type: string
          description: Fare identifier from search results
          example: "FARE_001_E_REG"
        ADT:
          type: integer
          minimum: 1
          maximum: 9
          description: Number of adult passengers
          example: 1
        CHD:
          type: integer
          minimum: 0
          maximum: 8
          description: Number of child passengers
          example: 0
        INF:
          type: integer
          minimum: 0
          maximum: 8
          description: Number of infant passengers
          example: 0

    FlightPricingResponse:
      type: object
      properties:
        TUI:
          type: string
          description: Transaction Unique Identifier
          example: "PRICING_12345_20240115_143500"
        status:
          type: string
          enum: [success, error]
          description: Response status
          example: "success"
        data_source:
          type: string
          description: Source of the response data
          example: "provider"
        response_time_ms:
          type: number
          description: Response time in milliseconds
          example: 1250
        FlightDetails:
          $ref: '#/components/schemas/DetailedFlightInfo'

    FlightPricingListRequest:
      type: object
      required:
        - TUI
      properties:
        TUI:
          type: string
          description: Transaction Unique Identifier from previous pricing request
          example: "PRICING_12345_20240115_143500"

    DetailedFlightInfo:
      type: object
      properties:
        FlightId:
          type: string
          example: "FL001_DEL_BOM_20240215"
        FareId:
          type: string
          example: "FARE_001_E_REG"
        Airline:
          type: string
          example: "AI"
        FlightNumber:
          type: string
          example: "AI131"
        Route:
          $ref: '#/components/schemas/RouteInfo'
        Aircraft:
          type: string
          example: "A320"
        PriceBreakdown:
          $ref: '#/components/schemas/DetailedPriceBreakdown'
        FareRules:
          $ref: '#/components/schemas/FareRules'
        BaggageAllowance:
          $ref: '#/components/schemas/BaggageInfo'
        SeatAvailability:
          $ref: '#/components/schemas/SeatAvailability'

    RouteInfo:
      type: object
      properties:
        Origin:
          type: string
          example: "DEL"
        Destination:
          type: string
          example: "BOM"
        DepartureTime:
          type: string
          format: date-time
          example: "2024-02-15T06:00:00"
        ArrivalTime:
          type: string
          format: date-time
          example: "2024-02-15T08:15:00"
        Duration:
          type: string
          example: "02:15"

    DetailedPriceBreakdown:
      type: object
      properties:
        Adult:
          $ref: '#/components/schemas/PassengerPrice'
        Child:
          $ref: '#/components/schemas/PassengerPrice'
        Infant:
          $ref: '#/components/schemas/PassengerPrice'
        Currency:
          type: string
          example: "INR"

    PassengerPrice:
      type: object
      properties:
        BaseFare:
          type: number
          example: 8500
        Taxes:
          type: number
          example: 1500
        TotalFare:
          type: number
          example: 10000

    FareRules:
      type: object
      properties:
        Cancellation:
          $ref: '#/components/schemas/CancellationRule'
        Changes:
          $ref: '#/components/schemas/ChangeRule'
        Refund:
          $ref: '#/components/schemas/RefundRule'

    CancellationRule:
      type: object
      properties:
        Allowed:
          type: boolean
          example: true
        Penalty:
          type: number
          example: 2000
        TimeLimit:
          type: string
          example: "24 hours before departure"

    ChangeRule:
      type: object
      properties:
        Allowed:
          type: boolean
          example: true
        Penalty:
          type: number
          example: 1500
        TimeLimit:
          type: string
          example: "2 hours before departure"

    RefundRule:
      type: object
      properties:
        Allowed:
          type: boolean
          example: true
        Conditions:
          type: string
          example: "As per airline policy"

    BaggageInfo:
      type: object
      properties:
        CheckedBaggage:
          type: string
          example: "15 KG"
        HandBaggage:
          type: string
          example: "7 KG"
        PersonalItem:
          type: string
          example: "2 KG"

    SeatAvailability:
      type: object
      properties:
        Economy:
          type: integer
          example: 9
        Business:
          type: integer
          example: 0
        First:
          type: integer
          example: 0

    StopInfo:
      type: object
      properties:
        Airport:
          type: string
          example: "BLR"
        ArrivalTime:
          type: string
          format: date-time
          example: "2024-02-15T10:30:00"
        DepartureTime:
          type: string
          format: date-time
          example: "2024-02-15T12:00:00"
        Duration:
          type: string
          example: "01:30"
        Terminal:
          type: string
          example: "T2"

    SearchMetadata:
      type: object
      properties:
        SearchId:
          type: string
          example: "SEARCH_12345"
        SearchTime:
          type: string
          format: date-time
          example: "2024-01-15T14:30:22Z"
        ResultCount:
          type: integer
          example: 25
        SearchParameters:
          $ref: '#/components/schemas/FlightSearchRequest'
        ProviderInfo:
          type: object
          properties:
            ProviderId:
              type: string
              example: "PROVIDER_001"
            ResponseTime:
              type: number
              example: 1250.5

    # Cache Management Schemas
    CacheStatisticsResponse:
      type: object
      properties:
        overall_performance:
          $ref: '#/components/schemas/OverallCachePerformance'
        cache_layers:
          $ref: '#/components/schemas/CacheLayersStats'
        cache_warming:
          $ref: '#/components/schemas/CacheWarmingStats'
        performance_trends:
          $ref: '#/components/schemas/CachePerformanceTrends'

    OverallCachePerformance:
      type: object
      properties:
        hit_rate_percentage:
          type: number
          example: 94.5
        total_requests:
          type: integer
          example: 125000
        total_hits:
          type: integer
          example: 118125
        total_misses:
          type: integer
          example: 6875
        avg_response_time_ms:
          type: number
          example: 12.3
        performance_score:
          type: integer
          example: 96

    CacheLayersStats:
      type: object
      properties:
        L1_memory:
          $ref: '#/components/schemas/CacheLayerStats'
        L2_redis:
          $ref: '#/components/schemas/CacheLayerStats'
        L3_persistent:
          $ref: '#/components/schemas/CacheLayerStats'

    CacheLayerStats:
      type: object
      properties:
        hit_rate_percentage:
          type: number
          example: 85.2
        total_requests:
          type: integer
          example: 125000
        hits:
          type: integer
          example: 106500
        misses:
          type: integer
          example: 18500
        avg_response_time_ms:
          type: number
          example: 0.8
        memory_usage_mb:
          type: number
          example: 256
        capacity_mb:
          type: number
          example: 512
        utilization_percentage:
          type: number
          example: 50.0
        storage_usage_gb:
          type: number
          example: 5.2
        capacity_gb:
          type: number
          example: 20.0

    CacheWarmingStats:
      type: object
      properties:
        total_warming_requests:
          type: integer
          example: 1250
        successful_warmings:
          type: integer
          example: 1198
        failed_warmings:
          type: integer
          example: 52
        warming_effectiveness:
          type: number
          example: 95.8
        popular_routes_cached:
          type: integer
          example: 45

    CachePerformanceTrends:
      type: object
      properties:
        last_hour_hit_rate:
          type: number
          example: 94.8
        last_day_hit_rate:
          type: number
          example: 93.2
        trend_direction:
          type: string
          enum: [improving, declining, stable]
          example: "improving"

    CacheWarmingRequest:
      type: object
      properties:
        routes:
          type: array
          items:
            $ref: '#/components/schemas/WarmingRoute'
          description: Specific routes to warm (empty for auto-detection)
        max_routes:
          type: integer
          minimum: 1
          maximum: 100
          description: Maximum number of routes to warm
          example: 20
        future_days:
          type: integer
          minimum: 1
          maximum: 30
          description: Number of future days to warm
          example: 3
        priority:
          type: string
          enum: [low, medium, high, critical]
          description: Warming priority level
          example: "high"

    WarmingRoute:
      type: object
      required:
        - from
        - to
        - dates
      properties:
        from:
          type: string
          pattern: '^[A-Z]{3}$'
          example: "DEL"
        to:
          type: string
          pattern: '^[A-Z]{3}$'
          example: "BOM"
        dates:
          type: array
          items:
            type: string
            format: date
          example: ["2024-02-15", "2024-02-16"]

    CacheWarmingResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, error, partial]
          example: "success"
        message:
          type: string
          example: "Cache warming initiated for 15 routes"
        warming_details:
          $ref: '#/components/schemas/WarmingDetails'
        performance_impact:
          $ref: '#/components/schemas/WarmingPerformanceImpact'

    WarmingDetails:
      type: object
      properties:
        routes_queued:
          type: integer
          example: 15
        estimated_completion_time:
          type: string
          example: "5 minutes"
        priority:
          type: string
          example: "high"
        warming_id:
          type: string
          example: "WARM_12345_20240115"

    WarmingPerformanceImpact:
      type: object
      properties:
        expected_hit_rate_improvement:
          type: string
          example: "12-18%"
        estimated_response_time_reduction:
          type: string
          example: "200-400ms"

    # Performance Monitoring Schemas
    PerformanceMetricsResponse:
      type: object
      properties:
        service:
          type: string
          example: "search_service"
        performance_stats:
          $ref: '#/components/schemas/ServicePerformanceStats'
        trends:
          $ref: '#/components/schemas/PerformanceTrends'
        alerts:
          $ref: '#/components/schemas/AlertSummary'

    ServicePerformanceStats:
      type: object
      properties:
        total_requests:
          type: integer
          example: 45000
        successful_requests:
          type: integer
          example: 44550
        failed_requests:
          type: integer
          example: 450
        success_rate:
          type: number
          example: 99.0
        error_rate:
          type: number
          example: 1.0
        avg_response_time_ms:
          type: number
          example: 245
        p50_response_time_ms:
          type: number
          example: 180
        p95_response_time_ms:
          type: number
          example: 450
        p99_response_time_ms:
          type: number
          example: 850
        requests_per_second:
          type: number
          example: 125.5
        cache_hit_rate:
          type: number
          example: 87.2
        circuit_breaker_status:
          type: string
          enum: [CLOSED, OPEN, HALF_OPEN]
          example: "CLOSED"
        last_updated:
          type: string
          format: date-time
          example: "2024-01-15T14:30:22Z"

    PerformanceTrends:
      type: object
      properties:
        response_time_trend:
          type: string
          enum: [improving, declining, stable]
          example: "improving"
        throughput_trend:
          type: string
          enum: [improving, declining, stable]
          example: "stable"
        error_rate_trend:
          type: string
          enum: [improving, declining, stable]
          example: "improving"

    AlertSummary:
      type: object
      properties:
        active_alerts:
          type: integer
          example: 0
        recent_alerts:
          type: array
          items:
            $ref: '#/components/schemas/AlertInfo'

    AlertInfo:
      type: object
      properties:
        alert_id:
          type: string
          example: "ALERT_12345"
        severity:
          type: string
          enum: [info, warning, critical]
          example: "warning"
        message:
          type: string
          example: "Response time above threshold"
        component:
          type: string
          example: "search_service"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T14:30:22Z"
        resolved:
          type: boolean
          example: false

    ProviderPerformanceResponse:
      type: object
      properties:
        service:
          type: string
          example: "provider_client"
        performance_stats:
          $ref: '#/components/schemas/ProviderPerformanceStats'

    ProviderPerformanceStats:
      type: object
      properties:
        total_requests:
          type: integer
          example: 25000
        successful_requests:
          type: integer
          example: 24750
        failed_requests:
          type: integer
          example: 250
        success_rate:
          type: number
          example: 99.0
        avg_response_time_ms:
          type: number
          example: 1250
        circuit_breakers:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/CircuitBreakerStats'
        retry_statistics:
          $ref: '#/components/schemas/RetryStats'

    CircuitBreakerStats:
      type: object
      properties:
        state:
          type: string
          enum: [CLOSED, OPEN, HALF_OPEN]
          example: "CLOSED"
        failure_rate:
          type: number
          example: 0.8
        success_threshold:
          type: integer
          example: 3
        failure_threshold:
          type: integer
          example: 5
        last_failure:
          type: string
          format: date-time
          nullable: true
          example: "2024-01-15T12:45:30Z"

    RetryStats:
      type: object
      properties:
        total_retries:
          type: integer
          example: 1250
        successful_retries:
          type: integer
          example: 1100
        failed_retries:
          type: integer
          example: 150
        avg_retry_delay_ms:
          type: number
          example: 1500

    # Advanced Monitoring Schemas
    DashboardServiceStatsResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        dashboard_stats:
          $ref: '#/components/schemas/DashboardServiceStats'

    DashboardServiceStats:
      type: object
      properties:
        total_connections:
          type: integer
          example: 125
        active_connections:
          type: integer
          example: 8
        metrics_broadcasted:
          type: integer
          example: 45000
        alerts_sent:
          type: integer
          example: 125
        uptime_seconds:
          type: integer
          example: 86400
        metric_buffer_size:
          type: integer
          example: 500
        alert_history_size:
          type: integer
          example: 50
        background_tasks_running:
          type: integer
          example: 3
        config:
          $ref: '#/components/schemas/DashboardConfig'

    DashboardConfig:
      type: object
      properties:
        ping_interval:
          type: integer
          example: 30
        client_timeout:
          type: integer
          example: 300
        metric_broadcast_interval:
          type: integer
          example: 5

    ReportGenerationRequest:
      type: object
      required:
        - report_type
        - format
      properties:
        report_type:
          type: string
          enum: [daily_summary, weekly_performance, monthly_analytics, system_health, alert_digest]
          description: Type of report to generate
          example: "daily_summary"
        format:
          type: string
          enum: [json, html, csv, pdf]
          description: Output format for the report
          example: "html"
        hours:
          type: integer
          minimum: 1
          maximum: 8760
          description: Number of hours to include in the report
          default: 24
          example: 24

    ReportGenerationResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, error]
          example: "success"
        report:
          $ref: '#/components/schemas/GeneratedReport'

    GeneratedReport:
      type: object
      properties:
        report_type:
          type: string
          example: "daily_summary"
        format:
          type: string
          example: "html"
        generated_at:
          type: string
          format: date-time
          example: "2024-01-15T14:30:22Z"
        template:
          type: string
          example: "Daily Performance Summary"
        data:
          oneOf:
            - type: string
              description: HTML, CSV, or PDF content
            - type: object
              description: JSON structured data

    ForecastRequest:
      type: object
      required:
        - metric_name
      properties:
        metric_name:
          type: string
          enum: [response_times, cache_hit_rates, request_volumes, error_rates, database_performance]
          description: Metric to forecast
          example: "response_times"
        hours_ahead:
          type: integer
          minimum: 1
          maximum: 168
          description: Number of hours to forecast ahead
          default: 24
          example: 24

    ForecastResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success, not_available, error]
          example: "success"
        forecast:
          $ref: '#/components/schemas/ForecastData'
        message:
          type: string
          description: Additional information or error message
          example: "Forecast generated successfully"

    ForecastData:
      type: object
      properties:
        metric:
          type: string
          example: "response_times"
        current_value:
          type: number
          example: 245.5
        predicted_values:
          type: array
          items:
            type: number
          example: [250, 255, 248, 252, 260, 258, 245, 240, 235, 238, 242, 248]
        prediction_intervals:
          type: array
          items:
            type: array
            items:
              type: number
            minItems: 2
            maxItems: 2
          example: [[230, 270], [235, 275], [228, 268], [232, 272]]
        confidence:
          type: number
          minimum: 0
          maximum: 1
          example: 0.87
        forecast_horizon:
          type: integer
          example: 12
        model_accuracy:
          type: number
          minimum: 0
          maximum: 1
          example: 0.89

    # Error Response Schema
    ErrorResponse:
      type: object
      properties:
        error:
          $ref: '#/components/schemas/ErrorDetails'

    ErrorDetails:
      type: object
      properties:
        code:
          type: string
          description: Error code for programmatic handling
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid input parameters"
        details:
          type: string
          description: Additional error details
          example: "Required field 'From' is missing"
        field_errors:
          type: array
          items:
            $ref: '#/components/schemas/FieldError'
          description: Field-specific validation errors
        retry_after:
          type: integer
          description: Seconds to wait before retrying (for rate limiting)
          example: 3600
        incident_id:
          type: string
          description: Unique incident identifier for support
          example: "INC_20240115_143022_001"
        circuit_breaker_status:
          type: string
          enum: [CLOSED, OPEN, HALF_OPEN]
          description: Circuit breaker state (for service unavailable errors)
          example: "OPEN"
        fallback_available:
          type: boolean
          description: Whether fallback data is available
          example: true

    FieldError:
      type: object
      properties:
        field:
          type: string
          description: Field name that caused the error
          example: "From"
        message:
          type: string
          description: Field-specific error message
          example: "This field is required"
        code:
          type: string
          description: Field error code
          example: "REQUIRED"
        value:
          description: Invalid value that was provided
          example: null