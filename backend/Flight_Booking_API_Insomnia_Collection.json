{"_type": "export", "__export_format": 4, "__export_date": "2024-01-15T14:30:22.000Z", "__export_source": "insomnia.desktop.app:v2023.5.8", "resources": [{"_id": "req_group_core", "_type": "request_group", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "🔧 Core Flight Services", "description": "Essential flight booking operations including search, pricing, and result retrieval", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1705327822000}, {"_id": "req_group_async", "_type": "request_group", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "⚡ Enhanced Async Operations", "description": "Enhanced async operations with circuit breaker protection and performance monitoring", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1705327821000}, {"_id": "req_group_cache", "_type": "request_group", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "🗄️ Cache Management (Admin)", "description": "Multi-layer cache management with L1 Memory + L2 Redis + L3 Persistent caching", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1705327820000}, {"_id": "req_group_database", "_type": "request_group", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "🏢 Database & Analytics (Admin)", "description": "Advanced database operations with connection pooling and query optimization", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1705327819000}, {"_id": "req_group_monitoring", "_type": "request_group", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "📊 Advanced Monitoring (Admin)", "description": "Advanced monitoring with real-time dashboards, automated reporting, and predictive analytics", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1705327818000}, {"_id": "wrk_flight_booking", "_type": "workspace", "parentId": null, "modified": 1705327822000, "created": 1705327822000, "name": "Flight Booking Optimization System API", "description": "Complete API collection for testing the Flight Booking System with 115+ endpoints across all optimization phases", "scope": "collection"}, {"_id": "env_base", "_type": "environment", "parentId": "wrk_flight_booking", "modified": 1705327822000, "created": 1705327822000, "name": "Base Environment", "data": {"base_url": "http://localhost:8000/apis", "api_key": "test-api-key-12345", "saved_tui": "", "saved_fare_id": "", "future_date": "2024-02-15"}, "dataPropertyOrder": {"&": ["base_url", "api_key", "saved_tui", "saved_fare_id", "future_date"]}, "color": null, "isPrivate": false, "metaSortKey": 1705327822000}, {"_id": "req_flight_search", "_type": "request", "parentId": "req_group_core", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/flight-search", "name": "Flight Search", "description": "Search for flights with comprehensive parameters", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{ _.future_date }}\"\n    }\n  ],\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"FareType\": \"REGULAR\",\n  \"DirectFlight\": false,\n  \"Airlines\": [\"AI\", \"6E\"],\n  \"MaxStops\": 2\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327822000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_flight_search_list", "_type": "request", "parentId": "req_group_core", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/flight-search-list", "name": "Flight Search List (Retrieve Results)", "description": "Retrieve cached flight search results using TUI", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"TUI\": \"{{ _.saved_tui }}\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327821000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_flight_pricing", "_type": "request", "parentId": "req_group_core", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/flight-pricing", "name": "Flight Pricing", "description": "Get detailed pricing for a specific fare", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"FareId\": \"{{ _.saved_fare_id }}\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327820000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_round_trip_search", "_type": "request", "parentId": "req_group_core", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/flight-search", "name": "Round Trip Search", "description": "Search for round-trip flights with multiple passengers", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{ _.future_date }}\"\n    },\n    {\n      \"From\": \"BOM\",\n      \"To\": \"DEL\",\n      \"OnwardDate\": \"2024-02-20\"\n    }\n  ],\n  \"ADT\": 2,\n  \"CHD\": 1,\n  \"INF\": 0,\n  \"Cabin\": \"B\",\n  \"FareType\": \"REGULAR\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327819000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_async_search", "_type": "request", "parentId": "req_group_async", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/async/search", "name": "Async Flight Search", "description": "Enhanced async flight search with optimization features", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"Trips\": [\n    {\n      \"From\": \"DEL\",\n      \"To\": \"BOM\",\n      \"OnwardDate\": \"{{ _.future_date }}\"\n    }\n  ],\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"Cabin\": \"E\",\n  \"FareType\": \"REGULAR\",\n  \"async\": true\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327822000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_async_pricing", "_type": "request", "parentId": "req_group_async", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/async/pricing", "name": "Async Flight Pricing", "description": "Enhanced async pricing with circuit breaker protection", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"FareId\": \"{{ _.saved_fare_id }}\",\n  \"ADT\": 1,\n  \"CHD\": 0,\n  \"INF\": 0,\n  \"async\": true\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327821000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_performance_search", "_type": "request", "parentId": "req_group_async", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/performance/search", "name": "Search Service Performance", "description": "Get search service performance metrics and statistics", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327820000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_cache_statistics", "_type": "request", "parentId": "req_group_cache", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/cache/statistics", "name": "<PERSON><PERSON>", "description": "Get comprehensive cache performance statistics across all layers", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327822000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_cache_warming", "_type": "request", "parentId": "req_group_cache", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/cache/warm", "name": "<PERSON><PERSON>", "description": "Trigger intelligent cache warming for popular routes", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"routes\": [\n    {\n      \"from\": \"DEL\",\n      \"to\": \"BOM\",\n      \"dates\": [\"{{ _.future_date }}\", \"2024-02-16\"]\n    },\n    {\n      \"from\": \"BOM\",\n      \"to\": \"DEL\",\n      \"dates\": [\"2024-02-20\", \"2024-02-21\"]\n    }\n  ],\n  \"max_routes\": 20,\n  \"future_days\": 3,\n  \"priority\": \"high\"\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327821000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_database_stats", "_type": "request", "parentId": "req_group_database", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/admin/database/stats", "name": "Database Statistics", "description": "Get database connection pool and query performance statistics", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327822000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_optimized_booking", "_type": "request", "parentId": "req_group_database", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/admin/booking/create-optimized", "name": "Optimized Booking Creation", "description": "Create booking with optimized database operations and batch processing", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"passenger_details\": {\n    \"adults\": [\n      {\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"<PERSON><PERSON>\",\n        \"date_of_birth\": \"1990-01-15\",\n        \"passport_number\": \"A12345678\",\n        \"nationality\": \"IN\",\n        \"gender\": \"<PERSON>\"\n      }\n    ],\n    \"children\": [],\n    \"infants\": []\n  },\n  \"flight_details\": {\n    \"fare_id\": \"{{ _.saved_fare_id }}\",\n    \"total_amount\": 10000,\n    \"currency\": \"INR\",\n    \"booking_class\": \"E\"\n  },\n  \"contact_details\": {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+91-9876543210\",\n    \"address\": {\n      \"street\": \"123 Main St\",\n      \"city\": \"Delhi\",\n      \"state\": \"Delhi\",\n      \"country\": \"IN\",\n      \"postal_code\": \"110001\"\n    }\n  },\n  \"payment_details\": {\n    \"payment_method\": \"credit_card\",\n    \"card_type\": \"visa\"\n  }\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327821000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_system_status", "_type": "request", "parentId": "req_group_monitoring", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/advanced/monitoring/comprehensive-status", "name": "Comprehensive System Status", "description": "Get comprehensive system health and performance status", "method": "GET", "body": {}, "parameters": [], "headers": [{"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327822000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}, {"_id": "req_predictive_analytics", "_type": "request", "parentId": "req_group_monitoring", "modified": 1705327822000, "created": 1705327822000, "url": "{{ _.base_url }}/advanced/analytics/forecast", "name": "Predictive Analytics - Response Times", "description": "Generate ML-based forecast for response times", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"metric_name\": \"response_times\",\n  \"hours_ahead\": 24\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-API-Key", "value": "{{ _.api_key }}"}], "authentication": {}, "metaSortKey": -1705327821000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global"}]}