-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `trips`
--

DROP TABLE IF EXISTS `trips`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `trips` (
  `id` int NOT NULL AUTO_INCREMENT,
  `flight_booking_id` varchar(36) NOT NULL,
  `Provider` varchar(50) NOT NULL,
  `Stops` varchar(10) NOT NULL,
  `Offer` varchar(255) DEFAULT NULL,
  `OrderID` int NOT NULL,
  `GrossFare` float NOT NULL,
  `NetFare` float NOT NULL,
  `Promo` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_trips_flight_booking_id` (`flight_booking_id`),
  KEY `ix_trips_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `trips`
--

LOCK TABLES `trips` WRITE;
/*!40000 ALTER TABLE `trips` DISABLE KEYS */;
INSERT INTO `trips` VALUES (8,'10','AMN','0','',0,125162,125162,NULL),(9,'10','AMN','0','',0,0,0,NULL),(10,'11','AMN','0','',0,125162,125162,NULL),(11,'11','AMN','0','',0,0,0,NULL),(12,'12','AMN','0','',0,125162,125162,NULL),(13,'13','AMN','0','',0,125162,125162,NULL),(14,'12','AMN','0','',0,0,0,NULL),(15,'13','AMN','0','',0,0,0,NULL),(16,'14','AMN','0','',0,125162,125162,NULL),(17,'14','AMN','0','',0,0,0,NULL),(18,'15','AMN','0','',0,125162,125162,NULL),(19,'15','AMN','0','',0,0,0,NULL),(20,'16','AMN','0','',0,125162,125162,NULL),(21,'16','AMN','0','',0,0,0,NULL),(22,'17','AMN','0','',0,125162,125162,NULL),(23,'17','AMN','0','',0,0,0,NULL),(24,'18','AMN','0','',0,125162,125162,NULL),(25,'18','AMN','0','',0,0,0,NULL),(26,'19','AMN','0','',0,125162,125162,NULL),(27,'19','AMN','0','',0,0,0,NULL),(28,'20','AMN','0','',0,125162,125162,NULL),(29,'20','AMN','0','',0,0,0,NULL),(30,'21','AMN','0','',0,125162,125162,NULL),(31,'21','AMN','0','',0,0,0,NULL),(32,'22','AMN','0','',0,125162,125162,NULL),(33,'22','AMN','0','',0,0,0,NULL),(34,'23','AMN','0','',0,125162,125162,NULL),(35,'23','AMN','0','',0,0,0,NULL),(36,'24','AMN','0','',0,125162,125162,NULL),(37,'24','AMN','0','',0,0,0,NULL),(38,'25','AMN','0','',0,125162,125162,NULL),(39,'25','AMN','0','',0,0,0,NULL),(40,'26','AMN','0','',0,125162,125162,NULL),(41,'26','AMN','0','',0,0,0,NULL),(42,'27','AMN','0','',0,125162,125162,NULL),(43,'27','AMN','0','',0,0,0,NULL),(44,'28','AMN','0','',0,125162,125162,NULL),(45,'28','AMN','0','',0,0,0,NULL),(46,'29','AMN','0','',0,125162,125162,NULL),(47,'29','AMN','0','',0,0,0,NULL),(48,'30','AMN','0','',0,125162,125162,NULL),(49,'30','AMN','0','',0,0,0,NULL),(50,'31','AMN','0','',0,125162,125162,NULL),(51,'31','AMN','0','',0,0,0,NULL),(52,'33','1G','1','DefaultOffer',0,10003,10003,NULL),(53,'34','TJ','0','DefaultOffer',0,3792,3792,NULL),(54,'35','TJ','1','DefaultOffer',0,45281,45281,NULL),(55,'35','TJ','1','DefaultOffer',1,45281,45281,NULL),(56,'36','TJ','0','DefaultOffer',0,13039,13039,NULL),(57,'37','TJ','0','DefaultOffer',0,14479,14479,NULL),(58,'38','TJ','0','DefaultOffer',0,3355.5,3355.5,NULL),(59,'39','6E','0','DefaultOffer',0,3792,3792,NULL),(60,'40','6E','0','DefaultOffer',0,3792,3792,NULL),(61,'41','6E','0','DefaultOffer',0,3792,3792,NULL),(62,'42','6E','0','DefaultOffer',0,3792,3792,NULL);
/*!40000 ALTER TABLE `trips` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:43
