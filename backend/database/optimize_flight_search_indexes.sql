-- Flight Search Database Optimization Script
-- This script creates optimized indexes for faster flight search performance

-- =====================================================
-- AIRPORT SEARCH OPTIMIZATION (tdb database)
-- =====================================================

USE tdb;

-- Drop existing indexes if they exist (for clean setup)
-- Note: Using individual DROP statements for MySQL 8.0 compatibility
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_schema = DATABASE() AND table_name = 'airports' AND index_name = 'idx_airports_search_composite') > 0,
    'DROP INDEX idx_airports_search_composite ON airports',
    'SELECT 1'));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_schema = DATABASE() AND table_name = 'airports' AND index_name = 'idx_airports_code_name') > 0,
    'DROP INDEX idx_airports_code_name ON airports',
    'SELECT 1'));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_schema = DATABASE() AND table_name = 'airports' AND index_name = 'idx_airports_city_search') > 0,
    'DROP INDEX idx_airports_city_search ON airports',
    'SELECT 1'));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create composite index for multi-field airport searches
CREATE INDEX idx_airports_search_composite
ON airports (country, city_name, name, code);

-- Create optimized index for code and name searches (most common)
CREATE INDEX idx_airports_code_name
ON airports (code, name);

-- Create index for city-based searches
CREATE INDEX idx_airports_city_search
ON airports (city_name, country);

-- Create full-text search index for fuzzy matching
-- Note: This requires MyISAM engine or MySQL 5.6+ with InnoDB
ALTER TABLE airports ADD FULLTEXT(name, city_name, code);

-- =====================================================
-- FLIGHT DATA OPTIMIZATION (tdb_booking database)
-- =====================================================

USE tdb_booking;

-- Drop existing flight indexes if they exist
DROP INDEX IF EXISTS idx_flights_route_date ON flights;
DROP INDEX IF EXISTS idx_flights_airline_cabin ON flights;
DROP INDEX IF EXISTS idx_flights_performance ON flights;
DROP INDEX IF EXISTS idx_flights_departure_time ON flights;
DROP INDEX IF EXISTS idx_flights_search_optimized ON flights;

-- Create primary route and date index (most important for search)
CREATE INDEX idx_flights_route_date
ON flights (DepartureCode, ArrivalCode, DepartureTime);

-- Create index for airline and cabin filtering
CREATE INDEX idx_flights_airline_cabin
ON flights (Airline, Cabin, Refundable);

-- Create comprehensive performance index for complex queries
CREATE INDEX idx_flights_performance
ON flights (DepartureCode, ArrivalCode, DepartureTime, Cabin, Airline);

-- Create index for departure time range queries
CREATE INDEX idx_flights_departure_time
ON flights (DepartureTime, DepartureCode, ArrivalCode);

-- Create optimized search index covering most common search patterns
CREATE INDEX idx_flights_search_optimized
ON flights (DepartureCode, ArrivalCode, DATE(DepartureTime), Cabin, Refundable);

-- =====================================================
-- PASSENGER SEARCH OPTIMIZATION
-- =====================================================

USE tdb_booking;

-- Create full-text search index for passenger names
CREATE FULLTEXT INDEX idx_travellers_fulltext_names
ON travellers (FName, LName);

-- Create composite index for passenger search by name and passport
CREATE INDEX idx_travellers_search_composite
ON travellers (FName, LName, PassportNo, PTC);

-- Create index for passport number searches (exact match)
CREATE INDEX idx_travellers_passport_search
ON travellers (PassportNo, PLI);

-- Create index for passenger type and nationality filtering
CREATE INDEX idx_travellers_filters
ON travellers (PTC, Nationality, Gender);

-- =====================================================
-- BOOKING REFERENCE OPTIMIZATION
-- =====================================================

-- Create optimized index for booking reference searches
CREATE INDEX idx_master_bookings_reference
ON master_bookings (booking_reference, booking_status, created_at);

-- Create index for user booking searches
CREATE INDEX idx_master_bookings_user_search
ON master_bookings (user_id, created_at DESC, booking_status);

-- Create composite index for booking search with filters
CREATE INDEX idx_master_bookings_search_filters
ON master_bookings (booking_reference, user_id, booking_status, total_amount);

-- Create index for booking date range searches
CREATE INDEX idx_master_bookings_date_range
ON master_bookings (created_at, booking_status, user_id);

-- =====================================================
-- FLIGHT BOOKING SEARCH OPTIMIZATION
-- =====================================================

-- Create index for flight booking searches by master booking
CREATE INDEX idx_flight_bookings_master_search
ON flight_bookings (master_booking_id, pnr, booking_status);

-- Create index for PNR searches
CREATE INDEX idx_flight_bookings_pnr_search
ON flight_bookings (pnr, airline_pnr, booking_status);

-- Create composite index for flight booking filters
CREATE INDEX idx_flight_bookings_filters
ON flight_bookings (booking_status, total_fare, created_at);

-- =====================================================
-- CONTACT INFO SEARCH OPTIMIZATION
-- =====================================================

-- Create index for contact information searches
CREATE INDEX idx_contact_info_search
ON contact_info (email, phone, master_booking_id);

-- Create index for email-based searches
CREATE INDEX idx_contact_info_email
ON contact_info (email, phone);

-- =====================================================
-- BOOKING DATA OPTIMIZATION
-- =====================================================

-- Optimize flight bookings table
DROP INDEX IF EXISTS idx_flight_bookings_user ON flight_bookings;
DROP INDEX IF EXISTS idx_flight_bookings_status ON flight_bookings;
DROP INDEX IF EXISTS idx_flight_bookings_date ON flight_bookings;

CREATE INDEX idx_flight_bookings_user
ON flight_bookings (user_id, booking_status, created_at);

CREATE INDEX idx_flight_bookings_status
ON flight_bookings (booking_status, created_at);

CREATE INDEX idx_flight_bookings_date
ON flight_bookings (created_at, booking_status);

-- =====================================================
-- SEGMENTS OPTIMIZATION
-- =====================================================

-- Optimize segments table for flight details
DROP INDEX IF EXISTS idx_segments_flight ON segments;
DROP INDEX IF EXISTS idx_segments_route ON segments;

CREATE INDEX idx_segments_flight
ON segments (flight_id, segment_order);

CREATE INDEX idx_segments_route
ON segments (departure_airport, arrival_airport, departure_time);

-- =====================================================
-- PERFORMANCE ANALYSIS QUERIES
-- =====================================================

-- Query to analyze index usage
-- Run this after implementing indexes to verify effectiveness

-- Check index cardinality
SELECT
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('airports', 'flights', 'flight_bookings', 'segments')
ORDER BY
    TABLE_NAME, INDEX_NAME;

-- =====================================================
-- OPTIMIZED QUERY EXAMPLES
-- =====================================================

-- Optimized airport search query
-- EXPLAIN SELECT * FROM airports
-- WHERE
--     (code LIKE 'DEL%' OR name LIKE '%Delhi%' OR city_name LIKE '%Delhi%')
--     AND country = 'India'
-- ORDER BY
--     CASE
--         WHEN code = 'DEL' THEN 1
--         WHEN code LIKE 'DEL%' THEN 2
--         WHEN name LIKE 'Delhi%' THEN 3
--         ELSE 4
--     END,
--     name
-- LIMIT 20;

-- Optimized flight search query
-- EXPLAIN SELECT
--     f.*,
--     s.*
-- FROM flights f
-- LEFT JOIN segments s ON f.segment_id = s.id
-- WHERE
--     f.DepartureCode = 'DEL'
--     AND f.ArrivalCode = 'BOM'
--     AND DATE(f.DepartureTime) = '2024-12-01'
--     AND f.Cabin = 'E'
-- ORDER BY
--     f.DepartureTime,
--     CAST(SUBSTRING_INDEX(f.Duration, 'h', 1) AS UNSIGNED) * 60 +
--     CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(f.Duration, 'h ', -1), 'm', 1) AS UNSIGNED)
-- LIMIT 50;

-- =====================================================
-- MAINTENANCE QUERIES
-- =====================================================

-- Query to monitor index effectiveness
-- Run periodically to ensure indexes are being used

-- Check slow queries
-- SELECT
--     query_time,
--     lock_time,
--     rows_sent,
--     rows_examined,
--     sql_text
-- FROM mysql.slow_log
-- WHERE sql_text LIKE '%airports%' OR sql_text LIKE '%flights%'
-- ORDER BY query_time DESC
-- LIMIT 10;

-- Analyze table statistics
ANALYZE TABLE airports;
ANALYZE TABLE flights;
ANALYZE TABLE flight_bookings;
ANALYZE TABLE segments;

-- =====================================================
-- PARTITIONING SETUP (Optional for large datasets)
-- =====================================================

-- Partition flights table by year for better performance
-- Note: This requires careful planning and should be done during maintenance window

-- ALTER TABLE flights PARTITION BY RANGE (YEAR(DepartureTime)) (
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p2026 VALUES LESS THAN (2027),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify all indexes are created successfully
SELECT
    CONCAT(TABLE_NAME, '.', INDEX_NAME) as 'Table.Index',
    INDEX_TYPE,
    CARDINALITY,
    CASE
        WHEN NON_UNIQUE = 0 THEN 'UNIQUE'
        ELSE 'NON-UNIQUE'
    END as 'Type'
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('airports', 'flights', 'flight_bookings', 'segments')
    AND INDEX_NAME != 'PRIMARY'
ORDER BY
    TABLE_NAME, INDEX_NAME;

-- Check table sizes and index sizes
SELECT
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Total Size (MB)',
    ROUND((DATA_LENGTH / 1024 / 1024), 2) AS 'Data Size (MB)',
    ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS 'Index Size (MB)',
    TABLE_ROWS
FROM
    INFORMATION_SCHEMA.TABLES
WHERE
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME IN ('airports', 'flights', 'flight_bookings', 'segments')
ORDER BY
    (DATA_LENGTH + INDEX_LENGTH) DESC;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Flight search database optimization completed successfully!' as 'Status';
SELECT 'Run EXPLAIN on your queries to verify index usage' as 'Next Step';
SELECT 'Monitor query performance and adjust indexes as needed' as 'Maintenance';
