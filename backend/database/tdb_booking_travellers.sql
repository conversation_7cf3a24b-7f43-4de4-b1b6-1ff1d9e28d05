-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `travellers`
--

DROP TABLE IF EXISTS `travellers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `travellers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `flight_booking_id` varchar(36) NOT NULL,
  `PaxID` int NOT NULL,
  `Title` varchar(10) NOT NULL,
  `FName` varchar(100) NOT NULL,
  `LName` varchar(100) NOT NULL,
  `Age` int NOT NULL,
  `DOB` datetime NOT NULL,
  `Gender` varchar(1) NOT NULL,
  `PTC` varchar(10) NOT NULL,
  `Nationality` varchar(50) NOT NULL,
  `PassportNo` varchar(20) NOT NULL,
  `PLI` varchar(50) NOT NULL,
  `PDOE` datetime NOT NULL,
  `VisaType` varchar(50) DEFAULT NULL,
  `DocType` varchar(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_travellers_PassportNo` (`PassportNo`),
  KEY `ix_travellers_flight_booking_id` (`flight_booking_id`),
  KEY `ix_travellers_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `travellers`
--

LOCK TABLES `travellers` WRITE;
/*!40000 ALTER TABLE `travellers` DISABLE KEYS */;
INSERT INTO `travellers` VALUES (4,'10',0,'Mr','SAJAD','SAJID',35,'1989-01-03 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(5,'10',0,'Mstr','SAJID','SAJID',11,'2013-10-08 00:00:00','M','CHD','INDIA','A1234567','INDIA','2030-11-14 00:00:00','STUDENT VISA','P'),(6,'10',0,'Mstr','SAYEED','SHAD',0,'2024-07-18 00:00:00','M','INF','INDIA','P1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(7,'11',0,'Mr','SAJAD','SAJID',35,'1989-01-03 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(8,'11',0,'Mstr','SAJID','SAJID',11,'2013-10-08 00:00:00','M','CHD','INDIA','A1234567','INDIA','2030-11-14 00:00:00','STUDENT VISA','P'),(9,'11',0,'Mstr','SAYEED','SHAD',0,'2024-07-18 00:00:00','M','INF','INDIA','P1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(10,'12',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(11,'13',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(12,'14',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(13,'15',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(14,'16',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(15,'17',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(16,'18',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(17,'19',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(18,'20',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(19,'21',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(20,'22',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(21,'23',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(22,'24',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(23,'25',0,'Mr','SAJAD','SAJID',35,'1989-01-03 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(24,'25',0,'Mstr','SAJID','SAJID',11,'2013-10-08 00:00:00','M','CHD','INDIA','A1234567','INDIA','2030-11-14 00:00:00','STUDENT VISA','P'),(25,'25',0,'Mstr','SAYEED','SHAD',0,'2024-07-18 00:00:00','M','INF','INDIA','P1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(26,'26',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(27,'27',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(28,'28',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(29,'29',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(30,'30',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(31,'31',0,'Mr','VISHNU','K',35,'1998-10-30 00:00:00','M','ADT','INDIA','a1234567','INDIA','2030-07-11 00:00:00','IMMIGRANT VISA','P'),(32,'33',1,'Mr','John','Doe',35,'1989-05-15 00:00:00','M','adult','US','A12345678','New York','2030-12-31 00:00:00',NULL,'Passport'),(33,'34',1,'Mrs','shebin','vallooran',25,'1989-05-15 00:00:00','F','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(34,'35',1,'Mrs','shebin','vallooran',25,'1989-05-15 00:00:00','F','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(35,'36',1,'Mr','shebin','vallooran',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(36,'37',1,'Mr','shebin','vallooran',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(37,'37',2,'Mrs','test','test',25,'1989-05-15 00:00:00','F','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(38,'37',3,'Mr','test2','test2',25,'1989-05-15 00:00:00','M','child','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(39,'37',4,'Mrs','test3','test3',25,'1989-05-15 00:00:00','F','infant','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(40,'38',1,'Mr','ALI','K',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(41,'39',1,'Mr','ALI','K',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(42,'40',1,'Mr','ALI','K',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(43,'41',1,'Mr','ALI','K',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other'),(44,'42',1,'Mr','ALI','K',25,'1989-05-15 00:00:00','M','adult','US','A123456789','New York','2030-12-31 00:00:00',NULL,'Other');
/*!40000 ALTER TABLE `travellers` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:44
