-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_auth
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `phone_country_code` varchar(5) DEFAULT NULL,
  `otp` varchar(6) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `role` enum('ADMIN','CUSTOMER') DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_users_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'<EMAIL>','hello','9633200983','91',NULL,1,'2024-12-16 13:04:04','2024-12-18 17:48:23',NULL,NULL),(2,'<EMAIL>','Vishnu','7012884807','91',NULL,1,'2024-12-18 17:58:58','2024-12-19 13:26:01',NULL,'ADMIN'),(3,'<EMAIL>','hello','7012884807','91',NULL,1,'2025-05-23 16:42:49','2025-05-23 16:44:49',NULL,'CUSTOMER'),(4,'<EMAIL>','hello','7012884807','91',NULL,0,'2025-05-23 20:21:28','2025-05-26 12:12:40',NULL,'CUSTOMER'),(5,'<EMAIL>','hello','7012884807','91','849269',0,'2025-05-23 20:21:39','2025-05-23 20:21:39',NULL,'CUSTOMER'),(6,'<EMAIL>','hello','7012884807','91','784756',0,'2025-05-23 20:22:04','2025-05-23 20:22:04',NULL,'CUSTOMER'),(7,'<EMAIL>','hello','7012884807','91','101374',0,'2025-05-23 20:23:41','2025-05-23 20:23:41',NULL,'CUSTOMER'),(8,'<EMAIL>','hello','7012884807','91','837602',0,'2025-05-23 20:23:59','2025-05-23 20:23:59',NULL,'CUSTOMER'),(9,'<EMAIL>','hello','7012884807','91','123456',0,'2025-05-23 20:31:23','2025-05-23 20:31:23',NULL,'CUSTOMER'),(10,'<EMAIL>','Test User','1234567890','+1','123456',0,'2025-05-26 16:25:42','2025-05-26 16:25:42',NULL,'CUSTOMER'),(11,'<EMAIL>','Mohammed Shabin V','9876543210','91','123456',1,'2025-05-26 17:46:56','2025-05-26 17:47:16',NULL,'ADMIN'),(12,'<EMAIL>','Test 6','9876543210','91','123456',1,'2025-05-26 17:50:04','2025-05-26 17:50:09',NULL,'ADMIN'),(13,'<EMAIL>','Mohammed Shabin V','9876543210','91','123456',0,'2025-05-26 18:46:16','2025-05-26 18:46:16',NULL,'ADMIN'),(14,'<EMAIL>','Mohammed Shabin V','9876543210','91','123456',1,'2025-05-26 18:46:47','2025-05-26 18:46:52',NULL,'ADMIN'),(15,'<EMAIL>','Mohammed Shabin V','9876543210','91','123456',1,'2025-05-26 19:22:32','2025-05-26 19:22:40',NULL,'ADMIN'),(16,'<EMAIL>','shabin','9876543210','91','123456',1,'2025-05-26 19:41:09','2025-05-26 19:41:14',NULL,'ADMIN'),(17,'<EMAIL>','ALI SAJIL K','09321116965','91','123456',1,'2025-05-26 19:48:53','2025-05-26 19:49:01',NULL,'ADMIN');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:44
