-- Create all databases
CREATE DATABASE IF NOT EXISTS tdb_auth;
CREATE DATABASE IF NOT EXISTS tdb_flight;
CREATE DATABASE IF NOT EXISTS tdb_booking;

-- Set up tdb_auth schema
USE tdb_auth;
-- Contents of tdb_auth_alembic_version.sql
CREATE TABLE IF NOT EXISTS `alembic_version` (
  `version_num` varchar(32) NOT NULL
);
-- Contents of tdb_auth_users.sql
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `hashed_password` varchar(255) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
);

-- Set up tdb_booking schema
USE tdb_booking;
-- Contents of tdb_booking_alembic_version.sql
CREATE TABLE IF NOT EXISTS `alembic_version` (
  `version_num` varchar(32) NOT NULL
);
-- Contents of tdb_booking_contact_info.sql
CREATE TABLE IF NOT EXISTS `contact_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) NOT NULL,
  PRIMARY KEY (`id`)
);
-- Contents of other booking tables would follow here...
