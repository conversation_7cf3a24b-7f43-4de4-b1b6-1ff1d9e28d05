-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `master_bookings`
--

DROP TABLE IF EXISTS `master_bookings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `master_bookings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `booking_reference` varchar(100) NOT NULL,
  `service_type` enum('FLIGHT','HOTEL','RAIL') NOT NULL,
  `status` enum('PENDING','CONFIRMED','CANCELED') NOT NULL,
  `payment_status` varchar(10) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_master_bookings_booking_reference` (`booking_reference`),
  KEY `ix_master_bookings_id` (`id`),
  KEY `ix_master_bookings_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `master_bookings`
--

LOCK TABLES `master_bookings` WRITE;
/*!40000 ALTER TABLE `master_bookings` DISABLE KEYS */;
INSERT INTO `master_bookings` VALUES (10,'ce2fdce5-3d39-4c29-a06f-23f1f4e63b95','FLIGHT','PENDING','unpaid','2024-12-12 15:44:27','2024-12-12 15:44:27',NULL,NULL),(11,'4a4eb68a-d291-40ce-bb96-eef819c51deb','FLIGHT','PENDING','unpaid','2024-12-12 15:45:57','2024-12-12 15:45:57',NULL,NULL),(12,'3861560f-3c19-4074-9e44-2c857f47ff1e','FLIGHT','PENDING','unpaid','2024-12-12 15:55:02','2024-12-12 15:55:02',NULL,NULL),(13,'160ccdb4-40e0-4af5-b79e-42edb83688eb','FLIGHT','PENDING','unpaid','2024-12-12 15:55:02','2024-12-12 15:55:02',NULL,NULL),(14,'b5cc0de7-f5ef-492b-9677-e6b5ae889d50','FLIGHT','PENDING','unpaid','2024-12-12 15:57:16','2024-12-12 15:57:16',NULL,NULL),(15,'fb895298-609a-4409-a124-11efad4314f4','FLIGHT','PENDING','unpaid','2024-12-12 16:00:49','2024-12-12 16:00:49',NULL,NULL),(16,'1797a319-b0a2-4edb-b20a-84ac8981318b','FLIGHT','PENDING','unpaid','2024-12-12 16:03:34','2024-12-12 16:03:34',NULL,NULL),(17,'33766adb-4196-4933-a2ba-1b41f6d95218','FLIGHT','PENDING','unpaid','2024-12-13 14:46:43','2024-12-13 14:46:43',NULL,NULL),(18,'d9d46fec-c92d-4d4b-89f6-0e10f227ca2a','FLIGHT','PENDING','unpaid','2024-12-16 13:02:31','2024-12-16 13:02:31',NULL,NULL),(19,'e730dd75-b524-459d-9319-4165c676e59e','FLIGHT','PENDING','unpaid','2024-12-16 13:12:20','2024-12-16 13:12:20',NULL,NULL),(20,'b9998d15-5745-4bb7-a424-83e62a618bed','FLIGHT','PENDING','unpaid','2024-12-16 13:50:41','2024-12-16 13:50:41',NULL,NULL),(21,'c533b092-84eb-4e03-9800-6aac7636a33b','FLIGHT','PENDING','unpaid','2024-12-17 10:59:52','2024-12-17 10:59:52',NULL,NULL),(22,'f8f25b29-b2f9-4890-813a-c858ed6b619d','FLIGHT','PENDING','unpaid','2024-12-17 11:00:28','2024-12-17 11:00:28',NULL,NULL),(23,'001f38c3-a834-4b54-b2db-aab798814cb9','FLIGHT','PENDING','unpaid','2024-12-17 11:03:36','2024-12-17 11:03:36',NULL,NULL),(24,'6ef7457e-945e-4a47-b29e-93b72e115954','FLIGHT','PENDING','unpaid','2024-12-17 11:04:26','2024-12-17 11:04:26',NULL,NULL),(25,'abc27b45-ff94-4886-a64d-1e047f531520','FLIGHT','PENDING','unpaid','2024-12-17 12:21:30','2024-12-17 12:21:30',NULL,NULL),(26,'119de19a-1770-4b5a-ba0f-8f290472a49a','FLIGHT','PENDING','unpaid','2024-12-17 12:25:00','2024-12-17 12:25:00',NULL,NULL),(27,'21242e97-1de2-4806-84f8-fab3abd3c65f','FLIGHT','PENDING','unpaid','2024-12-17 13:37:23','2024-12-17 13:37:23',NULL,1),(28,'0618032e-e7b5-451d-b9b4-404fe3216303','FLIGHT','PENDING','unpaid','2024-12-17 14:01:36','2024-12-17 14:01:36',NULL,1),(29,'8f51a2b0-029b-4279-b34b-0e5549dd8375','FLIGHT','PENDING','unpaid','2024-12-17 14:01:39','2024-12-17 14:01:39',NULL,1),(30,'bfdcffc8-9452-4a40-9b9c-efbe941b818b','FLIGHT','PENDING','unpaid','2024-12-18 17:11:45','2024-12-18 17:11:45',NULL,1),(31,'cb3d7110-29c3-4e88-a2fe-8b91b1ebe87f','FLIGHT','PENDING','unpaid','2024-12-18 17:12:26','2024-12-18 17:12:26',NULL,1),(32,'0dbbbc8f-7596-4dc1-8825-0758d34a7bbb','FLIGHT','PENDING','unpaid','2025-05-23 16:53:19','2025-05-23 16:53:19',NULL,3),(33,'bf87f017-000b-4245-aca7-198fb0b1feda','FLIGHT','PENDING','unpaid','2025-05-23 16:54:02','2025-05-23 16:54:02',NULL,3),(34,'fbaa0506-dc54-4790-ac9f-6091550f861b','FLIGHT','PENDING','unpaid','2025-05-26 18:47:21','2025-05-26 18:47:21',NULL,14),(35,'0b31bbab-2df6-4f50-9f00-5722a32e12d0','FLIGHT','PENDING','unpaid','2025-05-26 18:50:35','2025-05-26 18:50:35',NULL,14),(36,'0ee906d1-daf1-4908-bd3c-bcf407e7665a','FLIGHT','PENDING','unpaid','2025-05-26 19:14:20','2025-05-26 19:14:20',NULL,14),(37,'bd6a0d64-d656-4746-899a-bbd2eb468a24','FLIGHT','PENDING','unpaid','2025-05-26 19:42:21','2025-05-26 19:42:21',NULL,16),(38,'8f35b4fa-1e6e-4a55-88a3-bffe80ac21ec','FLIGHT','PENDING','unpaid','2025-05-26 19:49:46','2025-05-26 19:49:46',NULL,17),(39,'22f5aa7e-6961-455a-9653-344d1a780351','FLIGHT','PENDING','unpaid','2025-05-26 20:15:52','2025-05-26 20:15:52',NULL,17),(40,'ba9a70d5-1788-4594-9a55-c385f4169fdc','FLIGHT','PENDING','unpaid','2025-05-26 20:16:16','2025-05-26 20:16:16',NULL,17),(41,'fd3c92f5-30fb-4c8f-8523-f7e2f6378baf','FLIGHT','PENDING','unpaid','2025-05-26 20:16:47','2025-05-26 20:16:47',NULL,17),(42,'23eb786a-19d8-4ee8-805a-ae9af2c5b8e4','FLIGHT','PENDING','unpaid','2025-05-26 20:19:33','2025-05-26 20:19:33',NULL,17);
/*!40000 ALTER TABLE `master_bookings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:43
