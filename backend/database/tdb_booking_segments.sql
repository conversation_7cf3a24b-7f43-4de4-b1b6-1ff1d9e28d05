-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `segments`
--

DROP TABLE IF EXISTS `segments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `segments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `trip_id` varchar(36) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_segments_id` (`id`),
  KEY `ix_segments_trip_id` (`trip_id`)
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `segments`
--

LOCK TABLES `segments` WRITE;
/*!40000 ALTER TABLE `segments` DISABLE KEYS */;
INSERT INTO `segments` VALUES (18,'10'),(19,'10'),(20,'11'),(21,'11'),(22,'12'),(24,'12'),(23,'13'),(25,'13'),(26,'14'),(28,'14'),(27,'15'),(29,'15'),(30,'16'),(31,'16'),(32,'17'),(33,'17'),(34,'18'),(35,'18'),(36,'19'),(37,'19'),(38,'20'),(39,'20'),(40,'21'),(41,'21'),(42,'22'),(43,'22'),(44,'23'),(45,'23'),(46,'24'),(47,'24'),(48,'25'),(49,'25'),(50,'26'),(51,'26'),(52,'27'),(53,'27'),(54,'28'),(55,'28'),(56,'29'),(57,'29'),(58,'30'),(59,'30'),(60,'31'),(61,'31'),(62,'32'),(63,'32'),(64,'33'),(65,'33'),(66,'34'),(67,'34'),(68,'35'),(69,'35'),(70,'36'),(71,'36'),(72,'37'),(73,'37'),(74,'38'),(75,'38'),(76,'39'),(77,'39'),(78,'40'),(79,'40'),(80,'41'),(81,'41'),(82,'42'),(83,'42'),(84,'43'),(85,'43'),(86,'44'),(87,'44'),(88,'45'),(89,'45'),(90,'46'),(91,'46'),(92,'47'),(93,'47'),(94,'48'),(95,'48'),(96,'49'),(97,'49'),(98,'50'),(99,'50'),(100,'51'),(101,'51'),(102,'52'),(103,'52'),(104,'53'),(105,'54'),(106,'54'),(107,'55'),(108,'55'),(109,'56'),(110,'57'),(111,'58'),(14,'8'),(15,'8'),(16,'9'),(17,'9');
/*!40000 ALTER TABLE `segments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:44
