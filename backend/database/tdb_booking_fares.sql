-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `fares`
--

DROP TABLE IF EXISTS `fares`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fares` (
  `id` int NOT NULL AUTO_INCREMENT,
  `segment_id` varchar(36) NOT NULL,
  `Fare` float DEFAULT NULL,
  `YQ` float DEFAULT NULL,
  `PSF` float DEFAULT NULL,
  `Tax` float DEFAULT NULL,
  `GrossFare` float NOT NULL,
  `NetFare` float NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_fares_id` (`id`),
  KEY `ix_fares_segment_id` (`segment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fares`
--

LOCK TABLES `fares` WRITE;
/*!40000 ALTER TABLE `fares` DISABLE KEYS */;
INSERT INTO `fares` VALUES (14,'14',NULL,NULL,NULL,NULL,125162,125162),(15,'15',NULL,NULL,NULL,NULL,0,0),(16,'16',NULL,NULL,NULL,NULL,0,0),(17,'17',NULL,NULL,NULL,NULL,0,0),(18,'18',NULL,NULL,NULL,NULL,125162,125162),(19,'19',NULL,NULL,NULL,NULL,0,0),(20,'20',NULL,NULL,NULL,NULL,0,0),(21,'21',NULL,NULL,NULL,NULL,0,0),(22,'22',NULL,NULL,NULL,NULL,125162,125162),(23,'23',NULL,NULL,NULL,NULL,125162,125162),(24,'24',NULL,NULL,NULL,NULL,0,0),(25,'25',NULL,NULL,NULL,NULL,0,0),(26,'26',NULL,NULL,NULL,NULL,0,0),(27,'27',NULL,NULL,NULL,NULL,0,0),(28,'28',NULL,NULL,NULL,NULL,0,0),(29,'29',NULL,NULL,NULL,NULL,0,0),(30,'30',NULL,NULL,NULL,NULL,125162,125162),(31,'31',NULL,NULL,NULL,NULL,0,0),(32,'32',NULL,NULL,NULL,NULL,0,0),(33,'33',NULL,NULL,NULL,NULL,0,0),(34,'34',NULL,NULL,NULL,NULL,125162,125162),(35,'35',NULL,NULL,NULL,NULL,0,0),(36,'36',NULL,NULL,NULL,NULL,0,0),(37,'37',NULL,NULL,NULL,NULL,0,0),(38,'38',NULL,NULL,NULL,NULL,125162,125162),(39,'39',NULL,NULL,NULL,NULL,0,0),(40,'40',NULL,NULL,NULL,NULL,0,0),(41,'41',NULL,NULL,NULL,NULL,0,0);
/*!40000 ALTER TABLE `fares` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:44
