-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: tdb_booking
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `contact_info`
--

DROP TABLE IF EXISTS `contact_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `contact_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `flight_booking_id` varchar(36) NOT NULL,
  `Title` varchar(10) DEFAULT NULL,
  `FName` varchar(100) NOT NULL,
  `LName` varchar(100) NOT NULL,
  `Mobile` varchar(15) NOT NULL,
  `Phone` varchar(15) DEFAULT NULL,
  `Email` varchar(255) NOT NULL,
  `Address` varchar(255) DEFAULT NULL,
  `CountryCode` varchar(5) NOT NULL,
  `MobileCountryCode` varchar(5) DEFAULT NULL,
  `State` varchar(100) DEFAULT NULL,
  `City` varchar(100) DEFAULT NULL,
  `PIN` varchar(10) DEFAULT NULL,
  `GSTAddress` varchar(255) DEFAULT NULL,
  `GSTCompanyName` varchar(255) DEFAULT NULL,
  `GSTTIN` varchar(50) DEFAULT NULL,
  `UpdateProfile` tinyint(1) DEFAULT NULL,
  `IsGuest` tinyint(1) DEFAULT NULL,
  `SaveGST` tinyint(1) DEFAULT NULL,
  `Language` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ix_contact_info_Email` (`Email`),
  KEY `ix_contact_info_Mobile` (`Mobile`),
  KEY `ix_contact_info_flight_booking_id` (`flight_booking_id`),
  KEY `ix_contact_info_id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `contact_info`
--

LOCK TABLES `contact_info` WRITE;
/*!40000 ALTER TABLE `contact_info` DISABLE KEYS */;
INSERT INTO `contact_info` VALUES (1,'10','','','','8129884096','','<EMAIL>','AKBAR ONLINE BOOKING COMPANY PVT LTD','IN','+91','Maharashtra','Mumbai','400003','','','',0,1,0,''),(2,'11','','','','8129884096','','<EMAIL>','AKBAR ONLINE BOOKING COMPANY PVT LTD','IN','+91','Maharashtra','Mumbai','400003','','','',0,1,0,''),(3,'12','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(4,'13','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(5,'14','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(6,'15','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(7,'16','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(8,'17','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(9,'18','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(10,'19','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(11,'20','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(12,'21','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(13,'22','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(14,'23','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(15,'24','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(16,'25','','','','8129884096','','<EMAIL>','AKBAR ONLINE BOOKING COMPANY PVT LTD','IN','+91','Maharashtra','Mumbai','400003','','','',0,1,0,''),(17,'26','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(18,'27','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(19,'28','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(20,'29','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(21,'30','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(22,'31','MR','VISHNU','K','12345678','1234567','<EMAIL>','KOCHI','IN','+91','KERALA','KOCHI','673638','AB1234343443','Ecogo Software Solutions Private Limited','G3223',0,1,0,''),(23,'32','Mr','John','Hnnoo','234567890',NULL,'<EMAIL>','123 Elm Street, New York, NY, USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(24,'33','Mr','John','Hnnoo','234567890',NULL,'<EMAIL>','123 Elm Street, New York, NY, USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(25,'34','Mrs','shebin','vallooran','9961370269',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(26,'35','Mrs','shebin','vallooran','9961370269',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(27,'36','Mr','shebin','vallooran','9961370269',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(28,'37','Mr','shebin','vallooran','9876543210',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(29,'38','Mr','ALI','K','09321116965',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(30,'39','Mr','ALI','K','09321116965',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(31,'40','Mr','ALI','K','09321116965',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(32,'41','Mr','ALI','K','09321116965',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL),(33,'42','Mr','ALI','K','09321116965',NULL,'<EMAIL>','123 Elm street, New York , NY ,USA','+91','+91','NY','New York',NULL,NULL,NULL,NULL,0,1,0,NULL);
/*!40000 ALTER TABLE `contact_info` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-26 21:01:43
