-- Simplified Flight Search Database Optimization Script
-- Compatible with MySQL 8.0 in Docker

-- =====================================================
-- AIRPORT SEARCH OPTIMIZATION (tdb database)
-- =====================================================

USE tdb;

-- Create composite index for multi-field airport searches
CREATE INDEX idx_airports_search_composite
ON airports (country, city_name, name, code);

-- Create optimized index for code and name searches (most common)
CREATE INDEX idx_airports_code_name
ON airports (code, name);

-- Create index for city-based searches
CREATE INDEX idx_airports_city_search
ON airports (city_name, country);

-- =====================================================
-- FLIGHT DATA OPTIMIZATION (tdb_booking database)
-- =====================================================

USE tdb_booking;

-- Create primary route and date index (most important for search)
CREATE INDEX idx_flights_route_date
ON flights (DepartureCode, ArrivalCode, DepartureTime);

-- Create index for airline and cabin filtering
CREATE INDEX idx_flights_airline_cabin
ON flights (Airline, Cabin, Refundable);

-- Create comprehensive performance index for complex queries
CREATE INDEX idx_flights_performance
ON flights (DepartureCode, ArrivalCode, DepartureTime, Cabin, Airline);

-- Create index for departure time range queries
CREATE INDEX idx_flights_departure_time
ON flights (DepartureTime, DepartureCode, ArrivalCode);

-- =====================================================
-- BOOKING DATA OPTIMIZATION
-- =====================================================

-- Optimize flight bookings table based on actual structure
CREATE INDEX idx_flight_bookings_master
ON flight_bookings (master_booking_id, created_at);

CREATE INDEX idx_flight_bookings_tui
ON flight_bookings (TUI, created_at);

CREATE INDEX idx_flight_bookings_transaction
ON flight_bookings (TransactionID, created_at);

CREATE INDEX idx_flight_bookings_date
ON flight_bookings (created_at, Code);

-- =====================================================
-- SEGMENTS OPTIMIZATION
-- =====================================================

-- Optimize segments table for flight details based on actual structure
CREATE INDEX idx_segments_trip
ON segments (trip_id);

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Show created indexes for airports
SELECT 'AIRPORTS INDEXES:' as 'TABLE';
SELECT
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = 'tdb'
    AND TABLE_NAME = 'airports'
    AND INDEX_NAME != 'PRIMARY'
ORDER BY
    INDEX_NAME, SEQ_IN_INDEX;

-- Show created indexes for flights
SELECT 'FLIGHTS INDEXES:' as 'TABLE';
SELECT
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY
FROM
    INFORMATION_SCHEMA.STATISTICS
WHERE
    TABLE_SCHEMA = 'tdb_booking'
    AND TABLE_NAME = 'flights'
    AND INDEX_NAME != 'PRIMARY'
ORDER BY
    INDEX_NAME, SEQ_IN_INDEX;

SELECT 'Database optimization completed successfully!' as 'Status';
