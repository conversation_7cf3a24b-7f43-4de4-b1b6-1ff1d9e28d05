#!/usr/bin/env python3
"""
Step-by-step test to identify and fix integration issues.
"""

import sys
import os

def test_basic_imports():
    """Test basic imports first."""
    print("🔍 Testing Basic Imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported")
    except Exception as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        from pydantic import BaseModel
        print("✅ Pydantic imported")
    except Exception as e:
        print(f"❌ Pydantic import failed: {e}")
        return False
    
    try:
        import mysql.connector
        print("✅ MySQL connector imported")
    except Exception as e:
        print(f"❌ MySQL connector import failed: {e}")
        print("   Note: This is expected in non-Docker environment")
    
    return True

def test_app_imports():
    """Test app-specific imports."""
    print("\n🔍 Testing App Imports...")
    
    try:
        from app.microservices.flight_service.schemas import FlightSearchRequest, AirportSearchRequest
        print("✅ Flight service schemas imported")
    except Exception as e:
        print(f"❌ Flight service schemas import failed: {e}")
        return False
    
    try:
        from app.microservices.flight_service.cache_service import flight_cache_service
        print("✅ Cache service imported")
    except Exception as e:
        print(f"❌ Cache service import failed: {e}")
        print(f"   Error details: {e}")
        return False
    
    try:
        from app.microservices.flight_service.config import cache_timer
        print("✅ Flight service config imported")
    except Exception as e:
        print(f"❌ Flight service config import failed: {e}")
        return False
    
    return True

def test_enhanced_services():
    """Test enhanced service imports."""
    print("\n🔍 Testing Enhanced Service Imports...")
    
    try:
        from app.microservices.flight_service.shared_service.enhanced_airport_service import enhanced_airport_search
        print("✅ Enhanced airport service imported")
    except Exception as e:
        print(f"❌ Enhanced airport service import failed: {e}")
        print(f"   Error details: {e}")
        return False
    
    try:
        from app.microservices.flight_service.search_service.enhanced_flight_search import enhanced_flight_search_service
        print("✅ Enhanced flight search service imported")
    except Exception as e:
        print(f"❌ Enhanced flight search service import failed: {e}")
        print(f"   Error details: {e}")
        return False
    
    try:
        from app.microservices.flight_service.search_service.enhanced_routes import router
        print("✅ Enhanced routes imported")
    except Exception as e:
        print(f"❌ Enhanced routes import failed: {e}")
        print(f"   Error details: {e}")
        return False
    
    return True

def test_api_router():
    """Test API router integration."""
    print("\n🔍 Testing API Router Integration...")
    
    try:
        from app.api_router import api_router
        print("✅ API router imported")
        
        # Check routes
        route_paths = []
        for route in api_router.routes:
            if hasattr(route, 'path'):
                route_paths.append(route.path)
        
        enhanced_routes = [path for path in route_paths if '/flight/enhanced' in path]
        
        if enhanced_routes:
            print(f"✅ Enhanced routes found: {enhanced_routes}")
            return True
        else:
            print("❌ No enhanced routes found in API router")
            print(f"   Available routes: {route_paths[:10]}...")  # Show first 10
            return False
            
    except Exception as e:
        print(f"❌ API router test failed: {e}")
        return False

def main():
    """Run step-by-step tests."""
    print("🚀 Step-by-Step Integration Test")
    print("=" * 40)
    
    # Add current directory to Python path
    sys.path.insert(0, os.getcwd())
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("App Imports", test_app_imports),
        ("Enhanced Services", test_enhanced_services),
        ("API Router", test_api_router)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Integration is successful.")
        print("\n📝 Next steps:")
        print("   1. Start Docker services: docker-compose up -d")
        print("   2. Test enhanced endpoints")
        print("   3. Run comprehensive tests")
    else:
        print("⚠️  Some tests failed. Check errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
