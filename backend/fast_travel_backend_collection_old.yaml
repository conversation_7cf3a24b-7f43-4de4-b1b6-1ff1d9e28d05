type: collection.insomnia.rest/5.0
name: Flight Booking
meta:
  id: wrk_667cc738a82849abad75584782e864f6
  created: 1733923843889
  modified: 1733923843889
collection:
  - name: Flight
    meta:
      id: fld_7dd45e66831740d38b83dbe4d37fc337
      created: 1724941405660
      modified: 1724941405660
      sortKey: -1724941405660
    children:
      - name: Booking
        meta:
          id: fld_09360bb901d24a0fa8fe59c2345ec4cf
          created: 1728106707825
          modified: 1728106712988
          sortKey: -1725089874906
        children:
          - url: "{{base}}apis/create-booking/"
            name: Create Trip
            meta:
              id: req_5ba7ac5207fe4d82819eec70242f947f
              created: 1728107550795
              modified: 1731402994267
              isPrivate: false
              sortKey: -1728107550795
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"Code": "200",
                	"Msg": [
                		"Success"
                	],
                	"From": "CCJ",
                	"To": "DXB",
                	"FromName": "Kozhikode Arpt",
                	"ToName": "Dubai Intl Arpt",
                	"OnwardDate": "2024-10-23",
                	"ADT": 1,
                	"CHD": 0,
                	"INF": 0,
                	"NetAmount": 10003.0,
                	"GrossAmount": 10003.0,
                	"FareType": "PUBLISHED",
                	"Trips": [
                		{
                			"From": "CCJ",
                			"To": "DXB",
                			"TUI": "{% response 'body', 'req_5213078ab24f48429dfd3e0101500268', 'b64::JC5UVUk=::46b', 'never', 60 %}",
                			"Journey": [
                				{
                					"ChannelCode": "",
                					"Duration": "08h 15m",
                					"FCType": "",
                					"GrossFare": 10003.0,
                					"NetFare": 10003.0,
                					"Notices": [],
                					"OrderID": 0,
                					"Promo": null,
                					"Provider": "1G",
                					"SeatHold": false,
                					"Segments": [
                						{
                							"FUID": "1,2",
                							"VAC": "QR",
                							"Fares": {
                								"GrossFare": 10003.0,
                								"NetFare": 10003.0,
                								"OldSSRAmount": 0,
                								"PTCFare": [
                									{
                										"AddonDiscount": 0,
                										"AddonMarkup": 0,
                										"AgentMarkUp": 0,
                										"Ammendment": 0,
                										"API": 0,
                										"ATOAddonMarkup": 0,
                										"AtoCharge": 0,
                										"CGST": 0,
                										"CUTE": 0,
                										"Fare": 1700.0,
                										"GrossFare": 10003.0,
                										"IGST": 0,
                										"K3": 0,
                										"K7": 0,
                										"NetFare": 10003.0,
                										"OfflineSeviceCharge": 0,
                										"OldSSRAmount": 0,
                										"OT": "3950.0",
                										"OTT": "",
                										"PHF": 0,
                										"PSF": 0,
                										"PTC": "ADT",
                										"RCF": 0,
                										"RCS": 0,
                										"ReissueCharge": 0,
                										"SGST": 0,
                										"ST": 0,
                										"Tax": null,
                										"TransactionFee": 0,
                										"UD": 289.0,
                										"VATonServiceCharge": 0,
                										"VATonTransactionFee": 0,
                										"YQ": 3560.0,
                										"YR": 0
                									}
                								],
                								"TotalBaseFare": 1700.0,
                								"TotalTax": 8303.0,
                								"TotalAddonDiscount": 0,
                								"TotalAddonMarkup": 0,
                								"TotalAgentMarkUp": 0,
                								"TotalAtoCharge": 0,
                								"TotalCommission": 0,
                								"TotalReissueCharge": 0,
                								"TotalServiceTax": 0,
                								"TotalTransactionFee": 0,
                								"TotalVATonServiceCharge": 0,
                								"TotalVATonTransactionFee": 0
                							},
                							"Flight": {
                								"AirlineName": "Oman Aviation|Oman Aviation|Oman Aviation",
                								"AirCraft": "738",
                								"Airline": "Oman Aviation|Oman Aviation|Oman Aviation",
                								"Amenities": null,
                								"ArrAirportName": "Muscat Internatonal Arpt |Muscat",
                								"ArrivalCode": "MCT",
                								"ArrivalTerminal": null,
                								"ArrivalTime": "2024-10-23T10:50",
                								"Cabin": "ECONOMY",
                								"CarbonEmissions": 0,
                								"DepAirportName": "Kozhikode Arpt |Kozhikode",
                								"DepartureCode": "CCJ",
                								"DepartureTerminal": null,
                								"DepartureTime": "2024-10-23T08:55",
                								"Duration": "03h 25m",
                								"EquipmentType": "",
                								"FareClass": "O",
                								"Farelink": null,
                								"FBC": "OELOIA",
                								"FCBegin": "",
                								"FCEnd": "",
                								"Provider": "WY",
                								"FlightNo": "298",
                								"FUID": 1,
                								"Hops": null,
                								"VAC": "WY",
                								"MAC": "WY",
                								"OAC": "WY",
                								"RBD": "",
                								"Refundable": "N",
                								"Seats": 4
                							},
                							"SSR": [
                								{
                									"Code": "BAG",
                									"Description": "30KG,7Kg",
                									"PieceDescription": "",
                									"Charge": 0.0,
                									"OrginalCharge": 0.0,
                									"VAT": 0.0,
                									"Type": "2",
                									"Category": "",
                									"PTC": "ADT",
                									"ID": 1,
                									"IsFreeMeal": true,
                									"MealImage": "",
                									"SSRUrl": null,
                									"OriginID": 0,
                									"OriginCharge": 0.0,
                									"AdditionalField": []
                								}
                							],
                							"Rules": [
                								{
                									"OrginDestination": "CCJ-DXB",
                									"FareRuleText": null,
                									"Rule": [
                										{
                											"Info": [
                												{
                													"AdultAmount": "CANCELLATION Not Available",
                													"ChildAmount": "",
                													"InfantAmount": "",
                													"YouthAmount": null,
                													"Description": "Before Departure",
                													"CurrencyCode": "",
                													"TimeDay": null
                												}
                											],
                											"Head": "Cancellation(Per Pax/ Per Journey)"
                										},
                										{
                											"Info": [
                												{
                													"AdultAmount": "Not Applicable",
                													"ChildAmount": "",
                													"InfantAmount": "",
                													"YouthAmount": null,
                													"Description": "Before Departure",
                													"CurrencyCode": "",
                													"TimeDay": null
                												}
                											],
                											"Head": "NO Show(Per Pax/ Per Journey)"
                										},
                										{
                											"Info": [
                												{
                													"AdultAmount": "6000.0",
                													"ChildAmount": "6000.0",
                													"InfantAmount": "",
                													"YouthAmount": null,
                													"Description": "Before Departure",
                													"CurrencyCode": "",
                													"TimeDay": null
                												}
                											],
                											"Head": "Date Change(Per Pax/ Per Journey)"
                										}
                									]
                								}
                							]
                						},
                						{
                							"FUID": "1,2",
                							"VAC": "QR",
                							"Fares": {
                								"GrossFare": 10003.0,
                								"NetFare": 10003.0,
                								"OldSSRAmount": 0,
                								"PTCFare": [
                									{
                										"AddonDiscount": 0,
                										"AddonMarkup": 0,
                										"AgentMarkUp": 0,
                										"Ammendment": 0,
                										"API": 0,
                										"ATOAddonMarkup": 0,
                										"AtoCharge": 0,
                										"CGST": 0,
                										"CUTE": 0,
                										"Fare": 1700.0,
                										"GrossFare": 10003.0,
                										"IGST": 0,
                										"K3": 0,
                										"K7": 0,
                										"NetFare": 10003.0,
                										"OfflineSeviceCharge": 0,
                										"OldSSRAmount": 0,
                										"OT": "3950.0",
                										"OTT": "",
                										"PHF": 0,
                										"PSF": 0,
                										"PTC": "ADT",
                										"RCF": 0,
                										"RCS": 0,
                										"ReissueCharge": 0,
                										"SGST": 0,
                										"ST": 0,
                										"Tax": null,
                										"TransactionFee": 0,
                										"UD": 289.0,
                										"VATonServiceCharge": 0,
                										"VATonTransactionFee": 0,
                										"YQ": 3560.0,
                										"YR": 0
                									}
                								],
                								"TotalBaseFare": 1700.0,
                								"TotalTax": 8303.0,
                								"TotalAddonDiscount": 0,
                								"TotalAddonMarkup": 0,
                								"TotalAgentMarkUp": 0,
                								"TotalAtoCharge": 0,
                								"TotalCommission": 0,
                								"TotalReissueCharge": 0,
                								"TotalServiceTax": 0,
                								"TotalTransactionFee": 0,
                								"TotalVATonServiceCharge": 0,
                								"TotalVATonTransactionFee": 0
                							},
                							"Flight": {
                								"AirlineName": "Oman Aviation|Oman Aviation|Oman Aviation",
                								"AirCraft": "7M8",
                								"Airline": "Oman Aviation|Oman Aviation|Oman Aviation",
                								"Amenities": null,
                								"ArrAirportName": "Dubai Intl Arpt |Dubai",
                								"ArrivalCode": "DXB",
                								"ArrivalTerminal": "1",
                								"ArrivalTime": "2024-10-23T15:40",
                								"Cabin": "ECONOMY",
                								"CarbonEmissions": 0,
                								"DepAirportName": "Muscat Internatonal Arpt |Muscat",
                								"DepartureCode": "MCT",
                								"DepartureTerminal": null,
                								"DepartureTime": "2024-10-23T14:30",
                								"Duration": "01h 10m",
                								"EquipmentType": "",
                								"FareClass": "O",
                								"Farelink": null,
                								"FBC": "OELOIA",
                								"FCBegin": "",
                								"FCEnd": "",
                								"Provider": "WY",
                								"FlightNo": "609",
                								"FUID": 2,
                								"Hops": null,
                								"VAC": "WY",
                								"MAC": "WY",
                								"OAC": "WY",
                								"RBD": "",
                								"Refundable": "N",
                								"Seats": 4
                							},
                							"SSR": [
                								{
                									"Code": "BAG",
                									"Description": "30KG,7Kg",
                									"PieceDescription": "",
                									"Charge": 0.0,
                									"OrginalCharge": 0.0,
                									"VAT": 0.0,
                									"Type": "2",
                									"Category": "",
                									"PTC": "ADT",
                									"ID": 1,
                									"IsFreeMeal": true,
                									"MealImage": "",
                									"SSRUrl": null,
                									"OriginID": 0,
                									"OriginCharge": 0.0,
                									"AdditionalField": []
                								}
                							]
                						}
                					],
                					"Stops": 1
                				}
                			]
                		}
                	],
                	"TUI": "12-15-2-10-9036883663_0CCJMCTWY298MCTDXBWY609~14937318939024780||",
                	"CustomTripBooking": 8515,
                	"travellers": [
                		{
                			"title": "Mr",
                			"first_name": "John",
                			"first_name_ar": null,
                			"last_name": "Doe",
                			"last_name_ar": null,
                			"middle_name": "Michael",
                			"middle_name_ar": null,
                			"traveller_type": "adult",
                			"age": 35,
                			"dob": "1989-05-15",
                			"id_type": "Passport",
                			"identity_no": "*********",
                			"gender": "MALE",
                			"nationality": "US",
                			"country_of_residence": "US",
                			"passport_no": "*********",
                			"relation_with_main_traveller": "Husband",
                			"passport_city": "New York",
                			"passport_city_ar": null,
                			"passport_expiry_date": "2030-12-31",
                			"contactinfo": {
                				"title": "Mr",
                				"first_name": "John",
                				"last_name": "Hnnoo",
                				"phone_number": "*********",
                				"phn_country_code": "+91",
                				"email": "<EMAIL>"
                			},
                			"room_reference": "12345",
                			"ssr_info": [
                				{
                					"FUID": 1,
                					"SSID": 1,
                					"charge": ""
                				}
                			],
                			"address": "123 Elm Street, New York, NY, USA",
                			"husband_name": null,
                			"husband_name_ar": null,
                			"father_name": "Michael Doe",
                			"father_name_ar": null,
                			"mother_name": "Jane Doe",
                			"mother_name_ar": null,
                			"date_of_issue": "2021-01-01",
                			"birth_place": "New York",
                			"birth_place_ar": null,
                			"city": "New York",
                			"passport_type": "Regular"
                		}
                	]
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.0.0
            authentication:
              type: bearer
              token: Guest
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{base}}apis/create-booking/ "
            name: Create Trip 2
            meta:
              id: req_4e43f4debd444168bb9d508d7a5c8bfc
              created: 1731403346758
              modified: 1731686018112
              isPrivate: false
              sortKey: -1727666967234.75
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"flight_booking": {
                		"provider_info": {
                			"code": "TJ"
                		},
                		"TUI": "RS6e98eef7-2507-4529-9c12-b5c2ce972a6e|d998824d-8a0d-406a-9b28-3c1c6eee44cc|20241101144209",
                		"Mode": null,
                		"TransactionID": 240710052,
                		"ADT": 1,
                		"CHD": 1,
                		"INF": 1,
                		"NetAmount": 143960.0,
                		"AirlineNetFare": 125162.0,
                		"SSRAmount": 0.0,
                		"CrossSellAmount": 18798.0,
                		"GrossAmount": 125162.0,
                		"Trips": [
                			{
                				"Journey": [
                					{
                						"Provider": "tripjack",
                						"Stops": "0",
                						"Offer": "2366237",
                						"OrderID": 56,
                						"GrossFare": 125162.0,
                						"NetFare": 125162.0,
                						"CSBalance": 0.0,
                						"Promo": null,
                						"Segments": [
                							{
                								"Flight": {
                									"FUID": "1",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "TJINP6RW",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "537",
                									"ArrivalTime": "2024-11-10T05:45:00",
                									"DepartureTime": "2024-11-10T03:35:00",
                									"ArrivalCode": "DOH",
                									"DepartureCode": "CCJ",
                									"ArrAirportName": "Doha |Doha |QA |Qatar",
                									"DepAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "T",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "04h 40m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
                								},
                								"Fares": {
                									"PTCFare": [
                										{
                											"PTC": "ADT",
                											"Fare": 10445.0,
                											"YQ": 21866.0,
                											"PSF": 1191.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,GB,UB",
                											"OT": "3700.0,850.0,1801.0,2772.0,346.0,346.0,2772.0,462.0,9614.0,5342.0",
                											"Tax": 51062.0,
                											"GrossFare": 61557.0,
                											"NetFare": 61557.0,
                											"ST": 50.00,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										},
                										{
                											"PTC": "CHD",
                											"Fare": 7830.0,
                											"YQ": 21866.0,
                											"PSF": 1191.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,UB",
                											"OT": "3700.0,850.0,1670.0,2772.0,346.0,346.0,2772.0,462.0,5342.0",
                											"Tax": 41317.0,
                											"GrossFare": 49197.0,
                											"NetFare": 49197.0,
                											"ST": 50.00,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										},
                										{
                											"PTC": "INF",
                											"Fare": 1570.0,
                											"YQ": 3364.0,
                											"PSF": 0.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,K3,UB",
                											"OT": "3700.0,432.0,5342.0",
                											"Tax": 12838.0,
                											"GrossFare": 14408.0,
                											"NetFare": 14408.0,
                											"ST": 0.0,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										}
                									],
                									"GrossFare": 125162.0,
                									"NetFare": 125162.0,
                									"TotalServiceTax": 100.00,
                									"TotalBaseFare": 19845.0,
                									"TotalTax": 105317.00,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 100.00,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							},
                							{
                								"Flight": {
                									"FUID": "2",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "TJINP6RW",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "105",
                									"ArrivalTime": "2024-11-11T07:20:00",
                									"DepartureTime": "2024-11-11T02:45:00",
                									"ArrivalCode": "LHR",
                									"DepartureCode": "DOH",
                									"ArrAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
                									"DepAirportName": "Doha |Doha |QA |Qatar",
                									"ArrivalTerminal": "4",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "T",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "06h 35m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							}
                						],
                						"Notices": null
                					}
                				]
                			},
                			{
                				"Journey": [
                					{
                						"Provider": "tripjack",
                						"Stops": "0",
                						"Offer": "46556",
                						"OrderID": 66,
                						"GrossFare": 0.0,
                						"NetFare": 0.0,
                						"CSBalance": 0.0,
                						"Promo": null,
                						"Segments": [
                							{
                								"Flight": {
                									"FUID": "3",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "BA",
                									"FareBasisCode": "OJINP6RX",
                									"Airline": "Qatar Airways|Qatar Airways|British Airways",
                									"Aircraft": "",
                									"FlightNo": "5942",
                									"ArrivalTime": "2024-11-15T23:10:00",
                									"DepartureTime": "2024-11-15T13:05:00",
                									"ArrivalCode": "DOH",
                									"DepartureCode": "LHR",
                									"ArrAirportName": "Doha |Doha |QA |Qatar",
                									"DepAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "5",
                									"EquipmentType": "",
                									"RBD": "O",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "08h 05m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": null,
                									"MACAirlineLogo": null,
                									"OACAirlineLogo": null
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							},
                							{
                								"Flight": {
                									"FUID": "4",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "OJINP6RX",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "536",
                									"ArrivalTime": "2024-11-17T02:10:00",
                									"DepartureTime": "2024-11-16T19:40:00",
                									"ArrivalCode": "CCJ",
                									"DepartureCode": "DOH",
                									"ArrAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
                									"DepAirportName": "Doha |Doha |QA |Qatar",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "O",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "04h 00m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": null,
                									"MACAirlineLogo": null,
                									"OACAirlineLogo": null
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							}
                						],
                						"Notices": null
                					}
                				]
                			}
                		],
                		"Rules": [
                			{
                				"OrginDestination": "CCJ-DOH",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "600",
                								"ChildAmount": "600",
                								"InfantAmount": "600",
                								"Description": "Re Schedule",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "600",
                								"ChildAmount": "600",
                								"InfantAmount": "600",
                								"Description": "Cancellation",
                								"RuleText": ""
                							}
                						],
                						"Head": "ATO Service Fee"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "DOH-LHR",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "LHR-DOH",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "DOH-CCJ",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			}
                		],
                		"SSR": [
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			}
                		],
                		"CrossSell": [
                			{
                				"Code": "INS",
                				"TransactionID": 240710054
                			},
                			{
                				"Code": "RPI",
                				"TransactionID": *********
                			}
                		],
                		"Auxiliaries": [
                			{
                				"Code": null,
                				"EmployeeID": null,
                				"Amount": 0.0
                			}
                		],
                		"Hold": false,
                		"ActualHoldTime": 0,
                		"ActualDisplayTime": 0,
                		"CeilingInfo": "R0"
                	},
                	"Code": "200",
                	"Msg": [
                		"Success"
                	],
                	"Travellers": [
                		{
                			"ID": 1,
                			"PaxID": 0,
                			"Operation": "",
                			"Title": "Mr",
                			"FName": "SAJAD",
                			"LName": "SAJID",
                			"Age": 35,
                			"DOB": "1989-01-03",
                			"Country": "",
                			"Gender": "M",
                			"PTC": "ADT",
                			"Nationality": "INDIA",
                			"PassportNo": "a1234567",
                			"PLI": "INDIA",
                			"PDOE": "2030-07-11",
                			"VisaType": "IMMIGRANT VISA",
                			"DefenceID": "",
                			"PaxCategoryID": "",
                			"DocType": "P"
                		},
                		{
                			"ID": 2,
                			"PaxID": 0,
                			"Operation": "",
                			"Title": "Mstr",
                			"FName": "SAJID",
                			"LName": "SAJID",
                			"Age": 11,
                			"DOB": "2013-10-08",
                			"Gender": "M",
                			"Country": "",
                			"PTC": "CHD",
                			"Nationality": "INDIA",
                			"PassportNo": "A1234567",
                			"PLI": "INDIA",
                			"PDOE": "2030-11-14",
                			"VisaType": "STUDENT VISA",
                			"DOBDay": "0",
                			"DOBMonth": "0",
                			"DOBYear": "0",
                			"PDOEDay": "0",
                			"PDOEMonth": "0",
                			"PDOEBYear": "0",
                			"DefenceID": "",
                			"PaxCategoryID": "",
                			"DocType": "P"
                		},
                		{
                			"ID": 3,
                			"PaxID": 0,
                			"Operation": "",
                			"Title": "Mstr",
                			"FName": "SAYEED",
                			"LName": "SHAD",
                			"Age": 0,
                			"DOB": "2024-07-18",
                			"Gender": "M",
                			"Country": "",
                			"PTC": "INF",
                			"Nationality": "INDIA",
                			"PassportNo": "P1234567",
                			"PLI": "INDIA",
                			"PDOE": "2030-07-11",
                			"VisaType": "IMMIGRANT VISA",
                			"DOBDay": "0",
                			"DOBMonth": "0",
                			"DOBYear": "0",
                			"PDOEDay": "0",
                			"PDOEMonth": "0",
                			"PDOEBYear": "0",
                			"DefenceID": "",
                			"PaxCategoryID": "",
                			"DocType": "P"
                		}
                	],
                	"ContactInfo": {
                		"Title": "",
                		"FName": "",
                		"LName": "",
                		"Mobile": "8129884096",
                		"Phone": "",
                		"Email": "<EMAIL>",
                		"Address": "AKBAR ONLINE BOOKING COMPANY PVT LTD",
                		"CountryCode": "IN",
                		"MobileCountryCode": "+91",
                		"State": "Maharashtra",
                		"City": "Mumbai",
                		"PIN": "400003",
                		"GSTAddress": "",
                		"GSTCompanyName": "",
                		"GSTTIN": "",
                		"UpdateProfile": false,
                		"IsGuest": true,
                		"SaveGST": false,
                		"Language": "",
                		"GSTaddress": ""
                	}
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.0.0
            authentication:
              type: bearer
              token: Guest
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: "Auth "
        meta:
          id: fld_c65440dc9f094f1782581cfc5bfe9959
          created: 1730263610758
          modified: 1730263610758
          sortKey: -1730263610758
        children:
          - url: "{{ _.base }}apis/auth/register"
            name: Register
            meta:
              id: req_c82d2b1d2ae641d3b304d3663c886016
              created: 1730263581701
              modified: 1748012479992
              isPrivate: false
              sortKey: -1730446386099
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"email":"<EMAIL>",
                	"name":"hello",
                	"phone_number":"7012884807",
                	"phone_country_code":"91",
                	"role": "customer"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/9.3.3
            authentication:
              type: bearer
              token: Guest
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.base }}apis/auth/login"
            name: Login
            meta:
              id: req_7b6575792b8d42ecb3c8e17150cffc85
              created: 1730263621186
              modified: 1747998575879
              isPrivate: false
              sortKey: -1730446385999
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"password":"password",
                	"email":"<EMAIL>"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/9.3.3
            authentication:
              type: bearer
              token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.JJCU5TrmpvBwq_1GnaIUD39zOT7_rw_Y-jZwxS2mW48
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - name: Round Trip
        meta:
          id: fld_5d430a88d6cc48728da3c385dcfad4d3
          created: 1730446117927
          modified: 1730446117927
          sortKey: -1730446117927
        children:
          - url: "{{ _.test }}apis/search"
            name: Search
            meta:
              id: req_c88eaa7ff54e45d7928bcc39127e6141
              created: 1730446128072
              modified: 1748005072243
              isPrivate: false
              sortKey: -1730446128072
            method: POST
            body:
              mimeType: application/json
              text: |-
                {
                	"SecType": "D",
                	"FareType": "RT",
                	"ADT": 1,
                	"CHD": 0,
                	"INF": 0,
                	"Cabin": "E",
                	"Source": "CF",
                	"Mode": "AS",
                	"ClientID": "",
                	"IsMultipleCarrier": false,
                	"IsRefundable": false,
                	"preferedAirlines": null,
                	"TUI": "",
                	"YTH": 0,
                	"Trips": [
                		{
                			"From": "COK",
                			"To": "DEL",
                			"OnwardDate": "2025-08-27",
                			"ReturnDate": "2025-08-28",
                			"TUI": ""
                		}
                	],
                	"Parameters": {
                		"Airlines": "",
                		"GroupType": "",
                		"IsDirect": false,
                		"IsNearbyAirport": true,
                		"IsStudentFare": false,
                		"Refundable": ""
                	}
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.1.1
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.test }}apis/search_list"
            name: Search List
            meta:
              id: req_d150e4dc9a3b478b81a9c77a0560d990
              created: 1730446186041
              modified: 1748005096698
              isPrivate: false
              sortKey: -1730009923136
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"ClientID": "",
                	"TUI": "{% response 'body', 'req_c88eaa7ff54e45d7928bcc39127e6141', 'b64::JC5UVUk=::46b', 'never', 60 %}"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.1.1
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.test }}apis/pricing"
            name: "Pricing "
            meta:
              id: req_b0932aaecff54e21a0aa07fef2dfc7be
              created: 1730446245969
              modified: 1748005125568
              isPrivate: false
              sortKey: -1729791820668
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"Trips": [
                		{
                			"Amount": 14257,
                			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1swXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
                			"ChannelCode": null,
                			"OrderID": 1,
                			"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
                		},
                		{
                			"Amount": 14257,
                			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1sxXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
                			"ChannelCode": null,
                			"OrderID": 1,
                			"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
                		}
                	],
                	"ClientID": "",
                	"Mode": "SS",
                	"Options": "A",
                	"Source": "SF",
                	"TripType": "RT"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.1.1
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.test }}apis/pricing_list"
            name: Pricing List
            meta:
              id: req_a0f3d07a9b1041bb8bc816abaab11e9b
              created: 1730446361893
              modified: 1747992560762
              isPrivate: false
              sortKey: -1729682769434
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
                }
            headers:
              - name: Content-Type
                value: application/json
              - name: User-Agent
                value: insomnia/10.1.1
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
          - url: "{{ _.base }}apis/create-booking/"
            name: Create-Booking
            meta:
              id: req_92fa415f467343aa9dde2085d8d54d03
              created: 1730457745673
              modified: 1734525357450
              isPrivate: false
              sortKey: -1729628243817
            method: POST
            body:
              mimeType: application/json
              text: >-
                {
                	"flight_booking": {
                		"provider_info": {
                			"code": "TJ"
                		},
                		"TUI": "RS6e98eef7-2507-4529-9c12-b5c2ce972a6e|d998824d-8a0d-406a-9b28-3c1c6eee44cc|20241101144209",
                		"Mode": null,
                		"TransactionID": 240710052,
                		"ADT": 1,
                		"CHD": 1,
                		"INF": 0,
                		"NetAmount": 143960.0,
                		"AirlineNetFare": 125162.0,
                		"SSRAmount": 0.0,
                		"CrossSellAmount": 18798.0,
                		"GrossAmount": 125162.0,
                		"Trips": [
                			{
                				"Journey": [
                					{
                						"Provider": "AMN",
                						"Stops": "0",
                						"Offer": "",
                						"OrderID": 0,
                						"GrossFare": 125162.0,
                						"NetFare": 125162.0,
                						"CSBalance": 0.0,
                						"Promo": null,
                						"Segments": [
                							{
                								"Flight": {
                									"FUID": "1",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "TJINP6RW",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "537",
                									"ArrivalTime": "2024-11-10T05:45:00",
                									"DepartureTime": "2024-11-10T03:35:00",
                									"ArrivalCode": "DOH",
                									"DepartureCode": "CCJ",
                									"ArrAirportName": "Doha |Doha |QA |Qatar",
                									"DepAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "T",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "04h 40m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
                								},
                								"Fares": {
                									"PTCFare": [
                										{
                											"PTC": "ADT",
                											"Fare": 10445.0,
                											"YQ": 21866.0,
                											"PSF": 1191.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,GB,UB",
                											"OT": "3700.0,850.0,1801.0,2772.0,346.0,346.0,2772.0,462.0,9614.0,5342.0",
                											"Tax": 51062.0,
                											"GrossFare": 61557.0,
                											"NetFare": 61557.0,
                											"ST": 50.00,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										},
                										{
                											"PTC": "CHD",
                											"Fare": 7830.0,
                											"YQ": 21866.0,
                											"PSF": 1191.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,UB",
                											"OT": "3700.0,850.0,1670.0,2772.0,346.0,346.0,2772.0,462.0,5342.0",
                											"Tax": 41317.0,
                											"GrossFare": 49197.0,
                											"NetFare": 49197.0,
                											"ST": 50.00,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										},
                										{
                											"PTC": "INF",
                											"Fare": 1570.0,
                											"YQ": 3364.0,
                											"PSF": 0.0,
                											"YR": 0.0,
                											"UD": 0.0,
                											"K3": 0.0,
                											"API": 0.0,
                											"OTT": "YR,K3,UB",
                											"OT": "3700.0,432.0,5342.0",
                											"Tax": 12838.0,
                											"GrossFare": 14408.0,
                											"NetFare": 14408.0,
                											"ST": 0.0,
                											"CMS": 0.0,
                											"VATonServiceCharge": 0.0,
                											"VATonTransactionFee": 0.0,
                											"AgentMarkup": 0.0,
                											"Markup": 0.0,
                											"AtoCharge": 0.0,
                											"ReissueCharge": 0.0
                										}
                									],
                									"GrossFare": 125162.0,
                									"NetFare": 125162.0,
                									"TotalServiceTax": 100.00,
                									"TotalBaseFare": 19845.0,
                									"TotalTax": 105317.00,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 100.00,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							},
                							{
                								"Flight": {
                									"FUID": "2",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "TJINP6RW",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "105",
                									"ArrivalTime": "2024-11-11T07:20:00",
                									"DepartureTime": "2024-11-11T02:45:00",
                									"ArrivalCode": "LHR",
                									"DepartureCode": "DOH",
                									"ArrAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
                									"DepAirportName": "Doha |Doha |QA |Qatar",
                									"ArrivalTerminal": "4",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "T",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "06h 35m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
                									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							}
                						],
                						"Notices": null
                					}
                				]
                			},
                			{
                				"Journey": [
                					{
                						"Provider": "AMN",
                						"Stops": "0",
                						"Offer": "",
                						"OrderID": 0,
                						"GrossFare": 0.0,
                						"NetFare": 0.0,
                						"CSBalance": 0.0,
                						"Promo": null,
                						"Segments": [
                							{
                								"Flight": {
                									"FUID": "3",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "BA",
                									"FareBasisCode": "OJINP6RX",
                									"Airline": "Qatar Airways|Qatar Airways|British Airways",
                									"Aircraft": "",
                									"FlightNo": "5942",
                									"ArrivalTime": "2024-11-15T23:10:00",
                									"DepartureTime": "2024-11-15T13:05:00",
                									"ArrivalCode": "DOH",
                									"DepartureCode": "LHR",
                									"ArrAirportName": "Doha |Doha |QA |Qatar",
                									"DepAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "5",
                									"EquipmentType": "",
                									"RBD": "O",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "08h 05m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": null,
                									"MACAirlineLogo": null,
                									"OACAirlineLogo": null
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							},
                							{
                								"Flight": {
                									"FUID": "4",
                									"VAC": "QR",
                									"MAC": "QR",
                									"OAC": "QR",
                									"FareBasisCode": "OJINP6RX",
                									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
                									"Aircraft": "",
                									"FlightNo": "536",
                									"ArrivalTime": "2024-11-17T02:10:00",
                									"DepartureTime": "2024-11-16T19:40:00",
                									"ArrivalCode": "CCJ",
                									"DepartureCode": "DOH",
                									"ArrAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
                									"DepAirportName": "Doha |Doha |QA |Qatar",
                									"ArrivalTerminal": "",
                									"DepartureTerminal": "",
                									"EquipmentType": "",
                									"RBD": "O",
                									"Cabin": "E",
                									"Refundable": "R",
                									"Amenities": null,
                									"Duration": "04h 00m ",
                									"PaxCategory": "",
                									"Hops": null,
                									"VACAirlineLogo": null,
                									"MACAirlineLogo": null,
                									"OACAirlineLogo": null
                								},
                								"Fares": {
                									"PTCFare": [
                									],
                									"GrossFare": 0.0,
                									"NetFare": 0.0,
                									"TotalServiceTax": 0.0,
                									"TotalBaseFare": 0.0,
                									"TotalTax": 0.0,
                									"TotalCommission": 0.0,
                									"TotalVATonServiceCharge": 0.0,
                									"TotalVATonTransactionFee": 0.0,
                									"TotalAgentMarkup": 0.0,
                									"TotalMarkup": 0.0,
                									"TotalAtoCharge": 0.0,
                									"TotalReissueCharge": 0.0,
                									"DealKey": null
                								},
                								"MulticityRefID": null
                							}
                						],
                						"Notices": null
                					}
                				]
                			}
                		],
                		"Rules": [
                			{
                				"OrginDestination": "CCJ-DOH",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "600",
                								"ChildAmount": "600",
                								"InfantAmount": "600",
                								"Description": "Re Schedule",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "600",
                								"ChildAmount": "600",
                								"InfantAmount": "600",
                								"Description": "Cancellation",
                								"RuleText": ""
                							}
                						],
                						"Head": "ATO Service Fee"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "DOH-LHR",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "LHR-DOH",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			},
                			{
                				"OrginDestination": "DOH-CCJ",
                				"FUID": "1,2,3,4",
                				"Provider": "AMN",
                				"FareRuleText": null,
                				"Rule": [
                					{
                						"Info": [
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "Non Refundable",
                								"ChildAmount": "Non Refundable",
                								"InfantAmount": "Cancel Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Cancellation"
                					},
                					{
                						"Info": [
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "No Show",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "After Departure",
                								"RuleText": ""
                							},
                							{
                								"AdultAmount": "10935.0",
                								"ChildAmount": "10935.0",
                								"InfantAmount": "Reissue Permitted",
                								"Description": "Before Departure",
                								"RuleText": ""
                							}
                						],
                						"Head": "Re Schedule"
                					}
                				],
                				"SpecialInformations": ""
                			}
                		],
                		"SSR": [
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "1",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "2",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "3",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "ADT",
                				"PaxId": "1",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "CHD",
                				"PaxId": "2",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "25 Kg,7 Kg",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			},
                			{
                				"PTC": "INF",
                				"PaxId": "3",
                				"FUID": "4",
                				"Code": "BAG",
                				"Description": "10 Kg,",
                				"PieceDescription": "",
                				"SSRCategory": "",
                				"Charge": 0.0,
                				"Type": "2",
                				"SSRUrl": null
                			}
                		],
                		"CrossSell": [
                			{
                				"Code": "INS",
                				"TransactionID": 240710054
                			},
                			{
                				"Code": "RPI",
                				"TransactionID": *********
                			}
                		],
                		"Auxiliaries": [
                			{
                				"Code": null,
                				"EmployeeID": null,
                				"Amount": 0.0
                			}
                		],
                		"Hold": false,
                		"ActualHoldTime": 0,
                		"ActualDisplayTime": 0,
                		"CeilingInfo": "R0"
                	},
                	"Code": "200",
                	"Msg": [
                		"Success"
                	],
                	"Travellers": [
                		{
                			"ID": 1,
                			"PaxID": 0,
                			"Operation": "",
                			"Title": "Mr",
                			"FName": "VISHNU",
                			"LName": "K",
                			"Age": 35,
                			"DOB": "1998-10-30",
                			"Country": "",
                			"Gender": "M",
                			"PTC": "ADT",
                			"Nationality": "INDIA",
                			"PassportNo": "a1234567",
                			"PLI": "INDIA",
                			"PDOE": "2030-07-11",
                			"VisaType": "IMMIGRANT VISA",
                			"DefenceID": "",
                			"PaxCategoryID": "",
                			"DocType": "P"
                		}
                	],
                	"ContactInfo": {
                		"Title": "MR",
                		"FName": "VISHNU",
                		"LName": "K",
                		"Mobile": "12345678",
                		"Phone": "1234567",
                		"Email": "<EMAIL>",
                		"Address": "KOCHI",
                		"CountryCode": "IN",
                		"MobileCountryCode": "+91",
                		"State": "KERALA",
                		"City": "KOCHI",
                		"PIN": "673638",
                		"GSTAddress": "AB1234343443",
                		"GSTCompanyName": "Ecogo Software Solutions Private Limited",
                		"GSTTIN": "G3223",
                		"UpdateProfile": false,
                		"IsGuest": true,
                		"SaveGST": false,
                		"Language": "",
                		"GSTaddress": ""
                	}
                }
            headers:
              - name: Content-Type
                value: application/json
                id: pair_18ff4e04b95b44b692c484d77628a651
              - name: User-Agent
                value: insomnia/10.1.1
                id: pair_d1de5c399055406d8110965c08b9631e
              - id: pair_4b50267913be4b3ca791fcc1f72a551b
                name: Authorization
                value: Bearer
                  eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.L_Zk3TLGtR9TyK6EsY2lzFYBZ1y7EmCgYzfefKdaAcs
                disabled: false
                type: text
                multiline: false
            authentication:
              type: bearer
              token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.E2FSYubSuuh4EVi4-7mCoKQLO6OSMumNaiDEorEGqbs
              prefix: "Authorization: Bearer"
              disabled: true
            settings:
              renderRequestBody: true
              encodeUrl: true
              followRedirects: global
              cookies:
                send: true
                store: true
              rebuildPath: true
      - url: "{{ _.test }}apis/search"
        name: Search
        meta:
          id: req_31715a0eb9404e8b8ed81c20c0036dbd
          created: 1724941429652
          modified: 1747995785098
          isPrivate: false
          sortKey: -1724941429652
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"SecType": "D",
            	"FareType": "ON",
            	"ADT": 2,
            	"CHD": 0,
            	"INF": 0,
            	"Cabin": "E",
            	"Source": "CF",
            	"Mode": "AS",
            	"ClientID": "",
            	"IsMultipleCarrier": false,
            	"IsRefundable": false,
            	"preferedAirlines": null,
            	"TUI": "",
            	"YTH": 0,
            	"Trips": [
            		{
            			"From": "DEL",
            			"To": "BLR",
            			"OnwardDate": "2025-09-30",
            			"TUI": ""
            		}
            	],
            	"Parameters": {
            		"Airlines": "",
            		"GroupType": "",
            		"IsDirect": false,
            		"IsNearbyAirport": true,
            		"IsStudentFare": false,
            		"Refundable": ""
            	}
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/search_list"
        name: Get Search
        meta:
          id: req_c26b6f42fb384aa281d7452dc336eafd
          created: 1724942917816
          modified: 1747981616580
          isPrivate: false
          sortKey: -1724903526518.5
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"ClientID": "",
            	"TUI": "{% response 'body', 'req_31715a0eb9404e8b8ed81c20c0036dbd', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.test }}apis/pricing/"
        name: Pricer
        meta:
          id: req_11bca22defd4494482c1e1603d0f1982
          created: 1727864287335
          modified: 1747993720581
          isPrivate: false
          sortKey: -1724884574951.75
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"Trips": [
            		{
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1swXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"TUI": "{% response 'body', 'req_c26b6f42fb384aa281d7452dc336eafd', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		}
            	]
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: "{% response 'body', 'req_f0a42a9ef5384e84b253f9a271c150df',
            'b64::JC50b2tlbg==::46b', 'never', 60 %}"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/pricing_list/"
        name: Pricer List
        meta:
          id: req_b013a3b9a8524a339c8570c8afb39172
          created: 1727864322015
          modified: 1747981638703
          isPrivate: false
          sortKey: -1724875099168.375
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"TUI": "{% response 'body', 'req_11bca22defd4494482c1e1603d0f1982', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}b2capis/payments/v1/"
        name: Payment
        meta:
          id: req_135c9d489af24d42ab184c9235a100b4
          created: 1728494565161
          modified: 1729508523099
          isPrivate: false
          sortKey: -1724870361276.6875
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"booking_id": {% response 'body', 'req_88de0f855c7247538dc01f66f0836421', 'b64::JC5ib29raW5n::46b', 'never', 60 %}
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/create-booking/"
        name: Create Booking
        meta:
          id: req_99ff2b32123f4e8988562fe771ec8a36
          created: 1728721635167
          modified: 1748241774955
          isPrivate: false
          sortKey: -1724872730222.5312
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
                "flight_booking": {
                    "provider_info": {
                        "code": "1G"
                    },
                    "TUI": "12-15-2-10-9036883663_0CCJMCTWY298MCTDXBWY609~14937318939024780||",
                    "ADT": 1,
                    "CHD": 0,
                    "INF": 0,
                    "NetAmount": 10003.0,
                    "Trips": [
                        {
                            "Journey": [
                                {
                                    "Provider": "1G",
                                    "Stops": 1,
                                    "Segments": [
                                        {
                                            "Flight": {
                                                "FUID": "1",
                                                "VAC": "WY",
                                                "MAC": "WY",
                                                "OAC": "WY",
                                                "Airline": "Oman Aviation|Oman Aviation|Oman Aviation",
                                                "FlightNo": "298",
                                                "ArrivalTime": "2024-10-23T10:50",
                                                "DepartureTime": "2024-10-23T08:55",
                                                "ArrivalCode": "MCT",
                                                "DepartureCode": "CCJ",
                                                "Duration": "03h 25m",
                                                "FareBasisCode": "OELOIA",
                                                "ArrAirportName": "Muscat Internatonal Arpt |Muscat",
                                                "DepAirportName": "Kozhikode Arpt |Kozhikode",
                                                "RBD": "",
                                                "Cabin": "ECONOMY",
                                                "Refundable": "N"
                                            },
                                            "Fares": {
                                                "GrossFare": 10003.55,
                                                "NetFare": 10003
                                            }
                                        },
                                        {
                                            "Flight": {
                                                "FUID": "2",
                                                "VAC": "WY",
                                                "MAC": "WY",
                                                "OAC": "WY",
                                                "Airline": "Oman Aviation|Oman Aviation|Oman Aviation",
                                                "FlightNo": "609",
                                                "ArrivalTime": "2024-10-23T15:40",
                                                "DepartureTime": "2024-10-23T14:30",
                                                "ArrivalCode": "DXB",
                                                "DepartureCode": "MCT",
                                                "Duration": "01h 10m",
                                                "FareBasisCode": "OELOIA",
                                                "ArrAirportName": "Dubai Intl Arpt |Dubai",
                                                "DepAirportName": "Muscat Internatonal Arpt |Muscat",
                                                "RBD": "",
                                                "Cabin": "ECONOMY",
                                                "Refundable": "N"
                                            },
                                            "Fares": {
                                                "GrossFare": 10003,
                                                "NetFare": 10003
                                            }
                                        }
                                    ],
                                    "Offer": "DefaultOffer",
                                    "OrderID": 0,
                                    "GrossFare": 10003,
                                    "NetFare": 10003
                                }
                            ]
                        }
                    ],
                    "AirlineNetFare": 10003,
                    "SSRAmount": 0,
                    "CrossSellAmount": 0,
                    "GrossAmount": 10003,
                    "Hold": false,
                    "ActualHoldTime": 0,
                    "ActualDisplayTime": 0
                },
                "Travellers": [
                    {
                        "ID": 1,
                        "PaxID": 1,
                        "Title": "Mr",
                        "FName": "John",
                        "LName": "Doe",
                        "Age": 35,
                        "DOB": "1989-05-15",
                        "Gender": "M",
                        "PTC": "adult",
                        "PLI": "New York",
                        "PDOE": "2030-12-31",
                        "Nationality": "US",
                        "PassportNo": "*********",
                        "VisaType": null,
                        "DocType": "Passport"
                    }
                ],
                "ContactInfo": {
                    "Title": "Mr",
                    "FName": "John",
                    "LName": "Hnnoo",
                    "Mobile": "*********",
                    "Phone": null,
                    "Email": "<EMAIL>",
                    "Address": "123 Elm Street, New York, NY, USA",
                    "CountryCode": "+91",
                    "MobileCountryCode": "+91",
                    "State": "NY",
                    "City": "New York",
                    "PIN": null,
                    "GSTAddress": null,
                    "GSTCompanyName": null,
                    "GSTTIN": null,
                    "UpdateProfile": false,
                    "IsGuest": true,
                    "SaveGST": false,
                    "Language": null
                }
            }
        headers:
          - name: Content-Type
            value: application/json
            id: pair_5ae91f8ceb40474bb9c50c9eff3004b0
          - name: User-Agent
            value: insomnia/9.3.3
            id: pair_b88c9e3415024bf7bb7676d0a8d1cbbc
          - id: pair_d4c6906dfddc44148991cd7b9cff92f5
            name: Authorization
            value: Bearer
              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.mZvmwgAHVojiQuhdymYniHmHr3SR1SEstYFK4U9p6E0
            disabled: false
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/b2capis/payments/callback/?payment=5071"
        name: Payment Callback
        meta:
          id: req_ffb9f30440da4bb09c6c9fa6fe9dcce5
          created: 1729510219730
          modified: 1729510420210
          isPrivate: false
          sortKey: -1724867992330.8438
        method: POST
        body:
          mimeType: application/json
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.test }}apis/details/"
        name: Details
        meta:
          id: req_107fb4917f144ad89f9824f674d7d441
          created: 1729612917562
          modified: 1747992609912
          isPrivate: false
          sortKey: -1724873914695.4531
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"Trips": [
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1swXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_c88eaa7ff54e45d7928bcc39127e6141', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		},
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1sxXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		}
            	],
            	"ClientID": "",
            	"Mode": "SS",
            	"Options": "A",
            	"Source": "SF",
            	"TripType": "ON"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.test }}apis/service_req/"
        name: Service Request
        meta:
          id: req_52b40b19f99c462f90ff55e531d8ee03
          created: 1729613007296
          modified: 1747992638000
          isPrivate: false
          sortKey: -1724873322458.9922
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"Trips": [
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1swXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_c88eaa7ff54e45d7928bcc39127e6141', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		},
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1sxXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		}
            	],
            	"ClientID": "",
            	"Mode": "SS",
            	"Options": "A",
            	"Source": "SF",
            	"TripType": "ON"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.test }}apis/rules/"
        name: Rules
        meta:
          id: req_767b7960ba80465bacee60b3f5b5269b
          created: 1729613027275
          modified: 1747995760880
          isPrivate: false
          sortKey: -1724873026340.7617
        method: POST
        body:
          mimeType: application/json
          text: >-
            {
            	"Trips": [
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1swXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_c88eaa7ff54e45d7928bcc39127e6141', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		},
            		{
            			"Amount": 14256,
            			"Index": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5Ucmlwc1sxXS5Kb3VybmV5WzBdLkluZGV4::46b', 'never', 60 %}",
            			"ChannelCode": null,
            			"OrderID": 1,
            			"TUI": "{% response 'body', 'req_d150e4dc9a3b478b81a9c77a0560d990', 'b64::JC5UVUk=::46b', 'never', 60 %}"
            		}
            	],
            	"ClientID": "",
            	"Mode": "SS",
            	"Options": "A",
            	"Source": "SF",
            	"TripType": "ON"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/airports"
        name: Airports
        meta:
          id: req_29a2335d858f455291f5a24eb8917ccc
          created: 1729615432518
          modified: 1733984041110
          isPrivate: false
          sortKey: -1724874506931.914
        method: POST
        body:
          mimeType: application/json
          text: |-
            {
            	"search_text": "BLR"
            }
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/get-booking/bf87f017-000b-4245-aca7-198fb0b1feda"
        name: Get booking
        meta:
          id: req_286579b4f98f4464b1c1e37b200a3f73
          created: 1734006793635
          modified: 1748000930214
          isPrivate: false
          sortKey: -1724871545749.6094
        method: GET
        body:
          mimeType: application/json
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/user-bookings/"
        name: get-booking-by-user
        meta:
          id: req_819cc468556c4a08ab6ee49ee0d3c6cc
          created: 1734424135169
          modified: 1734424212934
          isPrivate: false
          sortKey: -1724870953513.1484
        method: GET
        body:
          mimeType: application/json
          text: >-
            {
            	"flight_booking": {
            		"provider_info": {
            			"code": "TJ"
            		},
            		"TUI": "RS6e98eef7-2507-4529-9c12-b5c2ce972a6e|d998824d-8a0d-406a-9b28-3c1c6eee44cc|20241101144209",
            		"Mode": null,
            		"TransactionID": 240710052,
            		"ADT": 1,
            		"CHD": 1,
            		"INF": 0,
            		"NetAmount": 143960.0,
            		"AirlineNetFare": 125162.0,
            		"SSRAmount": 0.0,
            		"CrossSellAmount": 18798.0,
            		"GrossAmount": 125162.0,
            		"Trips": [
            			{
            				"Journey": [
            					{
            						"Provider": "AMN",
            						"Stops": "0",
            						"Offer": "",
            						"OrderID": 0,
            						"GrossFare": 125162.0,
            						"NetFare": 125162.0,
            						"CSBalance": 0.0,
            						"Promo": null,
            						"Segments": [
            							{
            								"Flight": {
            									"FUID": "1",
            									"VAC": "QR",
            									"MAC": "QR",
            									"OAC": "QR",
            									"FareBasisCode": "TJINP6RW",
            									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
            									"Aircraft": "",
            									"FlightNo": "537",
            									"ArrivalTime": "2024-11-10T05:45:00",
            									"DepartureTime": "2024-11-10T03:35:00",
            									"ArrivalCode": "DOH",
            									"DepartureCode": "CCJ",
            									"ArrAirportName": "Doha |Doha |QA |Qatar",
            									"DepAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
            									"ArrivalTerminal": "",
            									"DepartureTerminal": "",
            									"EquipmentType": "",
            									"RBD": "T",
            									"Cabin": "E",
            									"Refundable": "R",
            									"Amenities": null,
            									"Duration": "04h 40m ",
            									"PaxCategory": "",
            									"Hops": null,
            									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
            									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
            									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
            								},
            								"Fares": {
            									"PTCFare": [
            										{
            											"PTC": "ADT",
            											"Fare": 10445.0,
            											"YQ": 21866.0,
            											"PSF": 1191.0,
            											"YR": 0.0,
            											"UD": 0.0,
            											"K3": 0.0,
            											"API": 0.0,
            											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,GB,UB",
            											"OT": "3700.0,850.0,1801.0,2772.0,346.0,346.0,2772.0,462.0,9614.0,5342.0",
            											"Tax": 51062.0,
            											"GrossFare": 61557.0,
            											"NetFare": 61557.0,
            											"ST": 50.00,
            											"CMS": 0.0,
            											"VATonServiceCharge": 0.0,
            											"VATonTransactionFee": 0.0,
            											"AgentMarkup": 0.0,
            											"Markup": 0.0,
            											"AtoCharge": 0.0,
            											"ReissueCharge": 0.0
            										},
            										{
            											"PTC": "CHD",
            											"Fare": 7830.0,
            											"YQ": 21866.0,
            											"PSF": 1191.0,
            											"YR": 0.0,
            											"UD": 0.0,
            											"K3": 0.0,
            											"API": 0.0,
            											"OTT": "YR,IN,K3,G4,PZ,PZ,QA,R9,UB",
            											"OT": "3700.0,850.0,1670.0,2772.0,346.0,346.0,2772.0,462.0,5342.0",
            											"Tax": 41317.0,
            											"GrossFare": 49197.0,
            											"NetFare": 49197.0,
            											"ST": 50.00,
            											"CMS": 0.0,
            											"VATonServiceCharge": 0.0,
            											"VATonTransactionFee": 0.0,
            											"AgentMarkup": 0.0,
            											"Markup": 0.0,
            											"AtoCharge": 0.0,
            											"ReissueCharge": 0.0
            										},
            										{
            											"PTC": "INF",
            											"Fare": 1570.0,
            											"YQ": 3364.0,
            											"PSF": 0.0,
            											"YR": 0.0,
            											"UD": 0.0,
            											"K3": 0.0,
            											"API": 0.0,
            											"OTT": "YR,K3,UB",
            											"OT": "3700.0,432.0,5342.0",
            											"Tax": 12838.0,
            											"GrossFare": 14408.0,
            											"NetFare": 14408.0,
            											"ST": 0.0,
            											"CMS": 0.0,
            											"VATonServiceCharge": 0.0,
            											"VATonTransactionFee": 0.0,
            											"AgentMarkup": 0.0,
            											"Markup": 0.0,
            											"AtoCharge": 0.0,
            											"ReissueCharge": 0.0
            										}
            									],
            									"GrossFare": 125162.0,
            									"NetFare": 125162.0,
            									"TotalServiceTax": 100.00,
            									"TotalBaseFare": 19845.0,
            									"TotalTax": 105317.00,
            									"TotalCommission": 0.0,
            									"TotalVATonServiceCharge": 0.0,
            									"TotalVATonTransactionFee": 0.0,
            									"TotalAgentMarkup": 0.0,
            									"TotalMarkup": 100.00,
            									"TotalAtoCharge": 0.0,
            									"TotalReissueCharge": 0.0,
            									"DealKey": null
            								},
            								"MulticityRefID": null
            							},
            							{
            								"Flight": {
            									"FUID": "2",
            									"VAC": "QR",
            									"MAC": "QR",
            									"OAC": "QR",
            									"FareBasisCode": "TJINP6RW",
            									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
            									"Aircraft": "",
            									"FlightNo": "105",
            									"ArrivalTime": "2024-11-11T07:20:00",
            									"DepartureTime": "2024-11-11T02:45:00",
            									"ArrivalCode": "LHR",
            									"DepartureCode": "DOH",
            									"ArrAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
            									"DepAirportName": "Doha |Doha |QA |Qatar",
            									"ArrivalTerminal": "4",
            									"DepartureTerminal": "",
            									"EquipmentType": "",
            									"RBD": "T",
            									"Cabin": "E",
            									"Refundable": "R",
            									"Amenities": null,
            									"Duration": "06h 35m ",
            									"PaxCategory": "",
            									"Hops": null,
            									"VACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
            									"MACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg",
            									"OACAirlineLogo": "\\Content\\Templates\\images\\AirlineLogo\\QR.jpg"
            								},
            								"Fares": {
            									"PTCFare": [
            									],
            									"GrossFare": 0.0,
            									"NetFare": 0.0,
            									"TotalServiceTax": 0.0,
            									"TotalBaseFare": 0.0,
            									"TotalTax": 0.0,
            									"TotalCommission": 0.0,
            									"TotalVATonServiceCharge": 0.0,
            									"TotalVATonTransactionFee": 0.0,
            									"TotalAgentMarkup": 0.0,
            									"TotalMarkup": 0.0,
            									"TotalAtoCharge": 0.0,
            									"TotalReissueCharge": 0.0,
            									"DealKey": null
            								},
            								"MulticityRefID": null
            							}
            						],
            						"Notices": null
            					}
            				]
            			},
            			{
            				"Journey": [
            					{
            						"Provider": "AMN",
            						"Stops": "0",
            						"Offer": "",
            						"OrderID": 0,
            						"GrossFare": 0.0,
            						"NetFare": 0.0,
            						"CSBalance": 0.0,
            						"Promo": null,
            						"Segments": [
            							{
            								"Flight": {
            									"FUID": "3",
            									"VAC": "QR",
            									"MAC": "QR",
            									"OAC": "BA",
            									"FareBasisCode": "OJINP6RX",
            									"Airline": "Qatar Airways|Qatar Airways|British Airways",
            									"Aircraft": "",
            									"FlightNo": "5942",
            									"ArrivalTime": "2024-11-15T23:10:00",
            									"DepartureTime": "2024-11-15T13:05:00",
            									"ArrivalCode": "DOH",
            									"DepartureCode": "LHR",
            									"ArrAirportName": "Doha |Doha |QA |Qatar",
            									"DepAirportName": "Heathrow |London ( LON ) |GB |United Kingdom",
            									"ArrivalTerminal": "",
            									"DepartureTerminal": "5",
            									"EquipmentType": "",
            									"RBD": "O",
            									"Cabin": "E",
            									"Refundable": "R",
            									"Amenities": null,
            									"Duration": "08h 05m ",
            									"PaxCategory": "",
            									"Hops": null,
            									"VACAirlineLogo": null,
            									"MACAirlineLogo": null,
            									"OACAirlineLogo": null
            								},
            								"Fares": {
            									"PTCFare": [
            									],
            									"GrossFare": 0.0,
            									"NetFare": 0.0,
            									"TotalServiceTax": 0.0,
            									"TotalBaseFare": 0.0,
            									"TotalTax": 0.0,
            									"TotalCommission": 0.0,
            									"TotalVATonServiceCharge": 0.0,
            									"TotalVATonTransactionFee": 0.0,
            									"TotalAgentMarkup": 0.0,
            									"TotalMarkup": 0.0,
            									"TotalAtoCharge": 0.0,
            									"TotalReissueCharge": 0.0,
            									"DealKey": null
            								},
            								"MulticityRefID": null
            							},
            							{
            								"Flight": {
            									"FUID": "4",
            									"VAC": "QR",
            									"MAC": "QR",
            									"OAC": "QR",
            									"FareBasisCode": "OJINP6RX",
            									"Airline": "Qatar Airways|Qatar Airways|Qatar Airways",
            									"Aircraft": "",
            									"FlightNo": "536",
            									"ArrivalTime": "2024-11-17T02:10:00",
            									"DepartureTime": "2024-11-16T19:40:00",
            									"ArrivalCode": "CCJ",
            									"DepartureCode": "DOH",
            									"ArrAirportName": "Kozhikode(Calicut) |Kozhikode |IN |India",
            									"DepAirportName": "Doha |Doha |QA |Qatar",
            									"ArrivalTerminal": "",
            									"DepartureTerminal": "",
            									"EquipmentType": "",
            									"RBD": "O",
            									"Cabin": "E",
            									"Refundable": "R",
            									"Amenities": null,
            									"Duration": "04h 00m ",
            									"PaxCategory": "",
            									"Hops": null,
            									"VACAirlineLogo": null,
            									"MACAirlineLogo": null,
            									"OACAirlineLogo": null
            								},
            								"Fares": {
            									"PTCFare": [
            									],
            									"GrossFare": 0.0,
            									"NetFare": 0.0,
            									"TotalServiceTax": 0.0,
            									"TotalBaseFare": 0.0,
            									"TotalTax": 0.0,
            									"TotalCommission": 0.0,
            									"TotalVATonServiceCharge": 0.0,
            									"TotalVATonTransactionFee": 0.0,
            									"TotalAgentMarkup": 0.0,
            									"TotalMarkup": 0.0,
            									"TotalAtoCharge": 0.0,
            									"TotalReissueCharge": 0.0,
            									"DealKey": null
            								},
            								"MulticityRefID": null
            							}
            						],
            						"Notices": null
            					}
            				]
            			}
            		],
            		"Rules": [
            			{
            				"OrginDestination": "CCJ-DOH",
            				"FUID": "1,2,3,4",
            				"Provider": "AMN",
            				"FareRuleText": null,
            				"Rule": [
            					{
            						"Info": [
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Cancellation"
            					},
            					{
            						"Info": [
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Re Schedule"
            					},
            					{
            						"Info": [
            							{
            								"AdultAmount": "600",
            								"ChildAmount": "600",
            								"InfantAmount": "600",
            								"Description": "Re Schedule",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "600",
            								"ChildAmount": "600",
            								"InfantAmount": "600",
            								"Description": "Cancellation",
            								"RuleText": ""
            							}
            						],
            						"Head": "ATO Service Fee"
            					}
            				],
            				"SpecialInformations": ""
            			},
            			{
            				"OrginDestination": "DOH-LHR",
            				"FUID": "1,2,3,4",
            				"Provider": "AMN",
            				"FareRuleText": null,
            				"Rule": [
            					{
            						"Info": [
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Cancellation"
            					},
            					{
            						"Info": [
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Re Schedule"
            					}
            				],
            				"SpecialInformations": ""
            			},
            			{
            				"OrginDestination": "LHR-DOH",
            				"FUID": "1,2,3,4",
            				"Provider": "AMN",
            				"FareRuleText": null,
            				"Rule": [
            					{
            						"Info": [
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Cancellation"
            					},
            					{
            						"Info": [
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Re Schedule"
            					}
            				],
            				"SpecialInformations": ""
            			},
            			{
            				"OrginDestination": "DOH-CCJ",
            				"FUID": "1,2,3,4",
            				"Provider": "AMN",
            				"FareRuleText": null,
            				"Rule": [
            					{
            						"Info": [
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "Non Refundable",
            								"ChildAmount": "Non Refundable",
            								"InfantAmount": "Cancel Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Cancellation"
            					},
            					{
            						"Info": [
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "No Show",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "After Departure",
            								"RuleText": ""
            							},
            							{
            								"AdultAmount": "10935.0",
            								"ChildAmount": "10935.0",
            								"InfantAmount": "Reissue Permitted",
            								"Description": "Before Departure",
            								"RuleText": ""
            							}
            						],
            						"Head": "Re Schedule"
            					}
            				],
            				"SpecialInformations": ""
            			}
            		],
            		"SSR": [
            			{
            				"PTC": "ADT",
            				"PaxId": "1",
            				"FUID": "1",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "CHD",
            				"PaxId": "2",
            				"FUID": "1",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "INF",
            				"PaxId": "3",
            				"FUID": "1",
            				"Code": "BAG",
            				"Description": "10 Kg,",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "ADT",
            				"PaxId": "1",
            				"FUID": "2",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "CHD",
            				"PaxId": "2",
            				"FUID": "2",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "INF",
            				"PaxId": "3",
            				"FUID": "2",
            				"Code": "BAG",
            				"Description": "10 Kg,",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "ADT",
            				"PaxId": "1",
            				"FUID": "3",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "CHD",
            				"PaxId": "2",
            				"FUID": "3",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "INF",
            				"PaxId": "3",
            				"FUID": "3",
            				"Code": "BAG",
            				"Description": "10 Kg,",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "ADT",
            				"PaxId": "1",
            				"FUID": "4",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "CHD",
            				"PaxId": "2",
            				"FUID": "4",
            				"Code": "BAG",
            				"Description": "25 Kg,7 Kg",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			},
            			{
            				"PTC": "INF",
            				"PaxId": "3",
            				"FUID": "4",
            				"Code": "BAG",
            				"Description": "10 Kg,",
            				"PieceDescription": "",
            				"SSRCategory": "",
            				"Charge": 0.0,
            				"Type": "2",
            				"SSRUrl": null
            			}
            		],
            		"CrossSell": [
            			{
            				"Code": "INS",
            				"TransactionID": 240710054
            			},
            			{
            				"Code": "RPI",
            				"TransactionID": *********
            			}
            		],
            		"Auxiliaries": [
            			{
            				"Code": null,
            				"EmployeeID": null,
            				"Amount": 0.0
            			}
            		],
            		"Hold": false,
            		"ActualHoldTime": 0,
            		"ActualDisplayTime": 0,
            		"CeilingInfo": "R0"
            	},
            	"Code": "200",
            	"Msg": [
            		"Success"
            	],
            	"Travellers": [
            		{
            			"ID": 1,
            			"PaxID": 0,
            			"Operation": "",
            			"Title": "Mr",
            			"FName": "VISHNU",
            			"LName": "K",
            			"Age": 35,
            			"DOB": "1998-10-30",
            			"Country": "",
            			"Gender": "M",
            			"PTC": "ADT",
            			"Nationality": "INDIA",
            			"PassportNo": "a1234567",
            			"PLI": "INDIA",
            			"PDOE": "2030-07-11",
            			"VisaType": "IMMIGRANT VISA",
            			"DefenceID": "",
            			"PaxCategoryID": "",
            			"DocType": "P"
            		}
            	],
            	"ContactInfo": {
            		"Title": "MR",
            		"FName": "VISHNU",
            		"LName": "K",
            		"Mobile": "12345678",
            		"Phone": "1234567",
            		"Email": "<EMAIL>",
            		"Address": "KOCHI",
            		"CountryCode": "IN",
            		"MobileCountryCode": "+91",
            		"State": "KERALA",
            		"City": "KOCHI",
            		"PIN": "673638",
            		"GSTAddress": "AB1234343443",
            		"GSTCompanyName": "Ecogo Software Solutions Private Limited",
            		"GSTTIN": "G3223",
            		"UpdateProfile": false,
            		"IsGuest": true,
            		"SaveGST": false,
            		"Language": "",
            		"GSTaddress": ""
            	}
            }
        headers:
          - name: Content-Type
            value: application/json
            id: pair_18ff4e04b95b44b692c484d77628a651
          - name: User-Agent
            value: insomnia/10.1.1
            id: pair_d1de5c399055406d8110965c08b9631e
          - id: pair_4b50267913be4b3ca791fcc1f72a551b
            name: Authorization
            value: Bearer
              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************.DCA2Zo4t-cILsAYMF5KxzT9p2qd_7eHGZ1WdTKCc70Y
            disabled: false
        authentication:
          type: bearer
          token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************.E2FSYubSuuh4EVi4-7mCoKQLO6OSMumNaiDEorEGqbs
          prefix: "Authorization: Bearer"
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/dashboard/bookings/1797a319-b0a2-4edb-b20a-84ac8981318b"
        name: Get booking from dashboard
        meta:
          id: req_ad357b9a9abd469f8c18c824f60796a6
          created: 1734524326305
          modified: 1734525422418
          isPrivate: false
          sortKey: -1724871249631.379
        method: GET
        body:
          mimeType: application/json
        headers:
          - name: Content-Type
            value: application/json
            id: pair_87aaa6418ed44a518273174b98afa769
          - name: User-Agent
            value: insomnia/9.3.3
            id: pair_287bee8a343e4467bbe70b64dc2f5f10
          - id: pair_a7ead6df92574b6d8a74a7aca427287a
            name: Authorization
            value: Bearer
              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************.PDyX9pZuZFiqOAG2TwRcyraGXS8o4BmUWNuix2r8feo
            disabled: false
        authentication:
          type: bearer
          token: Guest
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/get-all-bookings/"
        name: Get all booking
        meta:
          id: req_ac3f56549d6f42368052c2eeb3314948
          created: 1734593329011
          modified: 1734593359844
          isPrivate: false
          sortKey: -1724871397690.4941
        method: GET
        body:
          mimeType: application/json
        headers:
          - name: Content-Type
            value: application/json
          - name: User-Agent
            value: insomnia/9.3.3
        authentication:
          type: bearer
          token: Guest
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
      - url: "{{ _.base }}apis/dashboard/all-bookings/"
        name: Get all booking from dashboard
        meta:
          id: req_5783a407d57140ec8ee7b0230d295ff5
          created: 1734594897393
          modified: 1734594974683
          isPrivate: false
          sortKey: -1724871101572.2637
        method: GET
        body:
          mimeType: application/json
        headers:
          - name: Content-Type
            value: application/json
            id: pair_87aaa6418ed44a518273174b98afa769
          - name: User-Agent
            value: insomnia/9.3.3
            id: pair_287bee8a343e4467bbe70b64dc2f5f10
          - id: pair_a7ead6df92574b6d8a74a7aca427287a
            name: Authorization
            value: Bearer
              eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************.lif-lQjpsBQlJ4sD0eRBBZCdcFkZJKocn3K-20rcugw
            disabled: false
        authentication:
          type: bearer
          token: Guest
          disabled: true
        settings:
          renderRequestBody: true
          encodeUrl: true
          followRedirects: global
          cookies:
            send: true
            store: true
          rebuildPath: true
cookieJar:
  name: Default Jar
  meta:
    id: jar_75844ffe0b7e4faeb29211caf642cd99
    created: 1724941396931
    modified: 1724941396931
environments:
  name: Base Environment
  meta:
    id: env_db03e51bca6e49b99aefd8357d7a82d6
    created: 1724941396931
    modified: 1729674454792
    isPrivate: false
  data:
    base: http://127.0.0.1:8000/
    test: https://app.digiyatra.in/
    fastapi: http://0.0.0.0:8000/
    fastapi_test: http://198.199.87.142:8000/
