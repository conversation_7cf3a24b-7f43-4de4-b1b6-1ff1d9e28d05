"""
Test script to verify API compliance with YAML specification.
This script tests all endpoints to ensure they match the exact structure defined in the YAML file.
"""

import requests
import json
from typing import Dict, Any

# Base URL for the API
BASE_URL = "http://localhost:8000/apis"

class APIComplianceTest:
    def __init__(self):
        self.base_url = BASE_URL
        self.auth_token = None
        
    def test_auth_endpoints(self):
        """Test authentication endpoints"""
        print("Testing Authentication Endpoints...")
        
        # Test registration
        register_data = {
            "email": "<EMAIL>",
            "name": "Test User",
            "phone_number": "1234567890",
            "phone_country_code": "+1",
            "role": "customer"
        }
        
        try:
            response = requests.post(f"{self.base_url}/auth/register", json=register_data)
            print(f"Register Response: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Register Error: {e}")
        
        # Test login
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword"
        }
        
        try:
            response = requests.post(f"{self.base_url}/auth/login", json=login_data)
            print(f"Login Response: {response.status_code} - {response.text}")
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
        except Exception as e:
            print(f"Login Error: {e}")
    
    def test_flight_search_endpoints(self):
        """Test flight search endpoints"""
        print("\nTesting Flight Search Endpoints...")
        
        # Test search endpoint
        search_data = {
            "SecType": "D",
            "FareType": "REGULAR",
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "ECONOMY",
            "Source": "CF",
            "Mode": "AS",
            "ClientID": "",
            "IsMultipleCarrier": False,
            "IsRefundable": False,
            "preferedAirlines": None,
            "TUI": "",
            "YTH": 0,
            "Trips": [
                {
                    "From": "DEL",
                    "To": "BOM",
                    "OnwardDate": "2024-12-25",
                    "ReturnDate": None,
                    "TUI": ""
                }
            ],
            "Parameters": {
                "Airlines": "",
                "GroupType": "",
                "IsDirect": False,
                "IsNearbyAirport": True,
                "IsStudentFare": False,
                "Refundable": ""
            }
        }
        
        try:
            response = requests.post(f"{self.base_url}/search", json=search_data)
            print(f"Search Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Search Error: {e}")
        
        # Test search_list endpoint
        search_list_data = {
            "ClientID": "",
            "TUI": "test_tui_123"
        }
        
        try:
            response = requests.post(f"{self.base_url}/search_list", json=search_list_data)
            print(f"Search List Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Search List Error: {e}")
    
    def test_pricing_endpoints(self):
        """Test pricing endpoints"""
        print("\nTesting Pricing Endpoints...")
        
        pricing_data = {
            "Trips": [
                {
                    "Amount": 5000.0,
                    "Index": "0",
                    "ChannelCode": None,
                    "OrderID": 1,
                    "TUI": "test_tui_123"
                }
            ],
            "ClientID": "",
            "Mode": "SS",
            "Options": "A",
            "Source": "SF",
            "TripType": "O"
        }
        
        try:
            response = requests.post(f"{self.base_url}/pricing", json=pricing_data)
            print(f"Pricing Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Pricing Error: {e}")
        
        # Test pricing_list endpoint
        pricing_list_data = {
            "TUI": "test_tui_123"
        }
        
        try:
            response = requests.post(f"{self.base_url}/pricing_list", json=pricing_list_data)
            print(f"Pricing List Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Pricing List Error: {e}")
    
    def test_other_endpoints(self):
        """Test other flight endpoints"""
        print("\nTesting Other Flight Endpoints...")
        
        # Test details endpoint
        details_data = {
            "Trips": [
                {
                    "Amount": 5000.0,
                    "Index": "0",
                    "ChannelCode": None,
                    "OrderID": 1,
                    "TUI": "test_tui_123"
                }
            ],
            "ClientID": "",
            "Mode": "SS",
            "Options": "A",
            "Source": "SF",
            "TripType": "O"
        }
        
        try:
            response = requests.post(f"{self.base_url}/details/", json=details_data)
            print(f"Details Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Details Error: {e}")
        
        # Test service_req endpoint
        try:
            response = requests.post(f"{self.base_url}/service_req/", json=details_data)
            print(f"Service Request Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Service Request Error: {e}")
        
        # Test rules endpoint
        try:
            response = requests.post(f"{self.base_url}/rules/", json=details_data)
            print(f"Rules Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Rules Error: {e}")
        
        # Test airports endpoint
        airports_data = {
            "search_text": "Delhi"
        }
        
        try:
            response = requests.post(f"{self.base_url}/airports", json=airports_data)
            print(f"Airports Response: {response.status_code} - {response.text[:200]}...")
        except Exception as e:
            print(f"Airports Error: {e}")
    
    def test_payment_endpoints(self):
        """Test payment endpoints"""
        print("\nTesting Payment Endpoints...")
        
        # Test payment endpoint
        payment_data = {
            "booking_id": "test_booking_123"
        }
        
        try:
            response = requests.post(f"{self.base_url.replace('/apis', '')}/b2capis/payments/v1/", json=payment_data)
            print(f"Payment Response: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Payment Error: {e}")
        
        # Test payment callback endpoint
        try:
            response = requests.post(f"{self.base_url}/b2capis/payments/callback/?payment=test_payment_123")
            print(f"Payment Callback Response: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"Payment Callback Error: {e}")
    
    def run_all_tests(self):
        """Run all compliance tests"""
        print("Starting API Compliance Tests...")
        print("=" * 50)
        
        self.test_auth_endpoints()
        self.test_flight_search_endpoints()
        self.test_pricing_endpoints()
        self.test_other_endpoints()
        self.test_payment_endpoints()
        
        print("\n" + "=" * 50)
        print("API Compliance Tests Completed!")

if __name__ == "__main__":
    tester = APIComplianceTest()
    tester.run_all_tests()
