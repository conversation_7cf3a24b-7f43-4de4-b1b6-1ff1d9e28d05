#!/usr/bin/env python3
"""
Integration script to add enhanced search functionality to the main application.
This script helps integrate the new enhanced search services with the existing API.
"""

import os
import sys
from pathlib import Path


def integrate_enhanced_routes():
    """
    Integrate enhanced search routes into the main API router.
    """
    print("🔧 Integrating Enhanced Search Routes...")
    
    # Path to the main API router
    api_router_path = Path("app/api_router.py")
    
    if not api_router_path.exists():
        print(f"❌ API router file not found: {api_router_path}")
        return False
    
    # Read current content
    with open(api_router_path, 'r') as f:
        content = f.read()
    
    # Check if enhanced routes are already integrated
    if "enhanced_routes" in content:
        print("✅ Enhanced routes already integrated!")
        return True
    
    # Add import for enhanced routes
    import_line = "from app.microservices.flight_service.search_service.enhanced_routes import router as enhanced_search_router\n"
    
    # Find the right place to add the import
    lines = content.split('\n')
    import_added = False
    
    for i, line in enumerate(lines):
        if line.startswith("from app.microservices.flight_service") and not import_added:
            lines.insert(i + 1, import_line.strip())
            import_added = True
            break
    
    if not import_added:
        # Add import after other imports
        for i, line in enumerate(lines):
            if line.startswith("router = APIRouter()"):
                lines.insert(i, import_line.strip())
                lines.insert(i, "")
                break
    
    # Add router inclusion
    router_line = 'router.include_router(enhanced_search_router, prefix="/flight", tags=["enhanced-search"])'
    
    for i, line in enumerate(lines):
        if "include_router" in line and "flight_service_router" in line:
            lines.insert(i + 1, router_line)
            break
    
    # Write back the modified content
    with open(api_router_path, 'w') as f:
        f.write('\n'.join(lines))
    
    print("✅ Enhanced search routes integrated successfully!")
    return True


def update_requirements():
    """
    Update requirements.txt with any new dependencies.
    """
    print("📦 Checking requirements...")
    
    requirements_path = Path("app/requirements.txt")
    
    if not requirements_path.exists():
        print(f"⚠️  Requirements file not found: {requirements_path}")
        return
    
    # New dependencies for enhanced search
    new_deps = [
        "# Enhanced search dependencies",
        "# (Most dependencies should already be included)"
    ]
    
    with open(requirements_path, 'r') as f:
        current_content = f.read()
    
    # Check if we need to add anything
    needs_update = False
    for dep in new_deps:
        if dep not in current_content and not dep.startswith("#"):
            needs_update = True
            break
    
    if needs_update:
        with open(requirements_path, 'a') as f:
            f.write('\n\n')
            f.write('\n'.join(new_deps))
        print("✅ Requirements updated!")
    else:
        print("✅ Requirements are up to date!")


def create_environment_variables():
    """
    Create a sample .env file with enhanced search configurations.
    """
    print("🔧 Creating environment configuration...")
    
    env_sample_path = Path(".env.enhanced_search_sample")
    
    env_content = """
# Enhanced Flight Search Configuration
# Copy these variables to your .env file and adjust as needed

# Cache Configuration (in seconds)
FLIGHT_SEARCH_CACHE_TIMER=2700
FLIGHT_SEARCH_POPULAR_CACHE_TIMER=5400
FLIGHT_SEARCH_HOT_CACHE_TIMER=10800
MEMORY_CACHE_TTL=180
MEMORY_CACHE_HOT_TTL=600
AIRPORT_DATA_CACHE_TIMER=86400

# Performance Configuration
RESPONSE_TIME_TARGET_MS=2000
RESPONSE_TIME_WARNING_MS=1500
MEMORY_CACHE_MAX_SIZE=50000
CONCURRENT_REQUEST_LIMIT=100

# Enhanced Search Features
ENHANCED_SEARCH_ENABLED=true
FUZZY_SEARCH_ENABLED=true
INTELLIGENT_RANKING_ENABLED=true
PREDICTIVE_CACHING_ENABLED=true

# Monitoring and Analytics
ENABLE_PERFORMANCE_METRICS=true
METRICS_RETENTION_DAYS=30
SLOW_QUERY_THRESHOLD_MS=500
CACHE_HIT_RATE_ALERT_THRESHOLD=0.90

# Database Optimization
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_CONNECTION_POOL_MAX_OVERFLOW=30
DATABASE_QUERY_TIMEOUT=30

# Provider Configuration
PROVIDER_REQUEST_TIMEOUT=25
PROVIDER_RETRY_ATTEMPTS=3
PROVIDER_CIRCUIT_BREAKER_ENABLED=true
"""
    
    with open(env_sample_path, 'w') as f:
        f.write(env_content.strip())
    
    print(f"✅ Environment sample created: {env_sample_path}")
    print("📝 Copy these variables to your .env file and adjust as needed")


def run_database_optimization():
    """
    Prompt user to run database optimization.
    """
    print("\n🗄️  Database Optimization Required")
    print("=" * 50)
    print("To get the best performance from enhanced search, run the database optimization script:")
    print()
    print("mysql -u your_username -p your_database < database/optimize_flight_search_indexes.sql")
    print()
    print("This will create optimized indexes for:")
    print("  • Airport search queries")
    print("  • Flight search queries") 
    print("  • Booking data queries")
    print()
    
    response = input("Would you like to see the database optimization commands? (y/n): ")
    if response.lower() == 'y':
        print("\n📋 Database Optimization Commands:")
        print("=" * 40)
        print("# Connect to MySQL")
        print("mysql -u your_username -p")
        print()
        print("# Select your database")
        print("USE your_database_name;")
        print()
        print("# Run the optimization script")
        print("SOURCE database/optimize_flight_search_indexes.sql;")
        print()
        print("# Verify indexes were created")
        print("SHOW INDEX FROM airports;")
        print("SHOW INDEX FROM flights;")
        print()


def run_tests():
    """
    Prompt user to run tests.
    """
    print("\n🧪 Testing Enhanced Search")
    print("=" * 30)
    print("After integration, run these tests to verify everything works:")
    print()
    print("# Test enhanced search functionality")
    print("python test_enhanced_flight_search.py")
    print()
    print("# Test overall performance")
    print("python test_performance_optimization.py")
    print()
    
    response = input("Would you like to run the enhanced search tests now? (y/n): ")
    if response.lower() == 'y':
        print("\n🚀 Running enhanced search tests...")
        try:
            import subprocess
            result = subprocess.run([sys.executable, "test_enhanced_flight_search.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Tests completed successfully!")
                print(result.stdout)
            else:
                print("❌ Tests failed:")
                print(result.stderr)
        except Exception as e:
            print(f"❌ Could not run tests: {e}")
            print("Please run manually: python test_enhanced_flight_search.py")


def main():
    """
    Main integration function.
    """
    print("🚀 Enhanced Flight Search Integration")
    print("=" * 50)
    print("This script will integrate enhanced search functionality into your application.")
    print()
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("❌ Please run this script from the backend directory")
        sys.exit(1)
    
    success = True
    
    # Step 1: Integrate routes
    if not integrate_enhanced_routes():
        success = False
    
    # Step 2: Update requirements
    update_requirements()
    
    # Step 3: Create environment configuration
    create_environment_variables()
    
    # Step 4: Database optimization instructions
    run_database_optimization()
    
    # Step 5: Testing
    run_tests()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Enhanced Search Integration Complete!")
        print()
        print("📋 Next Steps:")
        print("1. Copy .env.enhanced_search_sample variables to your .env file")
        print("2. Run the database optimization script")
        print("3. Restart your application")
        print("4. Test the new endpoints:")
        print("   • POST /flight/enhanced/search")
        print("   • POST /flight/enhanced/airports")
        print("   • GET /flight/enhanced/search/stats")
        print()
        print("🎯 Expected Improvements:")
        print("• Sub-2-second response times")
        print("• 90%+ cache hit rates")
        print("• Enhanced search accuracy")
        print("• Intelligent flight ranking")
        print("• Fuzzy airport search")
    else:
        print("❌ Integration encountered some issues")
        print("Please check the error messages above and try again")
    
    print("\n📚 For detailed documentation, see:")
    print("• FLIGHT_SEARCH_OPTIMIZATION_PLAN.md")
    print("• Database optimization: database/optimize_flight_search_indexes.sql")


if __name__ == "__main__":
    main()
