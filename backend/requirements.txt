# Core FastAPI and Web Framework
fastapi==0.115.4
uvicorn==0.32.0
starlette==0.41.2
h11==0.14.0

# Database and ORM
SQLAlchemy==2.0.36
databases==0.9.0
aiomysql==0.2.0
mysql-connector-python==9.0.0
PyMySQL==1.1.1
alembic==1.13.1

# Authentication and Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
bcrypt==4.1.2

# Async HTTP Client
aiohttp==3.9.1

# Caching and Message Queue
redis==5.2.0
celery==5.4.0

# Data Validation and Serialization
pydantic==2.9.2
pydantic_core==2.23.4
marshmallow==3.22.0
marshmallow-sqlalchemy==1.1.0
email_validator==2.2.0

# Environment and Configuration
python-dotenv==1.0.1

# HTTP Requests
requests==2.32.3

# WebSocket Support (for real-time features)
websockets==12.0

# Date and Time Utilities
python-dateutil==2.9.0.post0
tzdata==2024.2

# Async and Concurrency
anyio==4.5.2
async-timeout>=4.0,<5.0

# Celery Dependencies
amqp==5.2.0
billiard==4.2.1
kombu==5.4.2
vine==5.1.0

# CLI and Terminal
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
prompt_toolkit==3.0.48
wcwidth==0.2.13

# Type Annotations and Utilities
typing_extensions==4.12.2
annotated-types==0.7.0

# Network and DNS
dnspython==2.6.1
certifi==2024.8.30
charset-normalizer==3.4.0
idna==3.10
urllib3==2.2.3

# Utilities
six==1.16.0
sniffio==1.3.1
packaging==24.1
exceptiongroup==1.2.2

# Optional: Machine Learning (for predictive analytics)
scikit-learn==1.3.2
numpy==1.24.4
pandas==2.1.4

# Optional: Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
