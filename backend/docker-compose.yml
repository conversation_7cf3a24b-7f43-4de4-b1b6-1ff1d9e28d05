version: '3.8'

services:
  fastapi:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
    environment:
      - DATABASE_HOST=mysql
      - DATABASE_USER=root
      - DATABASE_PASSWORD=Vishnu123
      - DATABASE_NAME=tdb
      - AUTH_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@mysql/tdb_auth
      - FLIGHT_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@mysql/tdb_flight
      - BOOKING_SERVICE_DATABASE_URL=mysql+aiomysql://root:Vishnu123@mysql/tdb_booking

  redis:
    image: "redis:6-alpine"
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: Vishnu123
    ports:
      - "3306:3306"
    volumes:
      - ./database/init_all_databases.sql:/docker-entrypoint-initdb.d/init_all.sql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  celery_worker:
    build: .
    command: celery -A app.config worker --loglevel=info --pool=solo
    depends_on:
      redis:
        condition: service_healthy
