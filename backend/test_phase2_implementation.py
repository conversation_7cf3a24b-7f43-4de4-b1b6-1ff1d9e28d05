#!/usr/bin/env python3
"""
Test script for Phase 2 implementation verification.
Tests async services, circuit breaker, request deduplication, and cache warming.
"""

import asyncio
import aiohttp
import time
import json
from typing import Dict, Any, List


class Phase2Tester:
    """Test suite for Phase 2 async implementation."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: aiohttp.ClientSession = None
        self.test_results = []
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request to the API."""
        url = f"{self.base_url}/apis{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await response.json()
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await response.json()
        except Exception as e:
            return {"error": str(e), "status": "failed"}
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {details}")
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    async def test_async_search_performance(self):
        """Test async search performance."""
        print("\n🔍 Testing Async Search Performance...")
        
        search_request = {
            "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-01-15"}],
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "E",
            "FareType": "REGULAR"
        }
        
        # Test async search
        start_time = time.time()
        result = await self.make_request("POST", "/async/search", search_request)
        response_time = time.time() - start_time
        
        success = "error" not in result and response_time < 5.0
        self.log_test_result(
            "Async Search Response Time",
            success,
            f"{response_time:.2f}s - {'Fast' if response_time < 2.0 else 'Acceptable' if response_time < 5.0 else 'Slow'}"
        )
        
        # Check for async-specific metadata
        has_metadata = all(key in result for key in ["response_time_ms", "data_source", "cache_hit"])
        self.log_test_result(
            "Async Search Metadata",
            has_metadata,
            "Contains performance metadata" if has_metadata else "Missing metadata"
        )
        
        return result.get("TUI") if "TUI" in result else None
    
    async def test_request_deduplication(self):
        """Test request deduplication functionality."""
        print("\n🔄 Testing Request Deduplication...")
        
        search_request = {
            "Trips": [{"From": "BLR", "To": "DEL", "OnwardDate": "2024-01-16"}],
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "E",
            "FareType": "REGULAR"
        }
        
        # Make 5 identical requests simultaneously
        tasks = []
        for i in range(5):
            task = self.make_request("POST", "/async/search", search_request)
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Check if requests were deduplicated (should be faster than 5 individual requests)
        expected_time = 5 * 2.0  # 5 requests * 2 seconds each
        deduplication_effective = total_time < expected_time
        
        self.log_test_result(
            "Request Deduplication",
            deduplication_effective,
            f"5 requests in {total_time:.2f}s - {'Deduplicated' if deduplication_effective else 'Not deduplicated'}"
        )
        
        # Check deduplication stats
        dedup_stats = await self.make_request("GET", "/async/performance/deduplication")
        has_dedup_stats = "performance_stats" in dedup_stats
        
        self.log_test_result(
            "Deduplication Statistics",
            has_dedup_stats,
            f"Deduplication rate: {dedup_stats.get('performance_stats', {}).get('deduplication_rate', 0):.2%}" if has_dedup_stats else "No stats available"
        )
    
    async def test_circuit_breaker_status(self):
        """Test circuit breaker functionality."""
        print("\n⚡ Testing Circuit Breaker Status...")
        
        # Get provider performance stats
        provider_stats = await self.make_request("GET", "/async/performance/provider")
        
        has_circuit_breaker = "performance_stats" in provider_stats and "circuit_breakers" in provider_stats["performance_stats"]
        
        if has_circuit_breaker:
            circuit_breakers = provider_stats["performance_stats"]["circuit_breakers"]
            all_closed = all(cb.get("state") == "closed" for cb in circuit_breakers.values())
            
            self.log_test_result(
                "Circuit Breaker Status",
                all_closed,
                f"All circuit breakers closed: {all_closed}"
            )
            
            # Log individual circuit breaker states
            for name, cb in circuit_breakers.items():
                state = cb.get("state", "unknown")
                failure_rate = cb.get("failure_rate", 0)
                print(f"  - {name}: {state} (failure rate: {failure_rate:.2%})")
        else:
            self.log_test_result(
                "Circuit Breaker Status",
                False,
                "Circuit breaker stats not available"
            )
    
    async def test_cache_warming(self):
        """Test cache warming functionality."""
        print("\n🔥 Testing Cache Warming...")
        
        # Get popular routes
        popular_routes = await self.make_request("GET", "/async/popular-routes?limit=5")
        
        has_popular_routes = "popular_routes" in popular_routes
        self.log_test_result(
            "Popular Routes Detection",
            has_popular_routes,
            f"Found {len(popular_routes.get('popular_routes', []))} popular routes" if has_popular_routes else "No popular routes found"
        )
        
        # Trigger cache warming
        warming_request = {
            "routes": [],
            "max_routes": 3,
            "future_days": 1
        }
        
        warming_result = await self.make_request("POST", "/async/cache/warm", warming_request)
        warming_success = warming_result.get("status") == "success"
        
        self.log_test_result(
            "Cache Warming Trigger",
            warming_success,
            warming_result.get("message", "Unknown result")
        )
        
        # Check warming stats
        warming_stats = await self.make_request("GET", "/async/performance/warming")
        has_warming_stats = "performance_stats" in warming_stats
        
        self.log_test_result(
            "Cache Warming Statistics",
            has_warming_stats,
            f"Warming tasks: {warming_stats.get('performance_stats', {}).get('warming_tasks', 0)}" if has_warming_stats else "No warming stats"
        )
    
    async def test_async_detail_service(self):
        """Test async detail service."""
        print("\n📋 Testing Async Detail Service...")
        
        # First, we need a fare ID from a search
        search_request = {
            "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": "2024-01-15"}],
            "ADT": 1,
            "CHD": 0,
            "INF": 0,
            "Cabin": "E",
            "FareType": "REGULAR"
        }
        
        search_result = await self.make_request("POST", "/async/search", search_request)
        
        # For testing purposes, use a mock fare ID
        detail_request = {
            "FareId": "test_fare_id_123",
            "ADT": 1,
            "CHD": 0,
            "INF": 0
        }
        
        start_time = time.time()
        detail_result = await self.make_request("POST", "/async/pricing", detail_request)
        response_time = time.time() - start_time
        
        # Check if detail service responds (even with error for mock data)
        detail_responsive = "error" not in detail_result or response_time < 5.0
        
        self.log_test_result(
            "Async Detail Service",
            detail_responsive,
            f"Response time: {response_time:.2f}s"
        )
        
        # Check detail service performance stats
        detail_stats = await self.make_request("GET", "/async/performance/detail")
        has_detail_stats = "performance_stats" in detail_stats
        
        self.log_test_result(
            "Detail Service Statistics",
            has_detail_stats,
            f"Total requests: {detail_stats.get('performance_stats', {}).get('total_requests', 0)}" if has_detail_stats else "No detail stats"
        )
    
    async def test_health_check(self):
        """Test comprehensive health check."""
        print("\n🏥 Testing Health Check...")
        
        health_result = await self.make_request("GET", "/async/health")
        
        overall_healthy = health_result.get("overall_status") in ["healthy", "degraded"]
        services_count = len(health_result.get("services", {}))
        
        self.log_test_result(
            "Health Check Response",
            overall_healthy,
            f"Status: {health_result.get('overall_status', 'unknown')} - {services_count} services monitored"
        )
        
        # Log individual service health
        if "services" in health_result:
            for service_name, service_health in health_result["services"].items():
                status = service_health.get("status", "unknown")
                print(f"  - {service_name}: {status}")
        
        # Log any issues
        issues = health_result.get("issues", [])
        if issues:
            print(f"  Issues detected: {', '.join(issues)}")
    
    async def run_all_tests(self):
        """Run all Phase 2 tests."""
        print("🚀 Starting Phase 2 Implementation Tests...\n")
        
        # Run tests
        await self.test_async_search_performance()
        await self.test_request_deduplication()
        await self.test_circuit_breaker_status()
        await self.test_cache_warming()
        await self.test_async_detail_service()
        await self.test_health_check()
        
        # Summary
        print("\n📊 Test Summary:")
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"Passed: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 All tests passed! Phase 2 implementation is working correctly.")
        elif passed >= total * 0.8:
            print("⚠️  Most tests passed. Some features may need attention.")
        else:
            print("❌ Several tests failed. Phase 2 implementation needs review.")
        
        return passed, total


async def main():
    """Main test function."""
    async with Phase2Tester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
