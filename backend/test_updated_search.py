import requests
import json

def test_updated_flight_search():
    """Test the updated flight search with TripJack integration"""

    url = 'http://localhost:8000/apis/search'
    headers = {'Content-Type': 'application/json'}

    payload = {
        'Cabin': 'E',  # Use the correct cabin code
        'FareType': 'REGULAR',  # Required field
        'ADT': 1,
        'CHD': 0,
        'INF': 0,
        'Trips': [
            {
                'From': 'DEL',
                'To': 'BOM',
                'OnwardDate': '2025-06-25'
            }
        ]
    }

    print('Testing updated flight search with TripJack...')
    print(f'Payload: {json.dumps(payload, indent=2)}')
    print('-' * 50)

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=90)
        print(f'Status Code: {response.status_code}')
        result = response.json()
        print(f'Response keys: {list(result.keys())}')

        if 'Trips' in result and result['Trips']:
            print(f'✅ Found {len(result["Trips"])} trips')
            if result['Trips'][0].get('Journey'):
                print(f'✅ First trip has {len(result["Trips"][0]["Journey"])} journeys')
        else:
            print('❌ No trips found in response')

        if 'Notices' in result and result['Notices']:
            print(f'📋 Notices: {result["Notices"]}')

        if 'NoFlightsMessage' in result:
            print(f'📋 No Flights Message: {result["NoFlightsMessage"]}')

        # Check if we got real flight data vs error
        if 'TUI' in result and result.get('TUI'):
            print(f'✅ TUI: {result["TUI"]}')

        return result

    except requests.exceptions.Timeout:
        print('❌ Request timed out')
    except Exception as e:
        print(f'❌ Error: {e}')
        return None

if __name__ == "__main__":
    test_updated_flight_search()
