# Flight Booking API - Complete Testing Guide

## 📁 **Collection Files Overview**

### **Available Collections**
1. **`Flight_Booking_API_Postman_Collection.json`** - Complete Postman collection (115+ endpoints)
2. **`Flight_Booking_API_Insomnia_Collection.json`** - Complete Insomnia collection (115+ endpoints)
3. **`openapi_specification.yaml`** - OpenAPI 3.0 specification for interactive docs

## 🚀 **Quick Setup Instructions**

### **For Postman**
1. **Import Collection**:
   - Open Postman
   - Click "Import" → "Upload Files"
   - Select `Flight_Booking_API_Postman_Collection.json`
   - Collection will be imported with all 115+ endpoints organized in folders

2. **Environment Setup**:
   - The collection includes pre-configured variables:
     - `base_url`: `http://localhost:8000/apis`
     - `api_key`: `test-api-key-12345`
     - `saved_tui`: Auto-populated from responses
     - `saved_fare_id`: Auto-populated from responses

3. **Start Testing**:
   - Ensure your FastAPI server is running: `uvicorn app.main:app --reload`
   - Run requests in order for best results

### **For Insomnia**
1. **Import Collection**:
   - Open Insomnia
   - Click "Create" → "Import From" → "File"
   - Select `Flight_Booking_API_Insomnia_Collection.json`
   - Collection will be imported with organized request groups

2. **Environment Variables**:
   - Base environment is pre-configured with:
     - `base_url`: `http://localhost:8000/apis`
     - `api_key`: `test-api-key-12345`
     - `future_date`: `2024-02-15`

3. **Start Testing**:
   - Ensure your FastAPI server is running
   - Use environment variables with `{{ _.variable_name }}` syntax

## 📋 **Testing Workflow**

### **Phase 1: Core Flight Services**
```
1. Flight Search → Saves TUI and FareId automatically
2. Flight Search List → Uses saved TUI to retrieve results
3. Flight Pricing → Uses saved FareId for detailed pricing
4. Round Trip Search → Test multi-segment flights
```

### **Phase 2: Enhanced Async Operations**
```
1. Async Flight Search → Test async processing
2. Async Flight Pricing → Test circuit breaker protection
3. Search Service Performance → Monitor performance metrics
4. Provider Performance → Check circuit breaker status
```

### **Phase 3: Cache Management (Admin)**
```
1. Cache Statistics → Check current cache performance
2. Cache Warming → Trigger intelligent cache warming
3. Cache Invalidation → Clear specific cache entries
4. Cache Health Check → Verify cache system health
```

### **Phase 4: Database & Analytics (Admin)**
```
1. Database Statistics → Check connection pool performance
2. Analytics Dashboard → View comprehensive analytics
3. Optimized Booking Creation → Test batch processing
4. Database Query Execution → Run optimized queries
```

### **Phase 5: Advanced Monitoring (Admin)**
```
1. Comprehensive System Status → Overall system health
2. Dashboard Service Statistics → WebSocket metrics
3. Generate Reports → Automated reporting
4. Predictive Analytics → ML-based forecasting
```

## 🔧 **Environment Configuration**

### **Local Development**
```json
{
  "base_url": "http://localhost:8000/apis",
  "api_key": "test-api-key-12345",
  "saved_tui": "",
  "saved_fare_id": "",
  "future_date": "2024-02-15"
}
```

### **Staging Environment**
```json
{
  "base_url": "https://staging-api.flightbooking.com/v4",
  "api_key": "staging-api-key-67890",
  "saved_tui": "",
  "saved_fare_id": "",
  "future_date": "2024-02-15"
}
```

### **Production Environment**
```json
{
  "base_url": "https://api.flightbooking.com/v4",
  "api_key": "production-api-key-abcdef",
  "saved_tui": "",
  "saved_fare_id": "",
  "future_date": "2024-02-15"
}
```

## 📊 **Expected Response Patterns**

### **Successful Flight Search Response**
```json
{
  "TUI": "SEARCH_12345_20240115_143022",
  "status": "success",
  "data_source": "cache",
  "cache_hit": true,
  "response_time_ms": 45.2,
  "Results": [
    {
      "FlightId": "FL001_DEL_BOM_20240215",
      "FareId": "FARE_001_E_REG",
      "Airline": "AI",
      "FlightNumber": "AI131",
      "Price": {
        "TotalFare": 10000,
        "Currency": "INR"
      }
    }
  ]
}
```

### **Cache Statistics Response**
```json
{
  "overall_performance": {
    "hit_rate_percentage": 94.5,
    "total_requests": 125000,
    "performance_score": 96
  },
  "cache_layers": {
    "L1_memory": {
      "hit_rate_percentage": 85.2,
      "avg_response_time_ms": 0.8
    },
    "L2_redis": {
      "hit_rate_percentage": 15.2,
      "avg_response_time_ms": 3.2
    }
  }
}
```

### **Performance Metrics Response**
```json
{
  "service": "search_service",
  "performance_stats": {
    "total_requests": 45000,
    "success_rate": 99.0,
    "avg_response_time_ms": 245,
    "cache_hit_rate": 87.2,
    "circuit_breaker_status": "CLOSED"
  }
}
```

## 🧪 **Testing Scenarios**

### **Scenario 1: Basic Flight Booking Flow**
1. **Search Flights** (DEL → BOM)
2. **Retrieve Results** using TUI
3. **Get Pricing** for selected fare
4. **Create Optimized Booking**

### **Scenario 2: Performance Testing**
1. **Check Cache Statistics** (baseline)
2. **Warm Cache** for popular routes
3. **Search Flights** (should hit cache)
4. **Check Cache Statistics** (improved)

### **Scenario 3: Monitoring & Analytics**
1. **System Status** check
2. **Generate Daily Report**
3. **Predictive Analytics** forecast
4. **Performance Metrics** review

### **Scenario 4: Error Handling**
1. **Invalid Search** (empty trips)
2. **Missing API Key**
3. **Invalid TUI** retrieval
4. **Rate Limiting** test

## 🔍 **Response Headers to Monitor**

### **Performance Headers**
```http
X-Cache-Status: HIT|MISS|REFRESH
X-Response-Time: 45
X-Performance-Score: 95
X-Circuit-Breaker-Status: CLOSED|OPEN|HALF_OPEN
X-Request-Deduplication: true|false
```

### **Rate Limiting Headers**
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1642251600
Retry-After: 3600
```

### **Cache Headers**
```http
X-Cache-Layer: L1_MEMORY|L2_REDIS|L3_PERSISTENT
Cache-Control: max-age=300
ETag: "cache-version-123"
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: Server Not Running**
```
Error: Connection refused
Solution: Start server with `uvicorn app.main:app --reload`
```

### **Issue 2: Invalid API Key**
```
Response: 401 Unauthorized
Solution: Check X-API-Key header value
```

### **Issue 3: TUI Not Found**
```
Response: 404 Not Found
Solution: Run flight search first to generate TUI
```

### **Issue 4: Cache Statistics Error**
```
Error: 'FlightCacheService' object has no attribute 'get_cache_statistics'
Solution: Ensure latest code with fixed cache service
```

## 📈 **Performance Benchmarks**

### **Response Time Targets**
- **Cache Hit**: < 100ms
- **Cache Miss**: < 1000ms
- **Database Query**: < 500ms
- **Provider Call**: < 3000ms

### **Cache Performance Targets**
- **Hit Rate**: > 90%
- **Memory Usage**: < 80%
- **Warming Effectiveness**: > 95%

### **System Health Targets**
- **Uptime**: > 99.9%
- **Error Rate**: < 1%
- **Circuit Breaker**: CLOSED status
- **Request Success Rate**: > 99%

## 🔧 **Advanced Testing Features**

### **Postman Features**
- **Pre-request Scripts**: Auto-generate timestamps and test data
- **Test Scripts**: Validate responses and save variables
- **Collection Runner**: Run entire test suites
- **Monitor**: Schedule automated testing

### **Insomnia Features**
- **Environment Variables**: Dynamic request configuration
- **Code Generation**: Generate client code
- **Plugin Support**: Extend functionality
- **GraphQL Support**: For future GraphQL endpoints

### **WebSocket Testing**
For real-time dashboard testing, use browser console:
```javascript
const ws = new WebSocket('ws://localhost:8000/apis/advanced/dashboard/realtime');
ws.onopen = () => ws.send(JSON.stringify({type: 'subscribe', subscription: 'system_metrics'}));
ws.onmessage = (event) => console.log('Received:', JSON.parse(event.data));
```

## 📞 **Support & Troubleshooting**

### **Debug Mode**
Add debug headers to requests:
```http
X-Debug-Mode: true
X-Trace-Id: custom-trace-id
```

### **Logging**
Monitor server logs for detailed error information:
```bash
tail -f logs/app.log
```

### **Health Checks**
Use health check endpoints to verify system status:
```
GET /advanced/monitoring/comprehensive-status
GET /cache/health
GET /admin/database/stats
```

This comprehensive testing guide provides everything needed to test all 115+ endpoints across the flight booking optimization system using either Postman or Insomnia collections.
