# Enhanced Flight Search Configuration
# Copy these variables to your .env file and adjust as needed

# Cache Configuration (in seconds)
FLIGHT_SEARCH_CACHE_TIMER=2700
FLIGHT_SEARCH_POPULAR_CACHE_TIMER=5400
FLIGHT_SEARCH_HOT_CACHE_TIMER=10800
MEMORY_CACHE_TTL=180
MEMORY_CACHE_HOT_TTL=600
AIRPORT_DATA_CACHE_TIMER=86400

# Performance Configuration
RESPONSE_TIME_TARGET_MS=2000
RESPONSE_TIME_WARNING_MS=1500
MEMORY_CACHE_MAX_SIZE=50000
CONCURRENT_REQUEST_LIMIT=100

# Enhanced Search Features
ENHANCED_SEARCH_ENABLED=true
FUZZY_SEARCH_ENABLED=true
INTELLIGENT_RANKING_ENABLED=true
PREDICTIVE_CACHING_ENABLED=true

# Monitoring and Analytics
ENABLE_PERFORMANCE_METRICS=true
METRICS_RETENTION_DAYS=30
SLOW_QUERY_THRESHOLD_MS=500
CACHE_HIT_RATE_ALERT_THRESHOLD=0.90

# Database Optimization
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_CONNECTION_POOL_MAX_OVERFLOW=30
DATABASE_QUERY_TIMEOUT=30

# Provider Configuration
PROVIDER_REQUEST_TIMEOUT=25
PROVIDER_RETRY_ATTEMPTS=3
PROVIDER_CIRCUIT_BREAKER_ENABLED=true