#!/usr/bin/env python3
"""
Simple integration test to verify enhanced search services are properly integrated.
This test doesn't require <PERSON><PERSON> and can run with basic Python imports.
"""

import sys
import os
import traceback
from datetime import datetime

def test_imports():
    """Test if all enhanced services can be imported successfully."""
    print("🧪 Testing Enhanced Search Integration")
    print("=" * 50)
    
    results = {
        "imports": {},
        "api_router": False,
        "overall_status": "unknown"
    }
    
    # Test 1: Enhanced Routes Import
    try:
        from app.microservices.flight_service.search_service.enhanced_routes import router
        results["imports"]["enhanced_routes"] = True
        print("✅ Enhanced routes imported successfully")
    except Exception as e:
        results["imports"]["enhanced_routes"] = False
        print(f"❌ Enhanced routes import failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
    
    # Test 2: Enhanced Flight Search Service Import
    try:
        from app.microservices.flight_service.search_service.enhanced_flight_search import enhanced_flight_search_service
        results["imports"]["enhanced_flight_search"] = True
        print("✅ Enhanced flight search service imported successfully")
    except Exception as e:
        results["imports"]["enhanced_flight_search"] = False
        print(f"❌ Enhanced flight search service import failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
    
    # Test 3: Enhanced Airport Search Service Import
    try:
        from app.microservices.flight_service.shared_service.enhanced_airport_service import enhanced_airport_search
        results["imports"]["enhanced_airport_search"] = True
        print("✅ Enhanced airport search service imported successfully")
    except Exception as e:
        results["imports"]["enhanced_airport_search"] = False
        print(f"❌ Enhanced airport search service import failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
    
    # Test 4: API Router Integration
    try:
        from app.api_router import api_router
        # Check if enhanced routes are included
        route_paths = [route.path for route in api_router.routes]
        enhanced_routes_found = any("/flight/enhanced" in path for path in route_paths)
        
        if enhanced_routes_found:
            results["api_router"] = True
            print("✅ Enhanced routes integrated into API router")
        else:
            results["api_router"] = False
            print("❌ Enhanced routes not found in API router")
            print(f"   Available routes: {route_paths}")
            
    except Exception as e:
        results["api_router"] = False
        print(f"❌ API router integration test failed: {e}")
        print(f"   Traceback: {traceback.format_exc()}")
    
    # Test 5: Schema Imports
    try:
        from app.microservices.flight_service.schemas import FlightSearchRequest, AirportSearchRequest
        results["imports"]["schemas"] = True
        print("✅ Required schemas imported successfully")
    except Exception as e:
        results["imports"]["schemas"] = False
        print(f"❌ Schema imports failed: {e}")
    
    return results

def test_service_initialization():
    """Test if services can be initialized without errors."""
    print("\n🔧 Testing Service Initialization")
    print("=" * 40)
    
    initialization_results = {}
    
    # Test Enhanced Flight Search Service
    try:
        from app.microservices.flight_service.search_service.enhanced_flight_search import enhanced_flight_search_service
        stats = enhanced_flight_search_service.get_performance_stats()
        initialization_results["flight_search"] = True
        print("✅ Enhanced flight search service initialized")
        print(f"   Initial stats: {stats}")
    except Exception as e:
        initialization_results["flight_search"] = False
        print(f"❌ Enhanced flight search service initialization failed: {e}")
    
    # Test Enhanced Airport Search Service
    try:
        from app.microservices.flight_service.shared_service.enhanced_airport_service import enhanced_airport_search
        stats = enhanced_airport_search.get_search_stats()
        initialization_results["airport_search"] = True
        print("✅ Enhanced airport search service initialized")
        print(f"   Initial stats: {stats}")
    except Exception as e:
        initialization_results["airport_search"] = False
        print(f"❌ Enhanced airport search service initialization failed: {e}")
    
    return initialization_results

def test_route_definitions():
    """Test if route definitions are correct."""
    print("\n🛣️  Testing Route Definitions")
    print("=" * 35)
    
    route_results = {}
    
    try:
        from app.microservices.flight_service.search_service.enhanced_routes import router
        
        # Get all routes from the enhanced router
        routes = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append({
                    "path": route.path,
                    "methods": list(route.methods),
                    "name": getattr(route, 'name', 'unknown')
                })
        
        expected_routes = [
            "/enhanced/search",
            "/enhanced/airports", 
            "/enhanced/search/stats",
            "/enhanced/search/benchmark",
            "/enhanced/search/health"
        ]
        
        found_routes = [route["path"] for route in routes]
        
        for expected_route in expected_routes:
            if expected_route in found_routes:
                route_results[expected_route] = True
                print(f"✅ Route found: {expected_route}")
            else:
                route_results[expected_route] = False
                print(f"❌ Route missing: {expected_route}")
        
        print(f"\n📋 Total routes defined: {len(routes)}")
        for route in routes:
            print(f"   • {route['methods']} {route['path']}")
            
    except Exception as e:
        print(f"❌ Route definition test failed: {e}")
        route_results["error"] = str(e)
    
    return route_results

def generate_summary(import_results, init_results, route_results):
    """Generate a summary of all test results."""
    print("\n" + "=" * 50)
    print("📋 INTEGRATION TEST SUMMARY")
    print("=" * 50)
    
    # Count successes
    import_successes = sum(1 for v in import_results["imports"].values() if v)
    total_imports = len(import_results["imports"])
    
    init_successes = sum(1 for v in init_results.values() if v)
    total_inits = len(init_results)
    
    route_successes = sum(1 for k, v in route_results.items() if k != "error" and v)
    total_routes = len([k for k in route_results.keys() if k != "error"])
    
    print(f"📦 Import Tests: {import_successes}/{total_imports} passed")
    print(f"🔧 Initialization Tests: {init_successes}/{total_inits} passed")
    print(f"🛣️  Route Tests: {route_successes}/{total_routes} passed")
    print(f"🔗 API Router Integration: {'✅' if import_results['api_router'] else '❌'}")
    
    # Overall status
    all_imports_passed = import_successes == total_imports
    all_inits_passed = init_successes == total_inits
    all_routes_passed = route_successes == total_routes
    api_router_passed = import_results["api_router"]
    
    if all_imports_passed and all_inits_passed and all_routes_passed and api_router_passed:
        status = "✅ ALL TESTS PASSED - Integration Successful!"
        next_steps = [
            "Start the Docker services: docker-compose up -d",
            "Test the enhanced endpoints:",
            "  • POST /flight/enhanced/search",
            "  • POST /flight/enhanced/airports", 
            "  • GET /flight/enhanced/search/stats",
            "Run the comprehensive test suite: python test_enhanced_flight_search.py"
        ]
    else:
        status = "⚠️  SOME TESTS FAILED - Check errors above"
        next_steps = [
            "Fix import errors if any",
            "Ensure all dependencies are installed",
            "Check file paths and module structure",
            "Retry integration after fixes"
        ]
    
    print(f"\n🎯 Overall Status: {status}")
    print(f"\n📝 Next Steps:")
    for step in next_steps:
        print(f"   • {step}")
    
    return {
        "timestamp": datetime.now().isoformat(),
        "import_results": import_results,
        "initialization_results": init_results,
        "route_results": route_results,
        "summary": {
            "imports_passed": f"{import_successes}/{total_imports}",
            "inits_passed": f"{init_successes}/{total_inits}",
            "routes_passed": f"{route_successes}/{total_routes}",
            "api_router_integrated": api_router_passed,
            "overall_success": all_imports_passed and all_inits_passed and all_routes_passed and api_router_passed
        }
    }

def main():
    """Main test execution."""
    print(f"🚀 Enhanced Flight Search Integration Test")
    print(f"📅 Started at: {datetime.now().isoformat()}")
    print(f"🐍 Python version: {sys.version}")
    print(f"📁 Working directory: {os.getcwd()}")
    print()
    
    # Run all tests
    import_results = test_imports()
    init_results = test_service_initialization()
    route_results = test_route_definitions()
    
    # Generate summary
    summary = generate_summary(import_results, init_results, route_results)
    
    # Save results
    try:
        import json
        with open("integration_test_results.json", "w") as f:
            json.dump(summary, f, indent=2, default=str)
        print(f"\n📄 Detailed results saved to: integration_test_results.json")
    except Exception as e:
        print(f"\n⚠️  Could not save results: {e}")
    
    print(f"\n✅ Integration test completed!")
    
    # Return exit code based on success
    return 0 if summary["summary"]["overall_success"] else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
