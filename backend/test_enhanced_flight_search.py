#!/usr/bin/env python3
"""
Enhanced Flight Search Performance Test Suite
Tests the new enhanced search algorithms and optimization improvements.
"""

import asyncio
import aiohttp
import time
import json
import statistics
from typing import Dict, Any, List
from datetime import datetime, timedelta


class EnhancedSearchTestSuite:
    """Test suite for enhanced flight search functionality."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
        # Test scenarios
        self.test_scenarios = [
            {
                "name": "Popular Route - DEL to BOM",
                "request": {
                    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")}],
                    "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "E", "FareType": "REGULAR"
                }
            },
            {
                "name": "Secondary Route - BLR to HYD",
                "request": {
                    "Trips": [{"From": "BLR", "To": "HYD", "OnwardDate": (datetime.now() + timedelta(days=45)).strftime("%Y-%m-%d")}],
                    "ADT": 2, "CHD": 1, "INF": 0, "Cabin": "E", "FareType": "REGULAR"
                }
            },
            {
                "name": "Premium Route - DEL to BLR",
                "request": {
                    "Trips": [{"From": "DEL", "To": "BLR", "OnwardDate": (datetime.now() + timedelta(days=60)).strftime("%Y-%m-%d")}],
                    "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "B", "FareType": "PREMIUM"
                }
            }
        ]
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def test_enhanced_search_performance(self) -> Dict[str, Any]:
        """Test enhanced search endpoint performance across multiple scenarios."""
        print("🚀 Testing Enhanced Flight Search Performance...")
        
        results = {
            "test_name": "enhanced_search_performance",
            "timestamp": datetime.now().isoformat(),
            "scenarios": [],
            "summary": {}
        }
        
        all_response_times = []
        cache_hits = 0
        total_requests = 0
        
        for scenario in self.test_scenarios:
            print(f"  📊 Testing: {scenario['name']}")
            
            scenario_results = await self._test_scenario(scenario)
            results["scenarios"].append(scenario_results)
            
            # Collect metrics
            for test in scenario_results["tests"]:
                all_response_times.append(test["response_time_ms"])
                if test.get("cache_hit", False):
                    cache_hits += 1
                total_requests += 1
        
        # Calculate summary statistics
        if all_response_times:
            results["summary"] = {
                "total_requests": total_requests,
                "avg_response_time_ms": round(statistics.mean(all_response_times), 2),
                "median_response_time_ms": round(statistics.median(all_response_times), 2),
                "min_response_time_ms": round(min(all_response_times), 2),
                "max_response_time_ms": round(max(all_response_times), 2),
                "cache_hit_rate": round(cache_hits / total_requests, 3) if total_requests > 0 else 0,
                "sub_2s_responses": sum(1 for t in all_response_times if t < 2000),
                "sub_2s_rate": round(sum(1 for t in all_response_times if t < 2000) / len(all_response_times), 3),
                "performance_target_met": all(t < 2000 for t in all_response_times)
            }
        
        return results
    
    async def _test_scenario(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Test a specific search scenario."""
        scenario_results = {
            "scenario_name": scenario["name"],
            "request_data": scenario["request"],
            "tests": []
        }
        
        # Test 1: First request (likely cache miss)
        test_result = await self._make_enhanced_search_request(
            scenario["request"], 
            f"first-{scenario['name'].lower().replace(' ', '-')}"
        )
        test_result["test_type"] = "first_request"
        scenario_results["tests"].append(test_result)
        
        # Test 2: Immediate second request (should be cache hit)
        test_result = await self._make_enhanced_search_request(
            scenario["request"], 
            f"second-{scenario['name'].lower().replace(' ', '-')}"
        )
        test_result["test_type"] = "cache_hit_test"
        scenario_results["tests"].append(test_result)
        
        # Test 3: Request with scoring details
        test_result = await self._make_enhanced_search_request(
            scenario["request"], 
            f"scored-{scenario['name'].lower().replace(' ', '-')}",
            include_scores=True
        )
        test_result["test_type"] = "with_scoring"
        scenario_results["tests"].append(test_result)
        
        return scenario_results
    
    async def _make_enhanced_search_request(
        self, 
        request_data: Dict[str, Any], 
        request_id: str,
        include_scores: bool = False
    ) -> Dict[str, Any]:
        """Make an enhanced search request and measure performance."""
        start_time = time.time()
        
        url = f"{self.base_url}/enhanced/search"
        if include_scores:
            url += "?include_scores=true"
        
        headers = {"X-Request-ID": request_id}
        
        try:
            async with self.session.post(url, json=request_data, headers=headers) as response:
                response_time = (time.time() - start_time) * 1000
                response_data = await response.json()
                
                return {
                    "request_id": request_id,
                    "response_time_ms": round(response_time, 2),
                    "status_code": response.status,
                    "cache_hit": response.headers.get("X-Cache-Hit", "false") == "true",
                    "data_source": response.headers.get("X-Data-Source", "unknown"),
                    "enhanced_search": response.headers.get("X-Enhanced-Search", "false") == "true",
                    "total_options": response_data.get("total_options", 0),
                    "has_scoring": "score_breakdown" in str(response_data),
                    "success": response.status == 200
                }
                
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return {
                "request_id": request_id,
                "response_time_ms": round(response_time, 2),
                "status_code": 500,
                "error": str(e),
                "success": False
            }
    
    async def test_enhanced_airport_search(self) -> Dict[str, Any]:
        """Test enhanced airport search functionality."""
        print("🏢 Testing Enhanced Airport Search...")
        
        airport_test_cases = [
            {"search_text": "del", "expected_type": "exact_code"},
            {"search_text": "delhi", "expected_type": "city_match"},
            {"search_text": "mumbai", "expected_type": "city_match"},
            {"search_text": "bangalore", "expected_type": "city_match"},
            {"search_text": "indira", "expected_type": "name_match"},
            {"search_text": "", "expected_type": "popular_airports"},
            {"search_text": "xyz123", "expected_type": "no_match"}
        ]
        
        results = {
            "test_name": "enhanced_airport_search",
            "timestamp": datetime.now().isoformat(),
            "test_cases": [],
            "summary": {}
        }
        
        total_tests = 0
        successful_tests = 0
        total_response_time = 0
        
        for test_case in airport_test_cases:
            print(f"  🔍 Testing airport search: '{test_case['search_text']}'")
            
            start_time = time.time()
            
            try:
                url = f"{self.base_url}/enhanced/airports"
                request_data = {"search_text": test_case["search_text"]}
                
                async with self.session.post(url, json=request_data) as response:
                    response_time = (time.time() - start_time) * 1000
                    response_data = await response.json()
                    
                    test_result = {
                        "search_text": test_case["search_text"],
                        "expected_type": test_case["expected_type"],
                        "response_time_ms": round(response_time, 2),
                        "status_code": response.status,
                        "total_results": len(response_data.get("airports", [])),
                        "has_relevance_scores": any("relevance_score" in airport for airport in response_data.get("airports", [])),
                        "success": response.status == 200
                    }
                    
                    # Validate results based on expected type
                    if test_case["expected_type"] == "no_match":
                        test_result["validation"] = "passed" if test_result["total_results"] == 0 else "failed"
                    elif test_case["expected_type"] == "popular_airports":
                        test_result["validation"] = "passed" if test_result["total_results"] > 0 else "failed"
                    else:
                        test_result["validation"] = "passed" if test_result["total_results"] > 0 else "failed"
                    
                    results["test_cases"].append(test_result)
                    
                    if test_result["success"] and test_result["validation"] == "passed":
                        successful_tests += 1
                    
                    total_response_time += response_time
                    total_tests += 1
                    
            except Exception as e:
                response_time = (time.time() - start_time) * 1000
                results["test_cases"].append({
                    "search_text": test_case["search_text"],
                    "expected_type": test_case["expected_type"],
                    "response_time_ms": round(response_time, 2),
                    "error": str(e),
                    "success": False,
                    "validation": "failed"
                })
                total_response_time += response_time
                total_tests += 1
        
        # Calculate summary
        results["summary"] = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": round(successful_tests / total_tests, 3) if total_tests > 0 else 0,
            "avg_response_time_ms": round(total_response_time / total_tests, 2) if total_tests > 0 else 0,
            "all_sub_1s": all(test.get("response_time_ms", 1000) < 1000 for test in results["test_cases"])
        }
        
        return results
    
    async def test_search_accuracy(self) -> Dict[str, Any]:
        """Test search result accuracy and relevance."""
        print("🎯 Testing Search Accuracy and Relevance...")
        
        # This would involve more complex validation of search results
        # For now, we'll test basic functionality
        
        accuracy_tests = [
            {
                "name": "Popular Route Accuracy",
                "request": {
                    "Trips": [{"From": "DEL", "To": "BOM", "OnwardDate": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")}],
                    "ADT": 1, "CHD": 0, "INF": 0, "Cabin": "E", "FareType": "REGULAR"
                },
                "expected_criteria": {
                    "min_options": 5,
                    "has_direct_flights": True,
                    "price_range_reasonable": True
                }
            }
        ]
        
        results = {
            "test_name": "search_accuracy",
            "timestamp": datetime.now().isoformat(),
            "accuracy_tests": [],
            "summary": {}
        }
        
        for test in accuracy_tests:
            print(f"  📊 Testing: {test['name']}")
            
            try:
                url = f"{self.base_url}/enhanced/search?include_scores=true"
                headers = {"X-Request-ID": f"accuracy-test-{test['name'].lower().replace(' ', '-')}"}
                
                async with self.session.post(url, json=test["request"], headers=headers) as response:
                    response_data = await response.json()
                    
                    # Analyze results for accuracy
                    accuracy_result = {
                        "test_name": test["name"],
                        "total_options": response_data.get("total_options", 0),
                        "has_scoring": "score_breakdown" in str(response_data),
                        "criteria_met": {},
                        "accuracy_score": 0.0
                    }
                    
                    # Check criteria
                    criteria_met = 0
                    total_criteria = len(test["expected_criteria"])
                    
                    if response_data.get("total_options", 0) >= test["expected_criteria"]["min_options"]:
                        accuracy_result["criteria_met"]["min_options"] = True
                        criteria_met += 1
                    else:
                        accuracy_result["criteria_met"]["min_options"] = False
                    
                    # Calculate accuracy score
                    accuracy_result["accuracy_score"] = criteria_met / total_criteria if total_criteria > 0 else 0
                    
                    results["accuracy_tests"].append(accuracy_result)
                    
            except Exception as e:
                results["accuracy_tests"].append({
                    "test_name": test["name"],
                    "error": str(e),
                    "accuracy_score": 0.0
                })
        
        # Calculate overall accuracy
        if results["accuracy_tests"]:
            avg_accuracy = sum(test.get("accuracy_score", 0) for test in results["accuracy_tests"]) / len(results["accuracy_tests"])
            results["summary"] = {
                "overall_accuracy": round(avg_accuracy, 3),
                "tests_passed": sum(1 for test in results["accuracy_tests"] if test.get("accuracy_score", 0) >= 0.8),
                "accuracy_target_met": avg_accuracy >= 0.9
            }
        
        return results
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all enhanced search tests."""
        print("🧪 Starting Enhanced Flight Search Test Suite")
        print("=" * 60)
        
        all_results = {
            "test_suite": "enhanced_flight_search",
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        try:
            # Test 1: Enhanced search performance
            all_results["tests"]["performance"] = await self.test_enhanced_search_performance()
            print()
            
            # Test 2: Enhanced airport search
            all_results["tests"]["airport_search"] = await self.test_enhanced_airport_search()
            print()
            
            # Test 3: Search accuracy
            all_results["tests"]["accuracy"] = await self.test_search_accuracy()
            print()
            
            # Generate overall summary
            all_results["overall_summary"] = self._generate_overall_summary(all_results["tests"])
            
        except Exception as e:
            print(f"❌ Test suite failed: {str(e)}")
            all_results["error"] = str(e)
        
        return all_results
    
    def _generate_overall_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall test summary."""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "enhancement_status": "✅ ENHANCED FEATURES WORKING",
            "key_improvements": [],
            "performance_gains": {},
            "recommendations": []
        }
        
        # Analyze performance results
        if "performance" in test_results:
            perf_summary = test_results["performance"].get("summary", {})
            if perf_summary.get("sub_2s_rate", 0) >= 0.95:
                summary["key_improvements"].append("95%+ requests under 2 seconds")
            if perf_summary.get("cache_hit_rate", 0) >= 0.8:
                summary["key_improvements"].append("High cache hit rate achieved")
        
        # Analyze airport search results
        if "airport_search" in test_results:
            airport_summary = test_results["airport_search"].get("summary", {})
            if airport_summary.get("success_rate", 0) >= 0.9:
                summary["key_improvements"].append("Enhanced airport search working well")
        
        # Analyze accuracy results
        if "accuracy" in test_results:
            accuracy_summary = test_results["accuracy"].get("summary", {})
            if accuracy_summary.get("overall_accuracy", 0) >= 0.8:
                summary["key_improvements"].append("Search accuracy targets met")
        
        if not summary["key_improvements"]:
            summary["enhancement_status"] = "⚠️ NEEDS OPTIMIZATION"
            summary["recommendations"].append("Review performance metrics and optimize accordingly")
        
        return summary


async def main():
    """Main test execution function."""
    print("🚀 Enhanced Flight Search Performance Test Suite")
    print("=" * 60)
    print("Testing enhanced search algorithms and optimizations...")
    print()
    
    async with EnhancedSearchTestSuite() as test_suite:
        results = await test_suite.run_comprehensive_tests()
        
        print("=" * 60)
        print("📋 ENHANCED SEARCH TEST RESULTS")
        print("=" * 60)
        
        if "overall_summary" in results:
            summary = results["overall_summary"]
            print(f"🎯 Status: {summary['enhancement_status']}")
            
            if summary["key_improvements"]:
                print("✨ Key Improvements:")
                for improvement in summary["key_improvements"]:
                    print(f"  • {improvement}")
                print()
            
            if summary["recommendations"]:
                print("💡 Recommendations:")
                for rec in summary["recommendations"]:
                    print(f"  • {rec}")
                print()
        
        # Save detailed results
        with open("enhanced_search_test_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print("📄 Detailed results saved to: enhanced_search_test_results.json")
        print("✅ Enhanced search testing completed!")


if __name__ == "__main__":
    asyncio.run(main())
