#!/usr/bin/env python3
"""
FastAPI server startup script configured for local network access.
Runs the flight booking API server accessible from local network devices.
"""

import uvicorn
import socket
import sys
import os
from pathlib import Path

def get_local_ip():
    """Get the local IP address of this machine."""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
        return local_ip
    except Exception:
        return "127.0.0.1"

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        "fastapi",
        "uvicorn",
        "redis",
        "sqlalchemy",
        "pymysql",
        "aiohttp",
        "celery"
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\n📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def check_services():
    """Check if required services are running."""
    services_status = {
        "redis": False,
        "mysql": False
    }

    # Check Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        services_status["redis"] = True
    except Exception:
        pass

    # Check MySQL (basic connection test)
    try:
        import pymysql
        # This is a basic check - you might need to adjust connection parameters
        connection = pymysql.connect(
            host='localhost',
            user='root',  # Adjust as needed
            password='',  # Adjust as needed
            database='test'
        )
        connection.close()
        services_status["mysql"] = True
    except Exception:
        pass

    return services_status

def print_network_info(host, port):
    """Print network access information."""
    local_ip = get_local_ip()

    print("🌐 SERVER NETWORK ACCESS INFORMATION")
    print("=" * 50)
    print(f"🖥️  Local Access:")
    print(f"   • http://localhost:{port}")
    print(f"   • http://127.0.0.1:{port}")
    print()
    print(f"📱 Local Network Access:")
    print(f"   • http://{local_ip}:{port}")
    print()
    print(f"🔗 API Documentation:")
    print(f"   • Swagger UI: http://{local_ip}:{port}/docs")
    print(f"   • ReDoc: http://{local_ip}:{port}/redoc")
    print()
    print(f"🚀 Optimized Endpoints:")
    print(f"   • Search: http://{local_ip}:{port}/optimized/search/optimized")
    print(f"   • Stats: http://{local_ip}:{port}/optimized/search/performance/stats")
    print()
    print("📋 Test Commands:")
    print(f"   curl http://{local_ip}:{port}/health")
    print(f"   curl http://{local_ip}:{port}/optimized/search/performance/stats")
    print()

def main():
    """Main server startup function."""
    print("🚀 Fast Travel Backend - Local Network Server")
    print("=" * 50)

    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✅ All dependencies found")

    # Check services
    print("\n🔍 Checking services...")
    services = check_services()

    for service, status in services.items():
        status_icon = "✅" if status else "⚠️"
        status_text = "Running" if status else "Not detected"
        print(f"   {status_icon} {service.upper()}: {status_text}")

    if not all(services.values()):
        print("\n⚠️  Some services are not running. The server may have limited functionality.")
        print("   • Redis: Required for caching and performance optimization")
        print("   • MySQL: Required for database operations")
        print("\n🔧 To start services:")
        if not services["redis"]:
            print("   • Redis: Install and start Redis server")
        if not services["mysql"]:
            print("   • MySQL: Install and start MySQL server")

        response = input("\n❓ Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)

    # Server configuration
    host = "0.0.0.0"  # Listen on all interfaces for network access
    port = 8000

    # Print network information
    print()
    print_network_info(host, port)

    # Firewall reminder
    print("🔒 FIREWALL CONFIGURATION")
    print("=" * 50)
    print("⚠️  Make sure your firewall allows incoming connections on port 8000")
    print()
    print("Windows Firewall:")
    print("   1. Open Windows Defender Firewall")
    print("   2. Click 'Allow an app or feature through Windows Defender Firewall'")
    print("   3. Click 'Change Settings' then 'Allow another app'")
    print("   4. Browse and select Python.exe")
    print("   5. Check both 'Private' and 'Public' networks")
    print()
    print("Alternative: Temporarily disable firewall for testing")
    print()

    # Start server
    input("Press Enter to start the server...")
    print("\n🚀 Starting FastAPI server...")
    print("   Press Ctrl+C to stop the server")
    print()

    try:
        # Configure uvicorn for production-like settings
        uvicorn.run(
            "app.main:app",  # Correct path to the FastAPI app
            host=host,
            port=port,
            reload=True,  # Enable auto-reload for development
            access_log=True,
            log_level="info",
            workers=1,  # Single worker for development
            # SSL configuration (uncomment if you have SSL certificates)
            # ssl_keyfile="path/to/key.pem",
            # ssl_certfile="path/to/cert.pem",
        )
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server failed to start: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("   • Check if port 8000 is already in use")
        print("   • Verify the main app file path (main:app)")
        print("   • Check if all dependencies are installed")
        sys.exit(1)

if __name__ == "__main__":
    main()
