from typing import List, Optional
import json
from app.config import get_database_connection
from .models import Airport

def create_airport_table_if_not_exists():
    connection = get_database_connection()
    cursor = connection.cursor()
    create_table_query = """
    CREATE TABLE IF NOT EXISTS airports (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(10) UNIQUE,
        name VARCHAR(100),
        country VARCHAR(100),
        city_name VARCHAR(100),
        city_code VARCHAR(10)
    )
    """
    cursor.execute(create_table_query)
    connection.commit()
    connection.close()

def bulk_insert_airports(airports: List[Airport]):
    try:
        create_airport_table_if_not_exists()

        connection = get_database_connection()
        cursor = connection.cursor()

        insert_query = """
        INSERT INTO airports (code, name, country, city_name, city_code)
        VALUES (%s, %s, %s, %s, %s)
        """

        # Prepare data for bulk insertion
        bulk_data = [
            (
                airport.code,
                airport.name,
                airport.country,
                airport.city_name,
                airport.city_code,
            ) for airport in airports
        ]

        # Execute bulk insert
        cursor.executemany(insert_query, bulk_data)
        connection.commit()
        connection.close()

        print(f"Bulk data inserted successfully. Inserted rows: {cursor.rowcount}")
    except Exception as e:
        print(f"Error: {str(e)}")

def run_airports_data():
    # Read data from JSON file
    airport_data = [
    {
        "code": "DEL",
        "name": "Indira Gandhi International Airport",
        "country": "India",
        "city_name": "New Delhi",
        "city_code": "DEL"
    },
    {
        "code": "BLR",
        "name": "Kempegowda International Airport",
        "country": "India",
        "city_name": "Bangalore",
        "city_code": "BLR"
    },
    {
        "code": "HYD",
        "name": "Rajiv Gandhi International Airport",
        "country": "India",
        "city_name": "Hyderabad",
        "city_code": "HYD"
    },
    {
        "code": "CCU",
        "name": "Netaji Subhas Chandra Bose International Airport",
        "country": "India",
        "city_name": "Kolkata",
        "city_code": "CCU"
    },
    {
        "code": "MAA",
        "name": "Chennai International Airport",
        "country": "India",
        "city_name": "Chennai",
        "city_code": "MAA"
    },
    {
        "code": "AMD",
        "name": "Sardar Vallabhbhai Patel International Airport",
        "country": "India",
        "city_name": "Ahmedabad",
        "city_code": "AMD"
    },
    {
        "code": "COK",
        "name": "Cochin International Airport",
        "country": "India",
        "city_name": "Kochi",
        "city_code": "COK"
    },
    {
        "code": "TRV",
        "name": "Trivandrum International Airport",
        "country": "India",
        "city_name": "Thiruvananthapuram",
        "city_code": "TRV"
    },
    {
        "code": "PNQ",
        "name": "Pune Airport",
        "country": "India",
        "city_name": "Pune",
        "city_code": "PNQ"
    },
    {
        "code": "GOI",
        "name": "Goa International Airport",
        "country": "India",
        "city_name": "Goa",
        "city_code": "GOI"
    },
    {
        "code": "KWI",
        "name": "Kuwait International Airport",
        "country": "India",
        "city_name": "Kuwait",
        "city_code": "KWI"
    },
    {
        "code": "CJB",
        "name": "Coimbatore International Airport",
        "country": "India",
        "city_name": "Coimbatore",
        "city_code": "CJB"
    },
    {
        "code": "JAI",
        "name": "Jaipur International Airport",
        "country": "India",
        "city_name": "Jaipur",
        "city_code": "JAI"
    },
    {
        "code": "IXC",
        "name": "Chandigarh International Airport",
        "country": "India",
        "city_name": "Chandigarh",
        "city_code": "IXC"
    },
    {
        "code": "BDQ",
        "name": "Vadodara Airport",
        "country": "India",
        "city_name": "Vadodara",
        "city_code": "BDQ"
    },
    {
        "code": "ISB",
        "name": "Islamabad International Airport",
        "country": "India",
        "city_name": "Islamabad",
        "city_code": "ISB"
    },
    {
        "code": "IXJ",
        "name": "Jammu Airport",
        "country": "India",
        "city_name": "Jammu",
        "city_code": "IXJ"
    },
    {
        "code": "UTR",
        "name": "Pantnagar Airport",
        "country": "India",
        "city_name": "Pantnagar",
        "city_code": "PGH"
    },
    {
        "code": "STV",
        "name": "Surat Airport",
        "country": "India",
        "city_name": "Surat",
        "city_code": "STV"
    },
    {
        "code": "TIR",
        "name": "Tirupati Airport",
        "country": "India",
        "city_name": "Tirupati",
        "city_code": "TIR"
    },
    {
        "code": "BHO",
        "name": "Rajah Bhoj Airport",
        "country": "India",
        "city_name": "Bhopal",
        "city_code": "BHO"
    },
    {
        "code": "NAG",
        "name": "Dr. Babasaheb Ambedkar International Airport",
        "country": "India",
        "city_name": "Nagpur",
        "city_code": "NAG"
    },
    {
        "code": "IXZ",
        "name": "Veer Savarkar International Airport",
        "country": "India",
        "city_name": "Port Blair",
        "city_code": "IXZ"
    },
    {
        "code": "DIB",
        "name": "Dibrugarh Airport",
        "country": "India",
        "city_name": "Dibrugarh",
        "city_code": "DIB"
    },
    {
        "code": "IXT",
        "name": "Tezpur Airport",
        "country": "India",
        "city_name": "Tezpur",
        "city_code": "IXT"
    },
    {
        "code": "RUP",
        "name": "Rupsi Airport",
        "country": "India",
        "city_name": "Rupsi",
        "city_code": "RUP"
    },
    {
        "code": "JRH",
        "name": "Jorhat Airport",
        "country": "India",
        "city_name": "Jorhat",
        "city_code": "JRH"
    },
    {
        "code": "GAU",
        "name": "Lokpriya Gopinath Bordoloi International Airport",
        "country": "India",
        "city_name": "Guwahati",
        "city_code": "GAU"
    },
    {
        "code": "BOM",
        "name": "Chhatrapati Shivaji Maharaj International Airport",
        "country": "India",
        "city_name": "Mumbai",
        "city_code": "BOM"
    },
    {
        "code": "DTT",
        "name": "Durgapur Airport",
        "country": "India",
        "city_name": "Durgapur",
        "city_code": "DTT"
    },
    {
        "code": "JDH",
        "name": "Jodhpur Airport",
        "country": "India",
        "city_name": "Jodhpur",
        "city_code": "JDH"
    },
    {
        "code": "PUD",
        "name": "Puducherry Airport",
        "country": "India",
        "city_name": "Puducherry",
        "city_code": "PUD"
    },
    {
        "code": "KUR",
        "name": "Kullu Manali Airport",
        "country": "India",
        "city_name": "Kullu",
        "city_code": "KUR"
    },
    {
        "code": "KIR",
        "name": "Kirkuk Airport",
        "country": "India",
        "city_name": "Kirkuk",
        "city_code": "KIR"
    },
    {
        "code": "PAT",
        "name": "Birsa Munda Airport",
        "country": "India",
        "city_name": "Patna",
        "city_code": "PAT"
    },
    {
        "code": "IXY",
        "name": "Keshod Airport",
        "country": "India",
        "city_name": "Keshod",
        "city_code": "IXY"
    },
    {
        "code": "HJR",
        "name": "Hajipur Airport",
        "country": "India",
        "city_name": "Hajipur",
        "city_code": "HJR"
    },
    {
        "code": "SHL",
        "name": "Shillong Airport",
        "country": "India",
        "city_name": "Shillong",
        "city_code": "SHL"
    },
    {
        "code": "RPR",
        "name": "Raipur Airport",
        "country": "India",
        "city_name": "Raipur",
        "city_code": "RPR"
    },
    {
        "code": "BHU",
        "name": "Bhatinda Airport",
        "country": "India",
        "city_name": "Bhatinda",
        "city_code": "BHU"
    },
    {
        "code": "MIR",
        "name": "Mirzapur Airport",
        "country": "India",
        "city_name": "Mirzapur",
        "city_code": "MIR"
    },
    {
        "code": "MEH",
        "name": "Mehboobnagar Airport",
        "country": "India",
        "city_name": "Mehboobnagar",
        "city_code": "MEH"
    },
    {
        "code": "GWL",
        "name": "Gwalior Airport",
        "country": "India",
        "city_name": "Gwalior",
        "city_code": "GWL"
    },
    {
        "code": "KNU",
        "name": "Kanpur Airport",
        "country": "India",
        "city_name": "Kanpur",
        "city_code": "KNU"
    },
    {
        "code": "VGA",
        "name": "Vijayawada Airport",
        "country": "India",
        "city_name": "Vijayawada",
        "city_code": "VGA"
    },
    {
        "code": "NDC",
        "name": "Nagpur Airport",
        "country": "India",
        "city_name": "Nagpur",
        "city_code": "NDC"
    },
    {
        "code": "LKO",
        "name": "Lucknow Airport",
        "country": "India",
        "city_name": "Lucknow",
        "city_code": "LKO"
    }
]

    # Convert list of dictionaries to list of Airport objects
    airport_objects = [Airport(**airport) for airport in airport_data]

    # Check if data is available
    if airport_data:
        # Call the bulk insert function
        bulk_insert_airports(airport_objects)
        print("BULK INSERT")
    else:
        print("No data found to insert.")


run_airports_data()