from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, DateTime, Boolean, func
from sqlalchemy.orm import declarative_mixin

# Create a base class for declarative models
Base = declarative_base()

@declarative_mixin
class TimestampMixin:
    created_at = Column(DateTime, default=func.now(), nullable=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=True)

class BaseModel(Base, TimestampMixin):
    __abstract__ = True