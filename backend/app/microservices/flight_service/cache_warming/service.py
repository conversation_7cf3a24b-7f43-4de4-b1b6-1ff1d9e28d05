"""
Advanced cache warming service for proactive cache population.
Implements intelligent route popularity detection and predictive cache loading.
"""

import asyncio
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from collections import defaultdict, Counter

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service, generate_search_cache_key
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
from app.microservices.flight_service.utils.async_provider_utils import make_async_provider_call
from app.microservices.flight_service import config


@dataclass
class RoutePopularity:
    """Route popularity metrics."""
    route: str  # Format: "FROM-TO"
    search_count: int = 0
    last_searched: Optional[float] = None
    avg_response_time: float = 0.0
    cache_hit_rate: float = 0.0
    priority_score: float = 0.0


@dataclass
class WarmingTask:
    """Cache warming task information."""
    task_id: str
    route: str
    date: str
    request_data: Dict[str, Any]
    created_at: float
    status: str = "pending"  # pending, running, completed, failed
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None


class CacheWarmingService:
    """
    Advanced cache warming service with intelligent route detection and predictive loading.
    """
    
    def __init__(self):
        self.redis_client = RedisCache.connection()
        self.route_popularity: Dict[str, RoutePopularity] = {}
        self.warming_tasks: Dict[str, WarmingTask] = {}
        self.warming_stats = {
            "total_warming_requests": 0,
            "successful_warmings": 0,
            "failed_warmings": 0,
            "routes_warmed": 0,
            "cache_hits_from_warming": 0
        }
        self.popular_routes_cache_key = "popular_routes_analysis"
        self._warming_semaphore = asyncio.Semaphore(5)  # Limit concurrent warming tasks
    
    async def track_search_request(self, request_data: Dict[str, Any], response_time: float, cache_hit: bool):
        """
        Track search request for popularity analysis.
        
        Args:
            request_data: Search request data
            response_time: Response time in seconds
            cache_hit: Whether this was a cache hit
        """
        try:
            # Extract route information
            if not request_data.get("Trips") or len(request_data["Trips"]) == 0:
                return
            
            trip = request_data["Trips"][0]
            route = f"{trip.get('From', '')}-{trip.get('To', '')}"
            
            if not route or route == "-":
                return
            
            # Update route popularity
            if route not in self.route_popularity:
                self.route_popularity[route] = RoutePopularity(route=route)
            
            popularity = self.route_popularity[route]
            popularity.search_count += 1
            popularity.last_searched = time.time()
            
            # Update average response time
            if popularity.avg_response_time == 0:
                popularity.avg_response_time = response_time
            else:
                popularity.avg_response_time = (popularity.avg_response_time + response_time) / 2
            
            # Update cache hit rate
            if cache_hit:
                self.warming_stats["cache_hits_from_warming"] += 1
            
            # Calculate priority score (higher = more important to warm)
            popularity.priority_score = self._calculate_priority_score(popularity)
            
            # Store updated popularity data
            await self._store_popularity_data()
            
        except Exception as e:
            print(f"Error tracking search request: {str(e)}")
    
    def _calculate_priority_score(self, popularity: RoutePopularity) -> float:
        """
        Calculate priority score for cache warming.
        
        Args:
            popularity: Route popularity data
            
        Returns:
            Priority score (higher = more important)
        """
        # Base score from search frequency
        frequency_score = min(popularity.search_count / 10.0, 10.0)  # Max 10 points
        
        # Recency score (more recent searches get higher score)
        if popularity.last_searched:
            hours_since_last = (time.time() - popularity.last_searched) / 3600
            recency_score = max(0, 5 - hours_since_last / 24)  # Max 5 points, decreases over days
        else:
            recency_score = 0
        
        # Response time penalty (slower routes get lower priority)
        response_time_penalty = max(0, 2 - popularity.avg_response_time / 2)  # Max 2 points
        
        return frequency_score + recency_score + response_time_penalty
    
    async def _store_popularity_data(self):
        """Store popularity data in cache."""
        try:
            popularity_data = {
                route: {
                    "search_count": pop.search_count,
                    "last_searched": pop.last_searched,
                    "avg_response_time": pop.avg_response_time,
                    "cache_hit_rate": pop.cache_hit_rate,
                    "priority_score": pop.priority_score
                }
                for route, pop in self.route_popularity.items()
            }
            
            self.redis_client.set_cache(
                self.popular_routes_cache_key,
                popularity_data,
                86400  # 24 hours
            )
        except Exception as e:
            print(f"Error storing popularity data: {str(e)}")
    
    async def _load_popularity_data(self):
        """Load popularity data from cache."""
        try:
            popularity_data = self.redis_client.get_cache(self.popular_routes_cache_key)
            if popularity_data:
                for route, data in popularity_data.items():
                    self.route_popularity[route] = RoutePopularity(
                        route=route,
                        search_count=data.get("search_count", 0),
                        last_searched=data.get("last_searched"),
                        avg_response_time=data.get("avg_response_time", 0.0),
                        cache_hit_rate=data.get("cache_hit_rate", 0.0),
                        priority_score=data.get("priority_score", 0.0)
                    )
        except Exception as e:
            print(f"Error loading popularity data: {str(e)}")
    
    async def get_popular_routes(self, limit: int = 20) -> List[RoutePopularity]:
        """
        Get most popular routes for cache warming.
        
        Args:
            limit: Maximum number of routes to return
            
        Returns:
            List of popular routes sorted by priority score
        """
        await self._load_popularity_data()
        
        # Sort routes by priority score (descending)
        sorted_routes = sorted(
            self.route_popularity.values(),
            key=lambda x: x.priority_score,
            reverse=True
        )
        
        return sorted_routes[:limit]
    
    async def warm_popular_routes(self, max_routes: int = 10, future_days: int = 3) -> Dict[str, Any]:
        """
        Warm cache for popular routes for upcoming dates.
        
        Args:
            max_routes: Maximum number of routes to warm
            future_days: Number of future days to warm
            
        Returns:
            Warming results summary
        """
        popular_routes = await self.get_popular_routes(max_routes)
        
        if not popular_routes:
            return {
                "status": "no_popular_routes",
                "routes_warmed": 0,
                "tasks_created": 0
            }
        
        # Generate dates for warming (today + future days)
        dates_to_warm = []
        for i in range(future_days + 1):
            date = (datetime.now() + timedelta(days=i)).strftime("%Y-%m-%d")
            dates_to_warm.append(date)
        
        # Create warming tasks
        tasks_created = 0
        for route_popularity in popular_routes:
            route_parts = route_popularity.route.split("-")
            if len(route_parts) != 2:
                continue
            
            from_code, to_code = route_parts
            
            for date in dates_to_warm:
                # Create request data for warming
                request_data = {
                    "Trips": [{"From": from_code, "To": to_code, "OnwardDate": date}],
                    "ADT": 1,
                    "CHD": 0,
                    "INF": 0,
                    "Cabin": "E",
                    "FareType": "REGULAR"
                }
                
                # Create warming task
                task_id = f"warm_{route_popularity.route}_{date}_{int(time.time())}"
                warming_task = WarmingTask(
                    task_id=task_id,
                    route=route_popularity.route,
                    date=date,
                    request_data=request_data,
                    created_at=time.time()
                )
                
                self.warming_tasks[task_id] = warming_task
                
                # Execute warming task in background
                asyncio.create_task(self._execute_warming_task(warming_task))
                tasks_created += 1
        
        return {
            "status": "warming_initiated",
            "routes_warmed": len(popular_routes),
            "tasks_created": tasks_created,
            "dates": dates_to_warm
        }
    
    async def _execute_warming_task(self, warming_task: WarmingTask):
        """
        Execute a single cache warming task.
        
        Args:
            warming_task: Task to execute
        """
        async with self._warming_semaphore:
            start_time = time.time()
            warming_task.status = "running"
            
            try:
                self.warming_stats["total_warming_requests"] += 1
                
                # Check if already cached
                cache_key = generate_search_cache_key(warming_task.request_data)
                existing_result = self.redis_client.get_cache(cache_key)
                
                if existing_result:
                    warming_task.status = "completed"
                    warming_task.result = {"cached": True, "message": "Already cached"}
                    warming_task.execution_time = time.time() - start_time
                    return
                
                # Translate request for provider
                provider_payload = flight_search_translate_client_to_provider(warming_task.request_data)
                provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"
                
                # Make async provider call
                provider_response = await make_async_provider_call(
                    provider_api_url,
                    provider_payload,
                    "tripjack"
                )
                
                # Translate response
                translator = SearchProviderResponseTranslator(provider_response, warming_task.request_data)
                client_response = translator.translate()
                
                # Add cache metadata
                client_response.update({
                    "TUI": cache_key,
                    "cached_at": datetime.now(timezone.utc).isoformat(),
                    "warmed": True
                })
                
                # Store in cache
                self.redis_client.set_cache(
                    cache_key,
                    client_response,
                    config.cache_timer.get('FLIGHT_SEARCH', 900)
                )
                
                warming_task.status = "completed"
                warming_task.result = {"cached": False, "message": "Successfully warmed"}
                warming_task.execution_time = time.time() - start_time
                
                self.warming_stats["successful_warmings"] += 1
                
            except Exception as e:
                warming_task.status = "failed"
                warming_task.error = str(e)
                warming_task.execution_time = time.time() - start_time
                
                self.warming_stats["failed_warmings"] += 1
                print(f"Cache warming failed for {warming_task.route} on {warming_task.date}: {str(e)}")
    
    async def warm_specific_routes(self, routes_config: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Warm cache for specific routes and dates.
        
        Args:
            routes_config: List of route configurations
                          [{"from": "DEL", "to": "BOM", "date": "2024-01-15", ...}, ...]
            
        Returns:
            Warming results
        """
        tasks_created = 0
        
        for route_config in routes_config:
            try:
                # Create request data
                request_data = {
                    "Trips": [{
                        "From": route_config.get("from", ""),
                        "To": route_config.get("to", ""),
                        "OnwardDate": route_config.get("date", "")
                    }],
                    "ADT": route_config.get("adults", 1),
                    "CHD": route_config.get("children", 0),
                    "INF": route_config.get("infants", 0),
                    "Cabin": route_config.get("cabin", "E"),
                    "FareType": route_config.get("fare_type", "REGULAR")
                }
                
                # Create warming task
                route = f"{route_config.get('from', '')}-{route_config.get('to', '')}"
                task_id = f"warm_specific_{route}_{route_config.get('date', '')}_{int(time.time())}"
                
                warming_task = WarmingTask(
                    task_id=task_id,
                    route=route,
                    date=route_config.get("date", ""),
                    request_data=request_data,
                    created_at=time.time()
                )
                
                self.warming_tasks[task_id] = warming_task
                
                # Execute warming task in background
                asyncio.create_task(self._execute_warming_task(warming_task))
                tasks_created += 1
                
            except Exception as e:
                print(f"Error creating warming task for route config {route_config}: {str(e)}")
        
        return {
            "status": "warming_initiated",
            "tasks_created": tasks_created,
            "routes_count": len(routes_config)
        }
    
    def get_warming_stats(self) -> Dict[str, Any]:
        """Get cache warming statistics."""
        # Count tasks by status
        task_status_counts = defaultdict(int)
        for task in self.warming_tasks.values():
            task_status_counts[task.status] += 1
        
        return {
            **self.warming_stats,
            "popular_routes_count": len(self.route_popularity),
            "active_warming_tasks": len(self.warming_tasks),
            "task_status_counts": dict(task_status_counts),
            "top_routes": [
                {"route": route, "priority_score": pop.priority_score, "search_count": pop.search_count}
                for route, pop in sorted(
                    self.route_popularity.items(),
                    key=lambda x: x[1].priority_score,
                    reverse=True
                )[:10]
            ]
        }
    
    async def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Clean up old warming tasks."""
        cutoff_time = time.time() - (max_age_hours * 3600)
        old_task_ids = [
            task_id for task_id, task in self.warming_tasks.items()
            if task.created_at < cutoff_time
        ]
        
        for task_id in old_task_ids:
            del self.warming_tasks[task_id]
        
        return len(old_task_ids)


# Global cache warming service instance
cache_warming_service = CacheWarmingService()
