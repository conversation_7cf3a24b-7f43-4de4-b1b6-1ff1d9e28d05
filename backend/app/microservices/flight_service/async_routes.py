"""
Async routes for flight service with enhanced performance and optimization features.
Provides async endpoints for flight search and detail operations.
"""

from fastapi import API<PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks
from typing import Dict, Any
from pydantic import BaseModel

from app.microservices.flight_service.search_service.async_service import async_flight_search
from app.microservices.flight_service.detail_service.async_service import async_flight_detail
from app.microservices.flight_service.cache_warming.service import cache_warming_service
from app.microservices.flight_service.utils.async_provider_utils import async_provider_client
from app.microservices.flight_service.utils.request_deduplication import request_deduplicator


# Pydantic models for request validation
class FlightSearchRequest(BaseModel):
    Trips: list
    ADT: int = 1
    CHD: int = 0
    INF: int = 0
    Cabin: str = "E"
    FareType: str = "REGULAR"


class FlightDetailRequest(BaseModel):
    FareId: str
    ADT: int = 1
    CHD: int = 0
    INF: int = 0


class SearchListRequest(BaseModel):
    TUI: str


class DetailListRequest(BaseModel):
    TUI: str


class CacheWarmingRequest(BaseModel):
    routes: list
    max_routes: int = 10
    future_days: int = 3


# Create router instance
router = APIRouter()


@router.post("/async/search")
async def async_flight_search_endpoint(request: FlightSearchRequest):
    """
    Enhanced async flight search with all optimization features.
    
    Args:
        request: Flight search request parameters
        
    Returns:
        Flight search results with performance metadata
    """
    try:
        request_data = request.dict()
        result = await async_flight_search.search(request_data)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Async search failed: {str(e)}")


@router.post("/async/search_list")
async def async_flight_search_list_endpoint(request: SearchListRequest):
    """
    Enhanced async search list retrieval.
    
    Args:
        request: Search list request with TUI
        
    Returns:
        Cached search results
    """
    try:
        request_data = request.dict()
        result = await async_flight_search.search_list(request_data)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Async search list failed: {str(e)}")


@router.post("/async/pricing")
async def async_flight_detail_endpoint(request: FlightDetailRequest):
    """
    Enhanced async flight detail/pricing with all optimization features.
    
    Args:
        request: Flight detail request parameters
        
    Returns:
        Flight detail results with performance metadata
    """
    try:
        request_data = request.dict()
        result = await async_flight_detail.detail(request_data)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Async detail failed: {str(e)}")


@router.post("/async/pricing_list")
async def async_flight_detail_list_endpoint(request: DetailListRequest):
    """
    Enhanced async detail list retrieval.
    
    Args:
        request: Detail list request with TUI
        
    Returns:
        Cached detail results
    """
    try:
        request_data = request.dict()
        result = await async_flight_detail.get_detail(request_data)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Async detail list failed: {str(e)}")


@router.get("/async/performance/search")
async def get_async_search_performance():
    """
    Get async search service performance statistics.
    
    Returns:
        Performance metrics for async search service
    """
    try:
        stats = async_flight_search.get_performance_stats()
        return {
            "status": "success",
            "service": "async_search",
            "performance_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve search performance: {str(e)}")


@router.get("/async/performance/detail")
async def get_async_detail_performance():
    """
    Get async detail service performance statistics.
    
    Returns:
        Performance metrics for async detail service
    """
    try:
        stats = async_flight_detail.get_performance_stats()
        return {
            "status": "success",
            "service": "async_detail",
            "performance_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve detail performance: {str(e)}")


@router.get("/async/performance/provider")
async def get_async_provider_performance():
    """
    Get async provider client performance statistics.
    
    Returns:
        Performance metrics for async provider client
    """
    try:
        stats = async_provider_client.get_stats()
        return {
            "status": "success",
            "service": "async_provider",
            "performance_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve provider performance: {str(e)}")


@router.get("/async/performance/deduplication")
async def get_deduplication_performance():
    """
    Get request deduplication performance statistics.
    
    Returns:
        Performance metrics for request deduplication
    """
    try:
        stats = request_deduplicator.get_stats()
        pending_requests = request_deduplicator.get_pending_requests_info()
        
        return {
            "status": "success",
            "service": "request_deduplication",
            "performance_stats": stats,
            "pending_requests": pending_requests
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve deduplication performance: {str(e)}")


@router.get("/async/performance/warming")
async def get_cache_warming_performance():
    """
    Get cache warming service performance statistics.
    
    Returns:
        Performance metrics for cache warming service
    """
    try:
        stats = cache_warming_service.get_warming_stats()
        return {
            "status": "success",
            "service": "cache_warming",
            "performance_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve warming performance: {str(e)}")


@router.post("/async/cache/warm")
async def trigger_async_cache_warming(
    request: CacheWarmingRequest, 
    background_tasks: BackgroundTasks
):
    """
    Trigger async cache warming for popular routes.
    
    Args:
        request: Cache warming configuration
        background_tasks: FastAPI background tasks
        
    Returns:
        Cache warming initiation confirmation
    """
    try:
        # Add cache warming task to background
        background_tasks.add_task(
            cache_warming_service.warm_popular_routes,
            request.max_routes,
            request.future_days
        )
        
        return {
            "status": "success",
            "message": f"Async cache warming initiated for up to {request.max_routes} routes",
            "max_routes": request.max_routes,
            "future_days": request.future_days
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Async cache warming failed: {str(e)}")


@router.post("/async/cache/warm/specific")
async def trigger_specific_route_warming(
    request: CacheWarmingRequest,
    background_tasks: BackgroundTasks
):
    """
    Trigger async cache warming for specific routes.
    
    Args:
        request: Specific routes to warm
        background_tasks: FastAPI background tasks
        
    Returns:
        Specific route warming confirmation
    """
    try:
        # Add specific route warming task to background
        background_tasks.add_task(
            cache_warming_service.warm_specific_routes,
            request.routes
        )
        
        return {
            "status": "success",
            "message": f"Async cache warming initiated for {len(request.routes)} specific routes",
            "routes_count": len(request.routes)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Specific route warming failed: {str(e)}")


@router.get("/async/popular-routes")
async def get_popular_routes(limit: int = 20):
    """
    Get popular routes for cache warming analysis.
    
    Args:
        limit: Maximum number of routes to return
        
    Returns:
        List of popular routes with metrics
    """
    try:
        popular_routes = await cache_warming_service.get_popular_routes(limit)
        
        return {
            "status": "success",
            "popular_routes": [
                {
                    "route": route.route,
                    "search_count": route.search_count,
                    "priority_score": route.priority_score,
                    "avg_response_time": route.avg_response_time,
                    "last_searched": route.last_searched
                }
                for route in popular_routes
            ],
            "total_routes": len(popular_routes)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve popular routes: {str(e)}")


@router.post("/async/deduplication/cleanup")
async def cleanup_expired_deduplication_requests():
    """
    Manually cleanup expired deduplication requests.
    
    Returns:
        Cleanup results
    """
    try:
        cleaned_count = await request_deduplicator.clear_expired_requests()
        
        return {
            "status": "success",
            "message": f"Cleaned up {cleaned_count} expired deduplication requests",
            "cleaned_count": cleaned_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deduplication cleanup failed: {str(e)}")


@router.get("/async/health")
async def async_services_health_check():
    """
    Comprehensive health check for all async services.
    
    Returns:
        Health status of all async components
    """
    try:
        health_status = {
            "overall_status": "healthy",
            "services": {},
            "issues": []
        }
        
        # Check async search service
        search_stats = async_flight_search.get_performance_stats()
        search_healthy = search_stats.get("error_rate", 0) < 0.1  # Less than 10% error rate
        health_status["services"]["async_search"] = {
            "status": "healthy" if search_healthy else "degraded",
            "error_rate": search_stats.get("error_rate", 0),
            "cache_hit_rate": search_stats.get("cache_hit_rate", 0)
        }
        
        if not search_healthy:
            health_status["issues"].append("High error rate in async search service")
        
        # Check async detail service
        detail_stats = async_flight_detail.get_performance_stats()
        detail_healthy = detail_stats.get("error_rate", 0) < 0.1
        health_status["services"]["async_detail"] = {
            "status": "healthy" if detail_healthy else "degraded",
            "error_rate": detail_stats.get("error_rate", 0),
            "cache_hit_rate": detail_stats.get("cache_hit_rate", 0)
        }
        
        if not detail_healthy:
            health_status["issues"].append("High error rate in async detail service")
        
        # Check provider client
        provider_stats = async_provider_client.get_stats()
        circuit_breakers = provider_stats.get("circuit_breakers", {})
        provider_healthy = all(
            cb.get("state") != "open" 
            for cb in circuit_breakers.values()
        )
        health_status["services"]["provider_client"] = {
            "status": "healthy" if provider_healthy else "degraded",
            "circuit_breakers": circuit_breakers
        }
        
        if not provider_healthy:
            health_status["issues"].append("Circuit breakers are open")
        
        # Check deduplication service
        dedup_stats = request_deduplicator.get_stats()
        dedup_healthy = dedup_stats.get("active_cleanup_task", False)
        health_status["services"]["deduplication"] = {
            "status": "healthy" if dedup_healthy else "warning",
            "pending_requests": dedup_stats.get("pending_requests", 0),
            "deduplication_rate": dedup_stats.get("deduplication_rate", 0)
        }
        
        if not dedup_healthy:
            health_status["issues"].append("Deduplication cleanup task not active")
        
        # Determine overall status
        if health_status["issues"]:
            health_status["overall_status"] = "degraded" if len(health_status["issues"]) < 3 else "unhealthy"
        
        return health_status
        
    except Exception as e:
        return {
            "overall_status": "unhealthy",
            "error": str(e),
            "services": {},
            "issues": ["Health check failed"]
        }
