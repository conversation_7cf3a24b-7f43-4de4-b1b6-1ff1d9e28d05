"""
Cache performance monitoring and alerting system.
Tracks cache metrics, performance, and provides alerting capabilities.
"""

import time
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from dataclasses import dataclass, asdict

from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service import config


@dataclass
class CacheMetrics:
    """Data class for cache performance metrics."""
    timestamp: str
    hit_rate: float
    miss_rate: float
    total_requests: int
    memory_cache_size: int
    response_time_avg: float
    error_rate: float
    cache_invalidations: int


class CacheMonitor:
    """
    Cache performance monitoring system with alerting capabilities.
    """
    
    def __init__(self):
        self.metrics_history: List[CacheMetrics] = []
        self.alert_thresholds = {
            "hit_rate_min": config.performance_config.get("cache_hit_rate_alert_threshold", 0.8),
            "response_time_max": config.performance_config.get("slow_query_threshold_ms", 1000),
            "error_rate_max": 0.05,  # 5% error rate threshold
            "memory_usage_max": 10000  # Maximum memory cache entries
        }
        self.last_alert_time = {}
        self.alert_cooldown = 300  # 5 minutes cooldown between alerts
    
    async def collect_metrics(self) -> CacheMetrics:
        """
        Collect current cache performance metrics.
        
        Returns:
            CacheMetrics: Current cache performance data
        """
        try:
            # Get cache statistics from the cache service
            stats = await flight_cache_service.get_cache_statistics()
            
            # Calculate derived metrics
            total_requests = stats.get("total_requests", 0)
            hits = stats.get("hits", 0)
            misses = stats.get("misses", 0)
            
            hit_rate = (hits / total_requests) if total_requests > 0 else 0
            miss_rate = (misses / total_requests) if total_requests > 0 else 0
            
            # Create metrics object
            metrics = CacheMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                hit_rate=hit_rate,
                miss_rate=miss_rate,
                total_requests=total_requests,
                memory_cache_size=stats.get("memory_cache_size", 0),
                response_time_avg=0.0,  # Would need to be calculated from request logs
                error_rate=0.0,  # Would need to be calculated from error logs
                cache_invalidations=stats.get("invalidations", 0)
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            
            # Keep only last 24 hours of metrics
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            self.metrics_history = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m.timestamp.replace('Z', '+00:00')) > cutoff_time
            ]
            
            return metrics
            
        except Exception as e:
            print(f"Error collecting cache metrics: {str(e)}")
            # Return default metrics on error
            return CacheMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                hit_rate=0.0,
                miss_rate=0.0,
                total_requests=0,
                memory_cache_size=0,
                response_time_avg=0.0,
                error_rate=1.0,  # High error rate to indicate problem
                cache_invalidations=0
            )
    
    async def check_alerts(self, metrics: CacheMetrics) -> List[Dict[str, Any]]:
        """
        Check if any alert thresholds are breached.
        
        Args:
            metrics: Current cache metrics
            
        Returns:
            List of alert messages
        """
        alerts = []
        current_time = time.time()
        
        # Check hit rate
        if metrics.hit_rate < self.alert_thresholds["hit_rate_min"]:
            alert_key = "low_hit_rate"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "warning",
                    "metric": "hit_rate",
                    "current_value": metrics.hit_rate,
                    "threshold": self.alert_thresholds["hit_rate_min"],
                    "message": f"Cache hit rate is low: {metrics.hit_rate:.2%} (threshold: {self.alert_thresholds['hit_rate_min']:.2%})",
                    "timestamp": metrics.timestamp
                })
                self.last_alert_time[alert_key] = current_time
        
        # Check memory usage
        if metrics.memory_cache_size > self.alert_thresholds["memory_usage_max"]:
            alert_key = "high_memory_usage"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "warning",
                    "metric": "memory_cache_size",
                    "current_value": metrics.memory_cache_size,
                    "threshold": self.alert_thresholds["memory_usage_max"],
                    "message": f"Memory cache size is high: {metrics.memory_cache_size} entries (threshold: {self.alert_thresholds['memory_usage_max']})",
                    "timestamp": metrics.timestamp
                })
                self.last_alert_time[alert_key] = current_time
        
        # Check error rate
        if metrics.error_rate > self.alert_thresholds["error_rate_max"]:
            alert_key = "high_error_rate"
            if self._should_send_alert(alert_key, current_time):
                alerts.append({
                    "type": "critical",
                    "metric": "error_rate",
                    "current_value": metrics.error_rate,
                    "threshold": self.alert_thresholds["error_rate_max"],
                    "message": f"Cache error rate is high: {metrics.error_rate:.2%} (threshold: {self.alert_thresholds['error_rate_max']:.2%})",
                    "timestamp": metrics.timestamp
                })
                self.last_alert_time[alert_key] = current_time
        
        return alerts
    
    def _should_send_alert(self, alert_key: str, current_time: float) -> bool:
        """
        Check if enough time has passed since the last alert of this type.
        
        Args:
            alert_key: Unique identifier for the alert type
            current_time: Current timestamp
            
        Returns:
            bool: True if alert should be sent
        """
        last_alert = self.last_alert_time.get(alert_key, 0)
        return (current_time - last_alert) > self.alert_cooldown
    
    async def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Dict containing performance summary
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        # Filter metrics for the specified time period
        recent_metrics = [
            m for m in self.metrics_history 
            if datetime.fromisoformat(m.timestamp.replace('Z', '+00:00')) > cutoff_time
        ]
        
        if not recent_metrics:
            return {
                "period_hours": hours,
                "data_points": 0,
                "message": "No data available for the specified period"
            }
        
        # Calculate summary statistics
        hit_rates = [m.hit_rate for m in recent_metrics]
        memory_sizes = [m.memory_cache_size for m in recent_metrics]
        total_requests = [m.total_requests for m in recent_metrics]
        
        return {
            "period_hours": hours,
            "data_points": len(recent_metrics),
            "hit_rate": {
                "avg": sum(hit_rates) / len(hit_rates),
                "min": min(hit_rates),
                "max": max(hit_rates)
            },
            "memory_cache_size": {
                "avg": sum(memory_sizes) / len(memory_sizes),
                "min": min(memory_sizes),
                "max": max(memory_sizes)
            },
            "total_requests": {
                "current": total_requests[-1] if total_requests else 0,
                "peak": max(total_requests) if total_requests else 0
            },
            "trend": self._calculate_trend(hit_rates),
            "last_updated": recent_metrics[-1].timestamp if recent_metrics else None
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """
        Calculate trend direction for a list of values.
        
        Args:
            values: List of numeric values
            
        Returns:
            str: Trend direction ('improving', 'declining', 'stable')
        """
        if len(values) < 2:
            return "stable"
        
        # Simple trend calculation based on first and last values
        first_half_avg = sum(values[:len(values)//2]) / (len(values)//2)
        second_half_avg = sum(values[len(values)//2:]) / (len(values) - len(values)//2)
        
        if second_half_avg > first_half_avg * 1.05:  # 5% improvement threshold
            return "improving"
        elif second_half_avg < first_half_avg * 0.95:  # 5% decline threshold
            return "declining"
        else:
            return "stable"
    
    async def run_monitoring_cycle(self):
        """
        Run a single monitoring cycle: collect metrics and check alerts.
        
        Returns:
            Dict containing metrics and any alerts
        """
        try:
            # Collect current metrics
            metrics = await self.collect_metrics()
            
            # Check for alerts
            alerts = await self.check_alerts(metrics)
            
            # Log alerts if any
            for alert in alerts:
                print(f"CACHE ALERT [{alert['type'].upper()}]: {alert['message']}")
            
            return {
                "metrics": asdict(metrics),
                "alerts": alerts,
                "status": "success"
            }
            
        except Exception as e:
            print(f"Error in monitoring cycle: {str(e)}")
            return {
                "metrics": None,
                "alerts": [],
                "status": "error",
                "error": str(e)
            }


# Global cache monitor instance
cache_monitor = CacheMonitor()
