from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.microservices.flight_service.shared_service.service import FlightShared
from app.microservices.flight_service.schemas import AirportSearchRequest

# Initialize the FastAPI router for handling shared flight service routes
router = APIRouter()
flight_shared = FlightShared()  # Create an instance of the FlightShared service

@router.post("/airports")
async def airports(request: AirportSearchRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to retrieve a list of airports based on the search text provided.

    Args:
        request_data (dict): A dictionary containing the search parameters,
                             specifically 'search_text' for filtering airport names,
                             codes, or city names.

    BackgroundTasks (BackgroundTasks): Allows executing background tasks
                                       after returning a response.

    Returns:
        list: A list of airports matching the search criteria.
               Returns an empty list if no matches are found.
    """
    # Call the airports method of the FlightShared service to get airport data
    request_data = request.model_dump()
    result = flight_shared.airports(request_data)

    # Return the list of matching airports
    return result