"""Initial flight service migration

Revision ID: 8030155f41ee
Revises: aefaa297f7de
Create Date: 2024-10-31 14:23:05.061178

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8030155f41ee'
down_revision: Union[str, None] = 'aefaa297f7de'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('airports', sa.Column('created_at', sa.DateTime(), nullable=False))
    op.add_column('airports', sa.Column('updated_at', sa.DateTime(), nullable=False))
    op.add_column('airports', sa.Column('is_deleted', sa.Boolean(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('airports', 'is_deleted')
    op.drop_column('airports', 'updated_at')
    op.drop_column('airports', 'created_at')
    # ### end Alembic commands ###
