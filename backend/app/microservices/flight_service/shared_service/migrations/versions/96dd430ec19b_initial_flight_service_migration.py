"""Initial flight service migration

Revision ID: 96dd430ec19b
Revises: 8030155f41ee
Create Date: 2024-10-31 14:24:53.056404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '96dd430ec19b'
down_revision: Union[str, None] = '8030155f41ee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('airports', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('airports', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True)
    op.alter_column('airports', 'is_deleted',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('airports', 'is_deleted',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.alter_column('airports', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    op.alter_column('airports', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False)
    # ### end Alembic commands ###
