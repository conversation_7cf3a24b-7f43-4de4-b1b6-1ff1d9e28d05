# admin.py
import os
from fastapi import FastAP<PERSON>
from fastapi_admin.app import app as admin_app
from fastapi_admin.providers.login import UsernamePasswordProvider
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from fastapi_admin.resources import Model
from fastapi_admin.widgets import inputs, displays
from app.config import get_database_connection  # Assumes you've set up config

# Initialize the FastAPI-Admin app
app = FastAPI()

# Database configurations
DATABASE_URL = os.getenv("DATABASE_URL")  # Replace with your actual database URL
engine = create_async_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=AsyncSession)

# Define the Airport model
class Airport(Model):
    label = "Airport"  # Admin label for the model
    page_pre_title = "Airports"
    page_title = "Manage Airports"
    
    # Form fields in the admin interface
    fields = [
        "id",
        inputs.Text(label="Code"),
        inputs.Text(label="Name"),
        inputs.Text(label="City"),
    ]

# Initialize FastAPI-Admin with configuration
async def init_admin():
    await admin_app.configure(
        admin_secret="mysecret",
        database_url=DATABASE_URL,
        provider=UsernamePasswordProvider(admin_user="admin", admin_password="password"),
        models=[Airport]
    )

# Main function to initialize app and run admin
@app.on_event("startup")
async def startup():
    await init_admin()

app.mount("/admin", admin_app)