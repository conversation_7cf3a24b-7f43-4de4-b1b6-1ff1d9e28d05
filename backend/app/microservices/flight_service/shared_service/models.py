from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String
from app.models import BaseModel,Base


class Airport(BaseModel):
    __tablename__ = 'airports'

    id = Column(Integer, primary_key=True)
    code = Column(String(length=10), nullable=True)
    name = Column(String(length=100), nullable=True)
    country = Column(String(length=100), nullable=True)
    city_name = Column(String(length=100), nullable=True)
    city_code = Column(String(length=10), nullable=True)