"""
Enhanced Airport Search Service with fuzzy matching and intelligent caching.
Optimized for sub-second response times and high accuracy.
"""

import time
import asyncio
from typing import List, Dict, Any, Optional
from difflib import SequenceMatcher
import re

# Handle optional dependencies gracefully
try:
    from app.config import get_database_connection
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    def get_database_connection():
        raise RuntimeError("Database connection not available outside Docker environment")

try:
    from app.microservices.flight_service.cache_service import flight_cache_service
    CACHE_SERVICE_AVAILABLE = True
except ImportError:
    CACHE_SERVICE_AVAILABLE = False
    flight_cache_service = None

try:
    from app.microservices.flight_service.config import cache_timer
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    cache_timer = {"AIRPORT_DATA": 86400}


class EnhancedAirportSearch:
    """
    Enhanced airport search service with fuzzy matching, caching, and intelligent ranking.
    """

    def __init__(self):
        self.cache_service = flight_cache_service
        self.cache_ttl = cache_timer.get("AIRPORT_DATA", 86400)  # 24 hours
        self.search_stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "fuzzy_matches": 0,
            "exact_matches": 0
        }

    async def search_airports(self, search_text: str, country_filter: str = None, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Enhanced airport search with fuzzy matching and intelligent ranking.

        Args:
            search_text: Search query (airport code, name, or city)
            country_filter: Optional country filter (e.g., "India")
            limit: Maximum number of results to return

        Returns:
            List of matching airports with relevance scores
        """
        start_time = time.time()
        self.search_stats["total_searches"] += 1

        # Generate cache key
        cache_key = self._generate_cache_key(search_text, country_filter, limit)

        # Check cache first
        cached_results = await self._get_cached_results(cache_key)
        if cached_results:
            self.search_stats["cache_hits"] += 1
            return cached_results

        # Perform database search
        results = await self._perform_database_search(search_text, country_filter, limit)

        # Cache results
        await self._cache_results(cache_key, results)

        # Add performance metadata
        response_time = (time.time() - start_time) * 1000
        for result in results:
            result["response_time_ms"] = response_time
            result["cache_hit"] = False

        return results

    async def _perform_database_search(self, search_text: str, country_filter: str, limit: int) -> List[Dict[str, Any]]:
        """
        Perform optimized database search with multiple matching strategies.
        """
        if not search_text or len(search_text.strip()) < 1:
            return await self._get_popular_airports(country_filter, limit)

        search_text = search_text.strip()

        # Try different search strategies in order of precision
        results = []

        # 1. Exact code match (highest priority)
        exact_matches = await self._search_by_exact_code(search_text, country_filter)
        results.extend(exact_matches)

        # 2. Code prefix match
        if len(results) < limit:
            prefix_matches = await self._search_by_code_prefix(search_text, country_filter, limit - len(results))
            results.extend(prefix_matches)

        # 3. Name and city fuzzy search
        if len(results) < limit:
            fuzzy_matches = await self._search_by_fuzzy_text(search_text, country_filter, limit - len(results))
            results.extend(fuzzy_matches)

        # 4. Full-text search (if available)
        if len(results) < limit:
            fulltext_matches = await self._search_by_fulltext(search_text, country_filter, limit - len(results))
            results.extend(fulltext_matches)

        # Remove duplicates and rank results
        unique_results = self._deduplicate_and_rank(results, search_text)

        return unique_results[:limit]

    async def _search_by_exact_code(self, search_text: str, country_filter: str) -> List[Dict[str, Any]]:
        """Search for exact airport code matches."""
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            query = """
                SELECT *, 100 as relevance_score, 'exact_code' as match_type
                FROM airports
                WHERE UPPER(code) = UPPER(%s)
            """
            params = [search_text]

            if country_filter:
                query += " AND country = %s"
                params.append(country_filter)

            cursor.execute(query, params)
            results = cursor.fetchall()

            if results:
                self.search_stats["exact_matches"] += len(results)

            return results

        finally:
            cursor.close()
            connection.close()

    async def _search_by_code_prefix(self, search_text: str, country_filter: str, limit: int) -> List[Dict[str, Any]]:
        """Search for airport codes starting with search text."""
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            query = """
                SELECT *, 90 as relevance_score, 'code_prefix' as match_type
                FROM airports
                WHERE UPPER(code) LIKE UPPER(%s)
                ORDER BY code
                LIMIT %s
            """
            params = [f"{search_text}%", limit]

            if country_filter:
                query = query.replace("ORDER BY", "AND country = %s ORDER BY")
                params.insert(-1, country_filter)

            cursor.execute(query, params)
            return cursor.fetchall()

        finally:
            cursor.close()
            connection.close()

    async def _search_by_fuzzy_text(self, search_text: str, country_filter: str, limit: int) -> List[Dict[str, Any]]:
        """Search using fuzzy text matching on names and cities."""
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            # Use LIKE for basic fuzzy matching
            query = """
                SELECT *,
                       CASE
                           WHEN UPPER(name) LIKE UPPER(%s) THEN 85
                           WHEN UPPER(city_name) LIKE UPPER(%s) THEN 80
                           WHEN UPPER(name) LIKE UPPER(%s) THEN 75
                           WHEN UPPER(city_name) LIKE UPPER(%s) THEN 70
                           ELSE 60
                       END as relevance_score,
                       'fuzzy_text' as match_type
                FROM airports
                WHERE (
                    UPPER(name) LIKE UPPER(%s) OR
                    UPPER(city_name) LIKE UPPER(%s) OR
                    UPPER(name) LIKE UPPER(%s) OR
                    UPPER(city_name) LIKE UPPER(%s)
                )
            """

            exact_pattern = f"{search_text}%"
            fuzzy_pattern = f"%{search_text}%"

            params = [
                exact_pattern, exact_pattern,  # Exact prefix matches
                fuzzy_pattern, fuzzy_pattern,  # Fuzzy matches
                exact_pattern, exact_pattern,  # For WHERE clause
                fuzzy_pattern, fuzzy_pattern   # For WHERE clause
            ]

            if country_filter:
                query += " AND country = %s"
                params.append(country_filter)

            query += " ORDER BY relevance_score DESC, name LIMIT %s"
            params.append(limit)

            cursor.execute(query, params)
            results = cursor.fetchall()

            if results:
                self.search_stats["fuzzy_matches"] += len(results)

            return results

        finally:
            cursor.close()
            connection.close()

    async def _search_by_fulltext(self, search_text: str, country_filter: str, limit: int) -> List[Dict[str, Any]]:
        """Search using MySQL full-text search (if available)."""
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            # Check if full-text index exists
            cursor.execute("""
                SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.STATISTICS
                WHERE table_schema = DATABASE()
                AND table_name = 'airports'
                AND index_type = 'FULLTEXT'
            """)

            has_fulltext = cursor.fetchone()["count"] > 0

            if not has_fulltext:
                return []

            query = """
                SELECT *,
                       MATCH(name, city_name, code) AGAINST(%s IN BOOLEAN MODE) as relevance_score,
                       'fulltext' as match_type
                FROM airports
                WHERE MATCH(name, city_name, code) AGAINST(%s IN BOOLEAN MODE)
            """

            # Prepare boolean search query
            boolean_query = f"+{search_text}*"
            params = [boolean_query, boolean_query]

            if country_filter:
                query += " AND country = %s"
                params.append(country_filter)

            query += " ORDER BY relevance_score DESC LIMIT %s"
            params.append(limit)

            cursor.execute(query, params)
            return cursor.fetchall()

        except Exception as e:
            # Fallback if full-text search fails
            print(f"Full-text search failed: {e}")
            return []

        finally:
            cursor.close()
            connection.close()

    async def _get_popular_airports(self, country_filter: str, limit: int) -> List[Dict[str, Any]]:
        """Get popular airports when no search text is provided."""
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)

        try:
            # Return popular Indian airports by default
            popular_codes = ['DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'COK', 'GOI', 'PNQ', 'AMD']

            query = """
                SELECT *, 95 as relevance_score, 'popular' as match_type
                FROM airports
                WHERE code IN ({})
            """.format(','.join(['%s'] * len(popular_codes)))

            params = popular_codes

            if country_filter:
                query += " AND country = %s"
                params.append(country_filter)

            query += " ORDER BY FIELD(code, {}) LIMIT %s".format(','.join(['%s'] * len(popular_codes)))
            params.extend(popular_codes)
            params.append(limit)

            cursor.execute(query, params)
            return cursor.fetchall()

        finally:
            cursor.close()
            connection.close()

    def _deduplicate_and_rank(self, results: List[Dict[str, Any]], search_text: str) -> List[Dict[str, Any]]:
        """Remove duplicates and apply intelligent ranking."""
        seen_codes = set()
        unique_results = []

        for result in results:
            code = result.get('code', '').upper()
            if code not in seen_codes:
                seen_codes.add(code)

                # Apply additional ranking based on search text similarity
                if search_text:
                    similarity_score = self._calculate_similarity(search_text, result)
                    result['relevance_score'] = (result.get('relevance_score', 0) + similarity_score) / 2

                unique_results.append(result)

        # Sort by relevance score
        return sorted(unique_results, key=lambda x: x.get('relevance_score', 0), reverse=True)

    def _calculate_similarity(self, search_text: str, airport: Dict[str, Any]) -> float:
        """Calculate similarity score between search text and airport data."""
        search_text = search_text.lower()

        # Check similarity with different fields
        code_similarity = SequenceMatcher(None, search_text, airport.get('code', '').lower()).ratio() * 100
        name_similarity = SequenceMatcher(None, search_text, airport.get('name', '').lower()).ratio() * 80
        city_similarity = SequenceMatcher(None, search_text, airport.get('city_name', '').lower()).ratio() * 90

        return max(code_similarity, name_similarity, city_similarity)

    def _generate_cache_key(self, search_text: str, country_filter: str, limit: int) -> str:
        """Generate cache key for airport search."""
        key_parts = [
            "airport_search",
            search_text.lower().strip() if search_text else "empty",
            country_filter or "all",
            str(limit)
        ]
        return ":".join(key_parts)

    async def _get_cached_results(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """Get results from cache."""
        try:
            return await self.cache_service.get_from_memory_cache(cache_key)
        except Exception as e:
            print(f"Cache retrieval error: {e}")
            return None

    async def _cache_results(self, cache_key: str, results: List[Dict[str, Any]]) -> None:
        """Cache search results."""
        try:
            # Store in memory cache for faster access
            cache_data = {"airports": results, "cached_at": time.time()}
            await self.cache_service.set_memory_cache(cache_key, cache_data, self.cache_ttl)
        except Exception as e:
            print(f"Cache storage error: {e}")

    def get_search_stats(self) -> Dict[str, Any]:
        """Get search performance statistics."""
        total = self.search_stats["total_searches"]
        if total == 0:
            return self.search_stats

        return {
            **self.search_stats,
            "cache_hit_rate": self.search_stats["cache_hits"] / total,
            "exact_match_rate": self.search_stats["exact_matches"] / total,
            "fuzzy_match_rate": self.search_stats["fuzzy_matches"] / total
        }


# Global instance
enhanced_airport_search = EnhancedAirportSearch()
