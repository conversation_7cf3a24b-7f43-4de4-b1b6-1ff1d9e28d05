"""
The core business logic for the flight shared service is implemented here. 
This file processes incoming requests related to flight information, 
interacts with the database, and retrieves airport data based on user input. 
It’s crucial for implementing shared functionalities within the flight service.
"""

from app.config import get_database_connection

class FlightShared:
    def airports(self, request_data):
        """
        Retrieves a list of airports based on the provided search text.

        Args:
            request_data (dict): A dictionary containing request parameters,
                                 specifically 'search_text' for filtering.

        Returns:
            list: A list of matching airports from the database.
        """
        # Get the search text from the request data, defaulting to an empty string if not provided
        search_text = request_data.get("search_text", "")

        # Establish a database connection
        connection = get_database_connection()
        cursor = connection.cursor(dictionary=True)  # Use dictionary for easy access to columns by name

        # SQL query to fetch airports based on the search text
        query = """
            SELECT * FROM airports
            WHERE 
                code LIKE %s OR 
                name LIKE %s OR 
                city_name LIKE %s
        """

        # Create the pattern for LIKE clause
        like_pattern = f"%{search_text}%"  # This will match any substring within the search text

        # Execute the query with the LIKE pattern for each field
        cursor.execute(query, (like_pattern, like_pattern, like_pattern))

        # Fetch all matching records
        airports = cursor.fetchall()

        # Close the database connection
        connection.close()

        # Return the list of matching airports
        return airports