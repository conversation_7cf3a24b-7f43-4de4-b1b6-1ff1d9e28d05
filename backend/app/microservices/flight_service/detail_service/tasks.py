"""
This file defines background tasks, typically using Celery. It allows you to implement asynchronous processes related to hotel searching, such as caching results or sending notifications. This helps keep the application responsive to user requests.
"""
from app.config import celery_app
from app.config import RedisCache
from app.microservices.flight_service.detail_service.providers.tripjack.request_creator import (
    review_translate_client_to_provider,
)
from app.microservices.flight_service.detail_service.providers.tripjack.response_converter import (
    review_translate_provider_to_client,
)
from app.microservices.flight_service.detail_service.utils import get_detail_ids
from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
from app.microservices.flight_service import config


@celery_app.task()
def fetch_flight_detail_task(provider_name, request_data, tui, context, fare_id):
    # Translate the client request data into the provider's expected format
    provider_payload = review_translate_client_to_provider(context,request_data)
    
    # Construct the API URL for the flight detail provider using the base URL from config
    provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/review"
    
    # Make the API call to the flight detail provider and get the response
    provider_response = make_provider_api_call(provider_api_url, provider_payload)
    
    # Translate the provider's response back into the format expected by the client
    client_response = review_translate_provider_to_client(request_data, provider_response, context)
    
    # Add the TUI back into the client response for reference
    client_response["TUI"] = tui
    
    # Store the client response in Redis cache with a TTL (Time To Live) of fixed seconds
    RedisCache.connection().set_cache(tui, client_response, config.cache_timer.get('FLIGHT_DETAIL'))
    return True