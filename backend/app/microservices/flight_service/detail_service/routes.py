from fastapi import <PERSON><PERSON>outer, HTTPException, BackgroundTasks
from app.microservices.flight_service.detail_service.service import FlightDetail
from app.microservices.flight_service.schemas import PricingRequest, PricingListRequest

# Create an instance of the APIRouter
router = APIRouter()
flight_detail = FlightDetail()  # Instantiate the FlightDetail service


@router.post("/pricing")
async def pricing(request: PricingRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to fetch flight pricing details - matches YAML specification exactly.

    Args:
        request (PricingRequest): A request containing pricing parameters,
                                 including fare information.

    Background Tasks:
        Allows for background processing of tasks if needed (not utilized here).

    Returns:
        dict: The flight pricing details or an error response.
    """
    request_data = request.model_dump()
    result = flight_detail.detail(request_data)  # Call the detail method from FlightDetail service
    return result  # Return the result to the client


@router.post("/pricing_list")
async def pricing_list(request: PricingListRequest, background_tasks: BackgroundTasks):
    """
    Endpoint to retrieve cached flight pricing details - matches YAML specification exactly.

    Args:
        request (PricingListRequest): A request containing the TUI (cache key)
                                     for fetching cached results.

    Background Tasks:
        Allows for background processing of tasks if needed (not utilized here).

    Returns:
        dict: The cached flight pricing details or an error response.
    """
    request_data = request.model_dump()
    result = flight_detail.get_detail(request_data)  # Call the get_detail method from FlightDetail service
    return result  # Return the result to the client