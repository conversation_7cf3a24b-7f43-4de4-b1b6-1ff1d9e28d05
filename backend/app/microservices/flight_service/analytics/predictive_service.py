"""
Predictive analytics service for capacity planning and trend forecasting.
Provides machine learning-based predictions for system optimization and scaling.
"""

import asyncio
import time
import math
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque
import statistics
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score

from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service.cache_service import flight_cache_service
from app.microservices.flight_service.database.connection_pool import db_connection_pool


@dataclass
class PredictionModel:
    """Machine learning model for predictions."""
    model_type: str
    model: Any
    features: List[str]
    target: str
    accuracy: float
    last_trained: float
    training_data_size: int


@dataclass
class Forecast:
    """Prediction forecast result."""
    metric: str
    current_value: float
    predicted_values: List[float]
    prediction_intervals: List[Tuple[float, float]]
    confidence: float
    forecast_horizon: int  # hours
    model_accuracy: float


@dataclass
class CapacityRecommendation:
    """Capacity planning recommendation."""
    component: str
    current_capacity: float
    predicted_demand: float
    recommended_action: str
    urgency: str  # low, medium, high, critical
    estimated_impact: str
    implementation_timeline: str


class PredictiveAnalyticsService:
    """
    Predictive analytics service with machine learning capabilities for forecasting and optimization.
    """
    
    def __init__(self):
        # Historical data storage
        self.historical_data: Dict[str, deque] = {
            "response_times": deque(maxlen=1000),
            "cache_hit_rates": deque(maxlen=1000),
            "request_volumes": deque(maxlen=1000),
            "error_rates": deque(maxlen=1000),
            "database_performance": deque(maxlen=1000)
        }
        
        # Prediction models
        self.models: Dict[str, PredictionModel] = {}
        
        # Service configuration
        self.config = {
            "min_training_samples": 50,
            "model_retrain_interval": 3600,  # 1 hour
            "forecast_horizon_hours": 24,
            "confidence_threshold": 0.7,
            "anomaly_detection_threshold": 2.0  # standard deviations
        }
        
        # Service statistics
        self.service_stats = {
            "total_predictions": 0,
            "models_trained": 0,
            "forecasts_generated": 0,
            "anomalies_detected": 0,
            "capacity_recommendations": 0
        }
        
        # Start background tasks
        self._background_tasks = []
        self._start_background_tasks()
    
    def _start_background_tasks(self):
        """Start background tasks for predictive analytics."""
        self._background_tasks = [
            asyncio.create_task(self._data_collection_loop()),
            asyncio.create_task(self._model_training_loop()),
            asyncio.create_task(self._anomaly_detection_loop())
        ]
    
    async def _data_collection_loop(self):
        """Background loop to collect historical data."""
        while True:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                
                current_time = time.time()
                
                # Collect performance metrics
                dashboard_data = await performance_analytics.get_performance_dashboard()
                
                # Extract and store metrics
                current_metrics = dashboard_data.get("current_metrics", {})
                
                for metric_key, metric_data in current_metrics.items():
                    service, metric_type = metric_key.split(":", 1) if ":" in metric_key else ("unknown", metric_key)
                    value = metric_data.get("current_value", 0)
                    
                    # Store in appropriate historical data
                    if "response_time" in metric_type:
                        self.historical_data["response_times"].append((current_time, value))
                    elif "cache_hit_rate" in metric_type:
                        self.historical_data["cache_hit_rates"].append((current_time, value))
                    elif "error_rate" in metric_type:
                        self.historical_data["error_rates"].append((current_time, value))
                
                # Collect additional metrics
                cache_stats = await flight_cache_service.get_cache_statistics()
                if cache_stats:
                    self.historical_data["request_volumes"].append((current_time, cache_stats.get("total_requests", 0)))
                
                db_stats = db_connection_pool.get_performance_stats()
                if db_stats:
                    query_stats = db_stats.get("query_stats", {})
                    avg_query_time = query_stats.get("total_query_executions", 0)
                    self.historical_data["database_performance"].append((current_time, avg_query_time))
                
            except Exception as e:
                print(f"Error in data collection loop: {str(e)}")
    
    async def _model_training_loop(self):
        """Background loop to train prediction models."""
        while True:
            try:
                await asyncio.sleep(self.config["model_retrain_interval"])
                
                # Train models for each metric
                for metric_name, data in self.historical_data.items():
                    if len(data) >= self.config["min_training_samples"]:
                        await self._train_prediction_model(metric_name, data)
                
            except Exception as e:
                print(f"Error in model training loop: {str(e)}")
    
    async def _anomaly_detection_loop(self):
        """Background loop for anomaly detection."""
        while True:
            try:
                await asyncio.sleep(600)  # Every 10 minutes
                
                # Check for anomalies in recent data
                anomalies = await self._detect_anomalies()
                
                for anomaly in anomalies:
                    print(f"Anomaly detected: {anomaly}")
                    self.service_stats["anomalies_detected"] += 1
                
            except Exception as e:
                print(f"Error in anomaly detection loop: {str(e)}")
    
    async def _train_prediction_model(self, metric_name: str, data: deque) -> Optional[PredictionModel]:
        """
        Train a prediction model for a specific metric.
        
        Args:
            metric_name: Name of the metric
            data: Historical data points
            
        Returns:
            Trained prediction model
        """
        try:
            if len(data) < self.config["min_training_samples"]:
                return None
            
            # Prepare training data
            timestamps = np.array([point[0] for point in data])
            values = np.array([point[1] for point in data])
            
            # Normalize timestamps (use hours from start)
            start_time = timestamps[0]
            X = ((timestamps - start_time) / 3600).reshape(-1, 1)  # Hours
            y = values
            
            # Create polynomial features for better fitting
            poly_features = PolynomialFeatures(degree=2)
            X_poly = poly_features.fit_transform(X)
            
            # Train linear regression model
            model = LinearRegression()
            model.fit(X_poly, y)
            
            # Calculate accuracy
            y_pred = model.predict(X_poly)
            accuracy = r2_score(y, y_pred)
            
            # Create prediction model
            prediction_model = PredictionModel(
                model_type="polynomial_regression",
                model={"regression": model, "poly_features": poly_features, "start_time": start_time},
                features=["time"],
                target=metric_name,
                accuracy=accuracy,
                last_trained=time.time(),
                training_data_size=len(data)
            )
            
            self.models[metric_name] = prediction_model
            self.service_stats["models_trained"] += 1
            
            print(f"Trained model for {metric_name} with accuracy: {accuracy:.3f}")
            
            return prediction_model
            
        except Exception as e:
            print(f"Error training model for {metric_name}: {str(e)}")
            return None
    
    async def generate_forecast(self, metric_name: str, hours_ahead: int = 24) -> Optional[Forecast]:
        """
        Generate forecast for a specific metric.
        
        Args:
            metric_name: Name of the metric to forecast
            hours_ahead: Number of hours to forecast ahead
            
        Returns:
            Forecast results
        """
        try:
            if metric_name not in self.models:
                print(f"No model available for {metric_name}")
                return None
            
            model_info = self.models[metric_name]
            model_data = model_info.model
            
            # Get current value
            current_data = self.historical_data.get(metric_name, deque())
            if not current_data:
                return None
            
            current_value = current_data[-1][1]
            current_time = time.time()
            
            # Generate predictions
            future_times = np.array([current_time + (i * 3600) for i in range(1, hours_ahead + 1)])
            X_future = ((future_times - model_data["start_time"]) / 3600).reshape(-1, 1)
            X_future_poly = model_data["poly_features"].transform(X_future)
            
            predictions = model_data["regression"].predict(X_future_poly)
            
            # Calculate prediction intervals (simplified)
            prediction_std = np.std(predictions)
            prediction_intervals = [
                (pred - 1.96 * prediction_std, pred + 1.96 * prediction_std)
                for pred in predictions
            ]
            
            # Calculate confidence based on model accuracy and data recency
            confidence = model_info.accuracy * 0.8  # Reduce confidence for future predictions
            
            forecast = Forecast(
                metric=metric_name,
                current_value=current_value,
                predicted_values=predictions.tolist(),
                prediction_intervals=prediction_intervals,
                confidence=confidence,
                forecast_horizon=hours_ahead,
                model_accuracy=model_info.accuracy
            )
            
            self.service_stats["forecasts_generated"] += 1
            self.service_stats["total_predictions"] += len(predictions)
            
            return forecast
            
        except Exception as e:
            print(f"Error generating forecast for {metric_name}: {str(e)}")
            return None
    
    async def _detect_anomalies(self) -> List[Dict[str, Any]]:
        """Detect anomalies in recent data."""
        anomalies = []
        
        try:
            for metric_name, data in self.historical_data.items():
                if len(data) < 10:  # Need at least 10 data points
                    continue
                
                # Get recent values
                recent_values = [point[1] for point in list(data)[-20:]]  # Last 20 points
                
                # Calculate statistics
                mean_value = statistics.mean(recent_values)
                std_value = statistics.stdev(recent_values) if len(recent_values) > 1 else 0
                
                # Check latest value for anomaly
                latest_value = recent_values[-1]
                z_score = abs(latest_value - mean_value) / std_value if std_value > 0 else 0
                
                if z_score > self.config["anomaly_detection_threshold"]:
                    anomalies.append({
                        "metric": metric_name,
                        "value": latest_value,
                        "expected_range": (mean_value - 2*std_value, mean_value + 2*std_value),
                        "z_score": z_score,
                        "severity": "high" if z_score > 3 else "medium",
                        "timestamp": time.time()
                    })
            
        except Exception as e:
            print(f"Error detecting anomalies: {str(e)}")
        
        return anomalies
    
    async def generate_capacity_recommendations(self) -> List[CapacityRecommendation]:
        """
        Generate capacity planning recommendations based on forecasts.
        
        Returns:
            List of capacity recommendations
        """
        recommendations = []
        
        try:
            # Generate forecasts for key metrics
            response_time_forecast = await self.generate_forecast("response_times", 24)
            cache_hit_forecast = await self.generate_forecast("cache_hit_rates", 24)
            request_volume_forecast = await self.generate_forecast("request_volumes", 24)
            
            # Analyze response time trends
            if response_time_forecast and response_time_forecast.confidence > 0.5:
                max_predicted = max(response_time_forecast.predicted_values)
                current_value = response_time_forecast.current_value
                
                if max_predicted > current_value * 1.5:  # 50% increase predicted
                    recommendations.append(CapacityRecommendation(
                        component="API Response Time",
                        current_capacity=current_value,
                        predicted_demand=max_predicted,
                        recommended_action="Scale up application servers or optimize slow endpoints",
                        urgency="high" if max_predicted > current_value * 2 else "medium",
                        estimated_impact="Prevent response time degradation",
                        implementation_timeline="1-2 days"
                    ))
            
            # Analyze cache performance
            if cache_hit_forecast and cache_hit_forecast.confidence > 0.5:
                min_predicted = min(cache_hit_forecast.predicted_values)
                current_value = cache_hit_forecast.current_value
                
                if min_predicted < current_value * 0.8:  # 20% decrease predicted
                    recommendations.append(CapacityRecommendation(
                        component="Cache Hit Rate",
                        current_capacity=current_value,
                        predicted_demand=min_predicted,
                        recommended_action="Increase cache memory or optimize cache warming strategy",
                        urgency="medium",
                        estimated_impact="Maintain cache effectiveness",
                        implementation_timeline="1-3 days"
                    ))
            
            # Analyze request volume trends
            if request_volume_forecast and request_volume_forecast.confidence > 0.5:
                max_predicted = max(request_volume_forecast.predicted_values)
                current_value = request_volume_forecast.current_value
                
                if max_predicted > current_value * 1.3:  # 30% increase predicted
                    recommendations.append(CapacityRecommendation(
                        component="Request Volume",
                        current_capacity=current_value,
                        predicted_demand=max_predicted,
                        recommended_action="Prepare for increased load - scale infrastructure",
                        urgency="medium",
                        estimated_impact="Handle increased traffic without degradation",
                        implementation_timeline="3-7 days"
                    ))
            
            # General recommendations if no specific issues
            if not recommendations:
                recommendations.append(CapacityRecommendation(
                    component="Overall System",
                    current_capacity=1.0,
                    predicted_demand=1.0,
                    recommended_action="System capacity appears adequate for predicted load",
                    urgency="low",
                    estimated_impact="Maintain current performance levels",
                    implementation_timeline="Ongoing monitoring"
                ))
            
            self.service_stats["capacity_recommendations"] += len(recommendations)
            
        except Exception as e:
            print(f"Error generating capacity recommendations: {str(e)}")
        
        return recommendations
    
    async def get_predictive_insights(self) -> Dict[str, Any]:
        """
        Get comprehensive predictive insights and analytics.
        
        Returns:
            Predictive analytics insights
        """
        try:
            insights = {
                "forecasts": {},
                "anomalies": await self._detect_anomalies(),
                "capacity_recommendations": await self.generate_capacity_recommendations(),
                "model_performance": {},
                "trends": {}
            }
            
            # Generate forecasts for all available metrics
            for metric_name in self.historical_data.keys():
                if metric_name in self.models:
                    forecast = await self.generate_forecast(metric_name, 12)  # 12 hours
                    if forecast:
                        insights["forecasts"][metric_name] = {
                            "current_value": forecast.current_value,
                            "predicted_trend": "increasing" if forecast.predicted_values[-1] > forecast.current_value else "decreasing",
                            "confidence": forecast.confidence,
                            "max_predicted": max(forecast.predicted_values),
                            "min_predicted": min(forecast.predicted_values)
                        }
            
            # Model performance summary
            for metric_name, model in self.models.items():
                insights["model_performance"][metric_name] = {
                    "accuracy": model.accuracy,
                    "last_trained": model.last_trained,
                    "training_data_size": model.training_data_size,
                    "model_type": model.model_type
                }
            
            # Trend analysis
            for metric_name, data in self.historical_data.items():
                if len(data) >= 10:
                    recent_values = [point[1] for point in list(data)[-10:]]
                    trend_direction = "stable"
                    
                    if len(recent_values) > 1:
                        slope = (recent_values[-1] - recent_values[0]) / len(recent_values)
                        if slope > 0.1:
                            trend_direction = "increasing"
                        elif slope < -0.1:
                            trend_direction = "decreasing"
                    
                    insights["trends"][metric_name] = {
                        "direction": trend_direction,
                        "recent_average": statistics.mean(recent_values),
                        "volatility": statistics.stdev(recent_values) if len(recent_values) > 1 else 0
                    }
            
            return insights
            
        except Exception as e:
            print(f"Error getting predictive insights: {str(e)}")
            return {"error": str(e)}
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get predictive analytics service statistics."""
        return {
            **self.service_stats,
            "models_available": len(self.models),
            "historical_data_points": sum(len(data) for data in self.historical_data.values()),
            "background_tasks_running": len([t for t in self._background_tasks if not t.done()]),
            "config": self.config
        }
    
    async def shutdown(self):
        """Shutdown the predictive analytics service."""
        # Cancel background tasks
        for task in self._background_tasks:
            task.cancel()
        
        print("Predictive analytics service shutdown complete")


# Global predictive analytics service instance
predictive_analytics = PredictiveAnalyticsService()
