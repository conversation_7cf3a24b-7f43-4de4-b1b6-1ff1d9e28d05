"""
Enhanced request deduplication service optimized for 3-second response target.
Implements intelligent request merging, response sharing, and performance optimization.
"""

import asyncio
import time
import hashlib
import json
from typing import Dict, Any, Optional, Set, List
from dataclasses import dataclass, field

from app.config import RedisCache
from app.microservices.flight_service import config


@dataclass
class PendingRequest:
    """Represents a pending request waiting for completion."""
    request_hash: str
    cache_key: str
    created_at: float
    request_type: str
    waiters: Set[asyncio.Future] = field(default_factory=set)
    completed: bool = False
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    priority: int = 0  # Higher priority requests get processed first


class RequestDeduplicator:
    """
    Advanced request deduplication service optimized for sub-3-second response times.
    """

    def __init__(self):
        self.redis_client = RedisCache.connection()
        self.pending_requests: Dict[str, PendingRequest] = {}
        self.deduplication_stats = {
            "total_requests": 0,
            "duplicate_requests": 0,
            "merged_requests": 0,
            "cache_hits": 0,
            "pending_requests_count": 0,
            "timeout_requests": 0,
            "successful_merges": 0
        }

        # Enhanced configuration from config
        self.deduplication_window = config.deduplication_config["window_seconds"]
        self.max_pending_requests = config.deduplication_config["max_pending_requests"]
        self.cleanup_interval = config.deduplication_config["cleanup_interval_seconds"]
        self.enabled = config.deduplication_config["enabled"]

        # Performance optimization settings
        self.fast_response_threshold = 1000  # 1 second for fast responses
        self.priority_boost_threshold = 2000  # 2 seconds for priority boost

        # Start cleanup task if enabled
        if self.enabled:
            asyncio.create_task(self._periodic_cleanup())

    def generate_request_hash(self, request_data: Dict[str, Any], request_type: str) -> str:
        """
        Generate a unique hash for request deduplication with performance optimization.

        Args:
            request_data: Request parameters
            request_type: Type of request (flight_search, flight_detail, etc.)

        Returns:
            Unique hash string for the request
        """
        if not self.enabled:
            return f"disabled_{int(time.time() * 1000)}"

        # Normalize request data for consistent hashing
        normalized_data = self._normalize_request_data(request_data, request_type)

        # Create hash with performance optimization
        hash_input = f"{request_type}:{json.dumps(normalized_data, sort_keys=True, separators=(',', ':'))}"
        return hashlib.sha256(hash_input.encode()).hexdigest()[:12]  # Shorter hash for better performance

    def _normalize_request_data(self, request_data: Dict[str, Any], request_type: str) -> Dict[str, Any]:
        """Normalize request data for consistent hashing with performance optimization."""
        if request_type == "flight_search":
            # Optimized normalization for flight search
            trips = request_data.get("Trips", [])
            if not trips:
                return {"empty": True}

            first_trip = trips[0]
            return {
                "route": f"{first_trip.get('From', '').upper()}-{first_trip.get('To', '').upper()}",
                "date": first_trip.get("OnwardDate", ""),
                "return": first_trip.get("ReturnDate", "") if "ReturnDate" in first_trip else None,
                "pax": f"{request_data.get('ADT', 0)}-{request_data.get('CHD', 0)}-{request_data.get('INF', 0)}",
                "cabin": request_data.get("Cabin", "E"),
                "fare": request_data.get("FareType", "")
            }

        elif request_type == "flight_detail":
            return {
                "fare_id": request_data.get("FareId", ""),
                "pax": f"{request_data.get('ADT', 0)}-{request_data.get('CHD', 0)}-{request_data.get('INF', 0)}"
            }

        # Simplified normalization for other types
        return {"type": request_type, "data": str(hash(str(request_data)))}

    async def check_duplicate_request(self, request_hash: str) -> Optional[Dict[str, Any]]:
        """
        Check if this request is a duplicate with optimized performance.

        Args:
            request_hash: Hash of the request

        Returns:
            Cached result if available, None otherwise
        """
        if not self.enabled:
            return None

        self.deduplication_stats["total_requests"] += 1
        start_time = time.time()

        try:
            # Fast path: Check if request is currently pending
            if request_hash in self.pending_requests:
                pending_req = self.pending_requests[request_hash]

                if pending_req.completed and pending_req.result:
                    self.deduplication_stats["duplicate_requests"] += 1
                    return pending_req.result

                # Request is still pending - wait with timeout optimization
                future = asyncio.Future()
                pending_req.waiters.add(future)

                try:
                    # Dynamic timeout based on request age
                    request_age = time.time() - pending_req.created_at
                    timeout = max(5.0, 25.0 - request_age)  # Shorter timeout for older requests

                    result = await asyncio.wait_for(future, timeout=timeout)
                    self.deduplication_stats["merged_requests"] += 1
                    self.deduplication_stats["successful_merges"] += 1
                    return result

                except asyncio.TimeoutError:
                    pending_req.waiters.discard(future)
                    self.deduplication_stats["timeout_requests"] += 1
                    return None

            # Check Redis for recent results with optimized key
            recent_result = self.redis_client.get_cache(f"dedup:{request_hash}")
            if recent_result:
                self.deduplication_stats["cache_hits"] += 1
                return recent_result

            return None

        except Exception as e:
            print(f"Deduplication check error: {str(e)}")
            return None

        finally:
            # Track performance
            check_time = (time.time() - start_time) * 1000
            if check_time > 100:  # Log slow deduplication checks
                print(f"Slow deduplication check: {check_time:.2f}ms for hash {request_hash}")

    async def register_pending_request(self, request_hash: str, cache_key: str, request_type: str = "unknown") -> bool:
        """
        Register a new pending request with priority handling.

        Args:
            request_hash: Hash of the request
            cache_key: Cache key for storing results
            request_type: Type of request

        Returns:
            True if registered successfully, False if already exists
        """
        if not self.enabled:
            return True

        if request_hash in self.pending_requests:
            return False

        # Check capacity and cleanup if needed
        if len(self.pending_requests) >= self.max_pending_requests:
            await self._emergency_cleanup()

        # Determine priority based on request type
        priority = self._calculate_request_priority(request_type)

        self.pending_requests[request_hash] = PendingRequest(
            request_hash=request_hash,
            cache_key=cache_key,
            created_at=time.time(),
            request_type=request_type,
            priority=priority
        )

        self.deduplication_stats["pending_requests_count"] = len(self.pending_requests)
        return True

    def _calculate_request_priority(self, request_type: str) -> int:
        """Calculate request priority for processing order."""
        priority_map = {
            "flight_search": 10,  # High priority
            "flight_detail": 8,   # Medium-high priority
            "flight_pricing": 8,  # Medium-high priority
            "booking": 6,         # Medium priority
            "default": 5          # Default priority
        }
        return priority_map.get(request_type, priority_map["default"])

    async def complete_request(self, request_hash: str, result: Dict[str, Any]) -> None:
        """
        Mark a pending request as completed with optimized notification.

        Args:
            request_hash: Hash of the completed request
            result: Result data
        """
        if not self.enabled or request_hash not in self.pending_requests:
            return

        pending_req = self.pending_requests[request_hash]
        pending_req.completed = True
        pending_req.result = result

        # Notify all waiters efficiently
        waiters_to_notify = list(pending_req.waiters)
        for waiter in waiters_to_notify:
            if not waiter.done():
                try:
                    waiter.set_result(result)
                except Exception as e:
                    print(f"Error notifying waiter: {str(e)}")

        # Store result in Redis with optimized TTL
        try:
            ttl = self._calculate_result_ttl(pending_req.request_type)
            self.redis_client.set_cache(f"dedup:{request_hash}", result, ttl)
        except Exception as e:
            print(f"Error caching deduplication result: {str(e)}")

        # Schedule cleanup
        asyncio.create_task(self._cleanup_completed_request(request_hash, delay=5))

    def _calculate_result_ttl(self, request_type: str) -> int:
        """Calculate TTL for deduplication cache based on request type."""
        ttl_map = {
            "flight_search": 300,    # 5 minutes
            "flight_detail": 180,    # 3 minutes
            "flight_pricing": 120,   # 2 minutes
            "booking": 600,          # 10 minutes
            "default": 300           # 5 minutes default
        }
        return ttl_map.get(request_type, ttl_map["default"])

    async def fail_request(self, request_hash: str, error: str) -> None:
        """
        Mark a pending request as failed with optimized cleanup.

        Args:
            request_hash: Hash of the failed request
            error: Error message
        """
        if not self.enabled or request_hash not in self.pending_requests:
            return

        pending_req = self.pending_requests[request_hash]
        pending_req.completed = True
        pending_req.error = error

        # Notify all waiters with exception
        waiters_to_notify = list(pending_req.waiters)
        for waiter in waiters_to_notify:
            if not waiter.done():
                try:
                    waiter.set_exception(Exception(error))
                except Exception as e:
                    print(f"Error notifying waiter of failure: {str(e)}")

        # Immediate cleanup for failed requests
        await self._cleanup_completed_request(request_hash, delay=1)

    async def get_pending_result(self, request_hash: str) -> Optional[Dict[str, Any]]:
        """Get result from pending request if available."""
        if not self.enabled or request_hash not in self.pending_requests:
            return None

        pending_req = self.pending_requests[request_hash]
        if pending_req.completed and pending_req.result:
            return pending_req.result
        return None

    async def _cleanup_completed_request(self, request_hash: str, delay: int = 5) -> None:
        """Clean up a completed request after delay."""
        await asyncio.sleep(delay)
        if request_hash in self.pending_requests:
            del self.pending_requests[request_hash]
            self.deduplication_stats["pending_requests_count"] = len(self.pending_requests)

    async def _emergency_cleanup(self) -> None:
        """Emergency cleanup when at capacity."""
        current_time = time.time()

        # Remove oldest completed requests first
        completed_requests = [
            (hash_key, req) for hash_key, req in self.pending_requests.items()
            if req.completed
        ]

        # Sort by creation time and remove oldest
        completed_requests.sort(key=lambda x: x[1].created_at)
        cleanup_count = min(len(completed_requests), self.max_pending_requests // 4)

        for i in range(cleanup_count):
            hash_key, _ = completed_requests[i]
            del self.pending_requests[hash_key]

        # If still at capacity, remove oldest pending requests
        if len(self.pending_requests) >= self.max_pending_requests:
            await self._cleanup_old_requests(force=True)

        self.deduplication_stats["pending_requests_count"] = len(self.pending_requests)

    async def _cleanup_old_requests(self, force: bool = False) -> None:
        """Clean up old pending requests."""
        current_time = time.time()
        expired_requests = []

        threshold = self.deduplication_window if not force else self.deduplication_window // 2

        for request_hash, pending_req in self.pending_requests.items():
            if current_time - pending_req.created_at > threshold:
                expired_requests.append(request_hash)

        for request_hash in expired_requests:
            pending_req = self.pending_requests[request_hash]

            # Notify waiters that request expired
            for waiter in pending_req.waiters:
                if not waiter.done():
                    try:
                        waiter.set_exception(asyncio.TimeoutError("Request deduplication timeout"))
                    except Exception:
                        pass

            del self.pending_requests[request_hash]

        self.deduplication_stats["pending_requests_count"] = len(self.pending_requests)

    async def _periodic_cleanup(self) -> None:
        """Periodic cleanup of old requests."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_old_requests()
            except Exception as e:
                print(f"Deduplication cleanup error: {str(e)}")

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive deduplication statistics."""
        total_requests = max(self.deduplication_stats["total_requests"], 1)

        return {
            **self.deduplication_stats,
            "enabled": self.enabled,
            "deduplication_rate": self.deduplication_stats["duplicate_requests"] / total_requests,
            "merge_rate": self.deduplication_stats["merged_requests"] / total_requests,
            "cache_hit_rate": self.deduplication_stats["cache_hits"] / total_requests,
            "timeout_rate": self.deduplication_stats["timeout_requests"] / total_requests,
            "success_rate": self.deduplication_stats["successful_merges"] / max(self.deduplication_stats["merged_requests"], 1),
            "current_pending_requests": len(self.pending_requests),
            "configuration": {
                "window_seconds": self.deduplication_window,
                "max_pending": self.max_pending_requests,
                "cleanup_interval": self.cleanup_interval
            }
        }


# Global request deduplicator instance
request_deduplicator = RequestDeduplicator()
