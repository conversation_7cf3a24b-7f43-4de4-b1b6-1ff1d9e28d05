def minutes_to_hours_minutes(minutes=None):
    if minutes:
        hours = int(minutes) // 60
        remaining_minutes = int(minutes) % 60
        return f"{hours:02d}h {remaining_minutes:02d}m"
    else:
        return ""
   
def extract_terminal_number(terminal_name):
    # Check if the input is None or an empty string
    if not terminal_name:
        return None  # Return None if the input is invalid
   
    # Remove "Terminal" from the string and strip any leading/trailing spaces
    return terminal_name.replace("Terminal", "").strip()


def format_fare_identifier(fareIdentifier,fare_class):
    if "PUBLISHED" in fareIdentifier:
        if "ECONOMY" in fare_class:
            fareIdentifier = "ECO"
        elif "BUSINESS" in fare_class:
            fareIdentifier = "BUSINESS"
        elif "PREMIUM ECONOMY" in fare_class:
            fareIdentifier = "PREMIUM ECONOMY"
    elif "CCL" in fareIdentifier:
        if "BUSINESS" in fare_class:
            fareIdentifier.replace("CCL","BUSINESS")
    elif "PEY" in fareIdentifier:
        if "PREMIUM_ECONOMY" in fare_class:
            fareIdentifier.replace("PEY", "PREMIUM ECONOMY")
    return fareIdentifier