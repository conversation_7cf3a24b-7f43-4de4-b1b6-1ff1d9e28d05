"""
Async provider utilities with circuit breaker pattern, timeout protection, and retry logic.
Provides robust external API integration with intelligent failure handling.
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass, field

from app.microservices.flight_service import config


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5  # Number of failures before opening
    recovery_timeout: int = 60  # Seconds before trying half-open
    success_threshold: int = 3  # Successes needed to close from half-open
    timeout_seconds: int = 30   # Request timeout


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics."""
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    total_requests: int = 0
    total_failures: int = 0
    total_successes: int = 0


class AsyncCircuitBreaker:
    """
    Async circuit breaker implementation for external API calls.
    """

    def __init__(self, name: str, config: CircuitBreakerConfig = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.stats = CircuitBreakerStats()
        self._lock = asyncio.Lock()

    async def call(self, func, *args, **kwargs):
        """
        Execute function with circuit breaker protection.

        Args:
            func: Async function to execute
            *args, **kwargs: Arguments for the function

        Returns:
            Function result or raises CircuitBreakerOpenError
        """
        async with self._lock:
            self.stats.total_requests += 1

            # Check if circuit is open
            if self.stats.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.stats.state = CircuitState.HALF_OPEN
                    print(f"Circuit breaker {self.name}: Attempting reset (HALF_OPEN)")
                else:
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is OPEN")

        try:
            # Execute the function
            result = await func(*args, **kwargs)
            await self._on_success()
            return result

        except Exception as e:
            await self._on_failure()
            raise

    async def _on_success(self):
        """Handle successful request."""
        async with self._lock:
            self.stats.success_count += 1
            self.stats.total_successes += 1
            self.stats.last_success_time = time.time()

            if self.stats.state == CircuitState.HALF_OPEN:
                if self.stats.success_count >= self.config.success_threshold:
                    self.stats.state = CircuitState.CLOSED
                    self.stats.failure_count = 0
                    self.stats.success_count = 0
                    print(f"Circuit breaker {self.name}: Reset to CLOSED")

    async def _on_failure(self):
        """Handle failed request."""
        async with self._lock:
            self.stats.failure_count += 1
            self.stats.total_failures += 1
            self.stats.last_failure_time = time.time()

            if self.stats.failure_count >= self.config.failure_threshold:
                self.stats.state = CircuitState.OPEN
                print(f"Circuit breaker {self.name}: Opened due to failures")

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.stats.last_failure_time is None:
            return True

        return (time.time() - self.stats.last_failure_time) >= self.config.recovery_timeout

    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        return {
            "name": self.name,
            "state": self.stats.state.value,
            "failure_count": self.stats.failure_count,
            "success_count": self.stats.success_count,
            "total_requests": self.stats.total_requests,
            "total_failures": self.stats.total_failures,
            "total_successes": self.stats.total_successes,
            "failure_rate": self.stats.total_failures / max(self.stats.total_requests, 1),
            "last_failure_time": self.stats.last_failure_time,
            "last_success_time": self.stats.last_success_time
        }


class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open."""
    pass


class AsyncProviderClient:
    """
    Async HTTP client with circuit breaker, retry logic, and timeout protection.
    """

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.circuit_breakers: Dict[str, AsyncCircuitBreaker] = {}
        self.request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "timeout_requests": 0,
            "circuit_breaker_blocks": 0
        }

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={"User-Agent": "FastTravel-FlightService/1.0"}
            )

    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()

    def get_circuit_breaker(self, provider_name: str) -> AsyncCircuitBreaker:
        """Get or create circuit breaker for provider."""
        if provider_name not in self.circuit_breakers:
            self.circuit_breakers[provider_name] = AsyncCircuitBreaker(
                name=provider_name,
                config=CircuitBreakerConfig(
                    failure_threshold=5,
                    recovery_timeout=60,
                    success_threshold=3,
                    timeout_seconds=30
                )
            )
        return self.circuit_breakers[provider_name]

    async def make_request(
        self,
        url: str,
        payload: Dict[str, Any],
        provider_name: str = "default",
        headers: Optional[Dict[str, str]] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ) -> Dict[str, Any]:
        """
        Make async HTTP request with circuit breaker protection.

        Args:
            url: Request URL
            payload: Request payload
            provider_name: Provider identifier for circuit breaker
            headers: Additional headers
            max_retries: Maximum retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            Response data as dictionary

        Raises:
            AsyncProviderError: On request failure
            CircuitBreakerOpenError: When circuit breaker is open
        """
        await self._ensure_session()

        # Prepare headers
        request_headers = {
            "Content-Type": "application/json"
        }
        if headers:
            request_headers.update(headers)

        # Get circuit breaker for this provider
        circuit_breaker = self.get_circuit_breaker(provider_name)

        # Track request
        self.request_stats["total_requests"] += 1

        try:
            # Execute request with circuit breaker protection
            result = await circuit_breaker.call(
                self._execute_request,
                url, payload, request_headers, max_retries, retry_delay
            )

            self.request_stats["successful_requests"] += 1
            return result

        except CircuitBreakerOpenError:
            self.request_stats["circuit_breaker_blocks"] += 1
            raise
        except Exception as e:
            self.request_stats["failed_requests"] += 1

            # Enhanced error logging for TripJack API
            print("=" * 80)
            print("🚨 ASYNC TRIPJACK API ERROR DETECTED 🚨")
            print("=" * 80)
            print(f"Provider: {provider_name}")
            print(f"URL: {url}")
            print(f"Request Headers: {request_headers}")
            print(f"Request Payload: {json.dumps(payload, indent=2) if payload else 'None'}")
            print(f"Error Type: {type(e).__name__}")
            print(f"Error Message: {str(e)}")
            print("=" * 80)
            print("END ASYNC TRIPJACK API ERROR")
            print("=" * 80)

            raise AsyncProviderError(f"Request failed: {str(e)}") from e

    async def _execute_request(
        self,
        url: str,
        payload: Dict[str, Any],
        headers: Dict[str, str],
        max_retries: int,
        retry_delay: float
    ) -> Dict[str, Any]:
        """Execute HTTP request with retry logic."""
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                async with self.session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}"
                        )

            except asyncio.TimeoutError as e:
                self.request_stats["timeout_requests"] += 1
                last_exception = e
                if attempt < max_retries:
                    await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
                raise

            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    await asyncio.sleep(retry_delay * (2 ** attempt))
                    continue
                raise

        # If we get here, all retries failed
        raise last_exception

    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        circuit_breaker_stats = {
            name: cb.get_stats()
            for name, cb in self.circuit_breakers.items()
        }

        return {
            "request_stats": self.request_stats,
            "circuit_breakers": circuit_breaker_stats,
            "active_circuit_breakers": len(self.circuit_breakers)
        }


class AsyncProviderError(Exception):
    """Exception for async provider errors."""
    pass


# Global async provider client
async_provider_client = AsyncProviderClient()


async def make_async_provider_call(
    provider_url: str,
    provider_request: Dict[str, Any],
    provider_name: str = "tripjack",
    api_key: Optional[str] = None
) -> Dict[str, Any]:
    """
    Make async provider API call with circuit breaker protection.

    Args:
        provider_url: Provider API URL
        provider_request: Request payload
        provider_name: Provider identifier
        api_key: API key for authentication

    Returns:
        Provider response data

    Raises:
        AsyncProviderError: On request failure
        CircuitBreakerOpenError: When circuit breaker is open
    """
    # Prepare headers
    headers = {}
    if api_key:
        headers["apikey"] = api_key
    elif provider_name == "tripjack":
        headers["apikey"] = config.PROVIDER_CONFIG.get('tripjack', {}).get('api_key', '')

    # Make async request
    async with async_provider_client:
        return await async_provider_client.make_request(
            url=provider_url,
            payload=provider_request,
            provider_name=provider_name,
            headers=headers,
            max_retries=3,
            retry_delay=1.0
        )
