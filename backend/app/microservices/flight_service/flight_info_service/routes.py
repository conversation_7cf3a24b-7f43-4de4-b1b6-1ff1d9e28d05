from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.microservices.flight_service.detail_service.service import FlightDetail

# Initialize the FastAPI router for handling flight detail service routes
router = APIRouter()
flight_detail = FlightDetail()  # Create an instance of the FlightDetail service

@router.post("/details")
async def flight_information(request_data: dict, background_tasks: BackgroundTasks):
    """
    Endpoint to retrieve detailed information about a flight based on provided request data.

    Args:
        request_data (dict): A dictionary containing the search parameters, 
                             which may include identifiers like fare_id for fetching 
                             flight details.

        BackgroundTasks (BackgroundTasks): Allows executing background tasks 
                                           after returning a response.

    Returns:
        dict: A dictionary containing flight details or an error message if 
              the request fails or the flight details are not found.
    """
    try:
        # Call the detail method of the FlightDetail service to get flight information
        result = flight_detail.detail(request_data)
        return result  # Return the retrieved flight details
    except HTTPException as e:
        # If an HTTP exception occurs, raise it to return an appropriate error response
        raise e
    except Exception as e:
        # Handle any other exceptions that may arise
        raise HTTPException(status_code=500, detail=str(e))  # Return a 500 error with the exception message