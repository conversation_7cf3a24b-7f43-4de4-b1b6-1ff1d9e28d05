"""
Advanced monitoring and analytics routes for Phase 4 implementation.
Provides WebSocket dashboards, automated reporting, and predictive analytics endpoints.
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
import json

from app.microservices.flight_service.dashboard.realtime_service import realtime_dashboard
from app.microservices.flight_service.reporting.automated_service import (
    automated_reporting, ReportType, ReportFormat, ReportSchedule
)
from app.microservices.flight_service.analytics.predictive_service import predictive_analytics


# Pydantic models for request validation
class ReportGenerationRequest(BaseModel):
    report_type: str
    format: str = "json"
    hours: int = 24


class ScheduleReportRequest(BaseModel):
    schedule_id: str
    report_type: str
    schedule_cron: str
    recipients: List[str]
    format: str = "html"
    enabled: bool = True


class ForecastRequest(BaseModel):
    metric_name: str
    hours_ahead: int = 24


class WebSocketMessage(BaseModel):
    type: str
    data: Optional[Dict[str, Any]] = None


# Create router instance
router = APIRouter()


@router.websocket("/dashboard/realtime")
async def websocket_dashboard(websocket: WebSocket):
    """
    WebSocket endpoint for real-time dashboard streaming.
    
    Provides live metrics, alerts, and system status updates.
    """
    client_id = None
    try:
        # Connect client
        client_id = await realtime_dashboard.connect_client(websocket)
        
        # Handle messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle client message
                await realtime_dashboard.handle_client_message(client_id, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await realtime_dashboard._send_error(client_id, "Invalid JSON message")
            except Exception as e:
                await realtime_dashboard._send_error(client_id, f"Error processing message: {str(e)}")
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket error: {str(e)}")
    finally:
        if client_id:
            await realtime_dashboard.disconnect_client(client_id)


@router.get("/dashboard/service-stats")
async def get_dashboard_service_stats():
    """
    Get real-time dashboard service statistics.
    
    Returns:
        Dashboard service performance metrics
    """
    try:
        stats = realtime_dashboard.get_service_stats()
        return {
            "status": "success",
            "dashboard_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard stats: {str(e)}")


@router.post("/dashboard/broadcast-metric")
async def broadcast_custom_metric(
    metric_type: str,
    value: float,
    service: str,
    metadata: Optional[Dict[str, Any]] = None
):
    """
    Broadcast a custom metric to dashboard clients.
    
    Args:
        metric_type: Type of metric
        value: Metric value
        service: Service name
        metadata: Additional metadata
        
    Returns:
        Confirmation of metric broadcast
    """
    try:
        from app.microservices.flight_service.dashboard.realtime_service import RealTimeMetric
        import time
        import uuid
        
        metric = RealTimeMetric(
            metric_id=str(uuid.uuid4()),
            timestamp=time.time(),
            service=service,
            metric_type=metric_type,
            value=value,
            metadata=metadata or {}
        )
        
        await realtime_dashboard.broadcast_metric(metric)
        
        return {
            "status": "success",
            "message": f"Metric {metric_type} broadcasted to dashboard clients",
            "metric_id": metric.metric_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to broadcast metric: {str(e)}")


@router.post("/dashboard/broadcast-alert")
async def broadcast_custom_alert(
    severity: str,
    message: str,
    component: str,
    metadata: Optional[Dict[str, Any]] = None
):
    """
    Broadcast a custom alert to dashboard clients.
    
    Args:
        severity: Alert severity (info, warning, critical)
        message: Alert message
        component: Component that generated the alert
        metadata: Additional metadata
        
    Returns:
        Confirmation of alert broadcast
    """
    try:
        alert = {
            "severity": severity,
            "message": message,
            "component": component,
            "metadata": metadata or {}
        }
        
        await realtime_dashboard.broadcast_alert(alert)
        
        return {
            "status": "success",
            "message": "Alert broadcasted to dashboard clients",
            "alert": alert
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to broadcast alert: {str(e)}")


@router.post("/reporting/generate-report")
async def generate_report(request: ReportGenerationRequest):
    """
    Generate an automated report on demand.
    
    Args:
        request: Report generation request
        
    Returns:
        Generated report data
    """
    try:
        # Validate report type
        try:
            report_type = ReportType(request.report_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid report type: {request.report_type}")
        
        # Validate format
        try:
            report_format = ReportFormat(request.format)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid report format: {request.format}")
        
        # Generate report
        report_data = await automated_reporting.generate_report(report_type, report_format)
        
        return {
            "status": "success",
            "report": report_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate report: {str(e)}")


@router.post("/reporting/schedule-report")
async def schedule_report(request: ScheduleReportRequest):
    """
    Schedule an automated report.
    
    Args:
        request: Report scheduling request
        
    Returns:
        Confirmation of report scheduling
    """
    try:
        # Validate report type
        try:
            report_type = ReportType(request.report_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid report type: {request.report_type}")
        
        # Validate format
        try:
            report_format = ReportFormat(request.format)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid report format: {request.format}")
        
        # Create schedule
        schedule = ReportSchedule(
            report_type=report_type,
            schedule_cron=request.schedule_cron,
            recipients=request.recipients,
            format=report_format,
            enabled=request.enabled
        )
        
        # Add schedule
        automated_reporting.add_schedule(request.schedule_id, schedule)
        
        return {
            "status": "success",
            "message": f"Report scheduled: {request.schedule_id}",
            "schedule": {
                "schedule_id": request.schedule_id,
                "report_type": request.report_type,
                "schedule_cron": request.schedule_cron,
                "recipients": request.recipients,
                "enabled": request.enabled
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to schedule report: {str(e)}")


@router.delete("/reporting/schedule/{schedule_id}")
async def remove_report_schedule(schedule_id: str):
    """
    Remove a scheduled report.
    
    Args:
        schedule_id: Schedule identifier
        
    Returns:
        Confirmation of schedule removal
    """
    try:
        automated_reporting.remove_schedule(schedule_id)
        
        return {
            "status": "success",
            "message": f"Report schedule removed: {schedule_id}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to remove schedule: {str(e)}")


@router.get("/reporting/schedules")
async def get_report_schedules():
    """
    Get all configured report schedules.
    
    Returns:
        List of report schedules
    """
    try:
        schedules = {}
        for schedule_id, schedule in automated_reporting.schedules.items():
            schedules[schedule_id] = {
                "report_type": schedule.report_type.value,
                "schedule_cron": schedule.schedule_cron,
                "recipients": schedule.recipients,
                "format": schedule.format.value,
                "enabled": schedule.enabled,
                "last_run": schedule.last_run,
                "next_run": schedule.next_run
            }
        
        return {
            "status": "success",
            "schedules": schedules
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve schedules: {str(e)}")


@router.get("/reporting/service-stats")
async def get_reporting_service_stats():
    """
    Get automated reporting service statistics.
    
    Returns:
        Reporting service performance metrics
    """
    try:
        stats = automated_reporting.get_service_stats()
        return {
            "status": "success",
            "reporting_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve reporting stats: {str(e)}")


@router.post("/analytics/forecast")
async def generate_forecast(request: ForecastRequest):
    """
    Generate predictive forecast for a metric.
    
    Args:
        request: Forecast request
        
    Returns:
        Forecast results
    """
    try:
        forecast = await predictive_analytics.generate_forecast(
            request.metric_name,
            request.hours_ahead
        )
        
        if forecast:
            return {
                "status": "success",
                "forecast": {
                    "metric": forecast.metric,
                    "current_value": forecast.current_value,
                    "predicted_values": forecast.predicted_values,
                    "prediction_intervals": forecast.prediction_intervals,
                    "confidence": forecast.confidence,
                    "forecast_horizon": forecast.forecast_horizon,
                    "model_accuracy": forecast.model_accuracy
                }
            }
        else:
            return {
                "status": "not_available",
                "message": f"No forecast available for metric: {request.metric_name}"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate forecast: {str(e)}")


@router.get("/analytics/capacity-recommendations")
async def get_capacity_recommendations():
    """
    Get capacity planning recommendations.
    
    Returns:
        Capacity planning recommendations
    """
    try:
        recommendations = await predictive_analytics.generate_capacity_recommendations()
        
        formatted_recommendations = []
        for rec in recommendations:
            formatted_recommendations.append({
                "component": rec.component,
                "current_capacity": rec.current_capacity,
                "predicted_demand": rec.predicted_demand,
                "recommended_action": rec.recommended_action,
                "urgency": rec.urgency,
                "estimated_impact": rec.estimated_impact,
                "implementation_timeline": rec.implementation_timeline
            })
        
        return {
            "status": "success",
            "recommendations": formatted_recommendations
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get capacity recommendations: {str(e)}")


@router.get("/analytics/predictive-insights")
async def get_predictive_insights():
    """
    Get comprehensive predictive analytics insights.
    
    Returns:
        Predictive analytics insights and forecasts
    """
    try:
        insights = await predictive_analytics.get_predictive_insights()
        
        return {
            "status": "success",
            "insights": insights
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get predictive insights: {str(e)}")


@router.get("/analytics/anomalies")
async def get_anomaly_detection():
    """
    Get recent anomaly detection results.
    
    Returns:
        Detected anomalies and their details
    """
    try:
        anomalies = await predictive_analytics._detect_anomalies()
        
        return {
            "status": "success",
            "anomalies": anomalies,
            "anomaly_count": len(anomalies)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to detect anomalies: {str(e)}")


@router.get("/analytics/model-performance")
async def get_model_performance():
    """
    Get machine learning model performance metrics.
    
    Returns:
        Model performance statistics
    """
    try:
        model_performance = {}
        
        for metric_name, model in predictive_analytics.models.items():
            model_performance[metric_name] = {
                "model_type": model.model_type,
                "accuracy": model.accuracy,
                "last_trained": model.last_trained,
                "training_data_size": model.training_data_size,
                "features": model.features,
                "target": model.target
            }
        
        return {
            "status": "success",
            "model_performance": model_performance,
            "total_models": len(model_performance)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get model performance: {str(e)}")


@router.get("/analytics/predictive-stats")
async def get_predictive_analytics_stats():
    """
    Get predictive analytics service statistics.
    
    Returns:
        Predictive analytics service performance metrics
    """
    try:
        stats = predictive_analytics.get_service_stats()
        return {
            "status": "success",
            "predictive_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve predictive stats: {str(e)}")


@router.get("/monitoring/comprehensive-status")
async def get_comprehensive_monitoring_status():
    """
    Get comprehensive monitoring status across all Phase 4 components.
    
    Returns:
        Complete monitoring system status
    """
    try:
        # Get stats from all services
        dashboard_stats = realtime_dashboard.get_service_stats()
        reporting_stats = automated_reporting.get_service_stats()
        predictive_stats = predictive_analytics.get_service_stats()
        
        # Calculate overall health
        services_healthy = 0
        total_services = 3
        
        if dashboard_stats.get("active_connections", 0) >= 0:  # Dashboard is responsive
            services_healthy += 1
        
        if reporting_stats.get("scheduler_running", False):  # Reporting scheduler is running
            services_healthy += 1
        
        if predictive_stats.get("models_available", 0) > 0:  # Predictive models are available
            services_healthy += 1
        
        overall_health = services_healthy / total_services
        
        return {
            "status": "success",
            "monitoring_status": {
                "overall_health": overall_health,
                "services_healthy": services_healthy,
                "total_services": total_services,
                "dashboard_service": {
                    "status": "healthy" if dashboard_stats.get("active_connections", 0) >= 0 else "unhealthy",
                    "stats": dashboard_stats
                },
                "reporting_service": {
                    "status": "healthy" if reporting_stats.get("scheduler_running", False) else "unhealthy",
                    "stats": reporting_stats
                },
                "predictive_service": {
                    "status": "healthy" if predictive_stats.get("models_available", 0) > 0 else "unhealthy",
                    "stats": predictive_stats
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get monitoring status: {str(e)}")


@router.post("/monitoring/shutdown-services")
async def shutdown_monitoring_services():
    """
    Gracefully shutdown all Phase 4 monitoring services.
    
    Returns:
        Shutdown confirmation
    """
    try:
        # Shutdown services
        await realtime_dashboard.shutdown()
        await automated_reporting.shutdown()
        await predictive_analytics.shutdown()
        
        return {
            "status": "success",
            "message": "All Phase 4 monitoring services shutdown successfully"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to shutdown services: {str(e)}")
