from fastapi import APIRouter, HTTPException, BackgroundTasks
from app.microservices.flight_service.search_service.routes import router as flight_search_router
from app.microservices.flight_service.detail_service.routes import router as flight_detail_router
from app.microservices.flight_service.details_service.routes import router as flight_details_router
from app.microservices.flight_service.ssr_service.routes import router as flight_ssr_router
from app.microservices.flight_service.fare_rule_service.routes import router as flight_fare_rule_router
from app.microservices.flight_service.flight_info_service.routes import router as flight_info_router
from app.microservices.flight_service.shared_service.routes import router as shared_flight_router
from app.microservices.flight_service.cache_management.routes import router as cache_management_router
from app.microservices.flight_service.async_routes import router as async_routes_router
from app.microservices.flight_service.database_routes import router as database_routes_router
from app.microservices.flight_service.advanced_routes import router as advanced_routes_router
from app.microservices.flight_service.search_service.optimized_routes import router as optimized_search_router

# Create an instance of APIRouter
router = APIRouter()

# Include various flight-related routers with an empty prefix and a common tag
router.include_router(flight_search_router, prefix="", tags=["Flight"])
router.include_router(flight_detail_router, prefix="", tags=["Flight"])
router.include_router(flight_details_router, prefix="", tags=["Flight"])
router.include_router(flight_ssr_router, prefix="", tags=["Flight"])
router.include_router(flight_fare_rule_router, prefix="", tags=["Flight"])
router.include_router(flight_info_router, prefix="", tags=["Flight"])
router.include_router(shared_flight_router, prefix="", tags=["Flight"])
router.include_router(cache_management_router, prefix="/admin", tags=["Cache Management"])
router.include_router(async_routes_router, prefix="", tags=["Async Flight Services"])
router.include_router(database_routes_router, prefix="/admin", tags=["Database & Analytics"])
router.include_router(advanced_routes_router, prefix="/advanced", tags=["Advanced Monitoring & Analytics"])
router.include_router(optimized_search_router, prefix="/optimized", tags=["Optimized Flight Search"])