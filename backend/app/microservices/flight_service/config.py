from app.config import env
from databases import Database
from sqlalchemy import create_engine, MetaData


DATABASE_URL = env.get("FLIGHT_SERVICE_DATABASE_URL")
database = Database(DATABASE_URL)
metadata = MetaData()
engine = create_engine(DATABASE_URL, echo=True)

PROVIDER_CONFIG = {
    "tripjack": {
        "base_url": "https://apitest.tripjack.com/",
        "api_key": "6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9",
    },
    "provider2": {

    },
}

# Enhanced cache configuration with intelligent TTL management optimized for 3-second response target
cache_timer = {
	# Search results - optimized TTL for sub-3-second performance
	"FLIGHT_SEARCH": int(env.get("FLIGHT_SEARCH_CACHE_TIMER") or 1800),  # 30 minutes (increased for better hit rate)
	"FLIGHT_SEARCH_POPULAR": int(env.get("FLIGHT_SEARCH_POPULAR_CACHE_TIMER") or 3600),  # 1 hour for popular routes
	"FLIGHT_SEARCH_HOT": int(env.get("FLIGHT_SEARCH_HOT_CACHE_TIMER") or 7200),  # 2 hours for very popular routes

	# Detail results - optimized for performance while maintaining data freshness
	"FLIGHT_DETAIL": int(env.get("FLIGHT_DETAIL_CACHE_TIMER") or 600),  # 10 minutes (increased)
	"FLIGHT_DETAIL_PREMIUM": int(env.get("FLIGHT_DETAIL_PREMIUM_CACHE_TIMER") or 300),  # 5 minutes for premium fares
	"FLIGHT_PRICING": int(env.get("FLIGHT_PRICING_CACHE_TIMER") or 180),  # 3 minutes for pricing data

	# Booking related caches
	"BOOKING_DETAILS": int(env.get("BOOKING_DETAILS_CACHE_TIMER") or 3600),  # 1 hour
	"USER_BOOKINGS": int(env.get("USER_BOOKINGS_CACHE_TIMER") or 1800),  # 30 minutes

	# Static data with longer TTL
	"AIRPORT_DATA": int(env.get("AIRPORT_DATA_CACHE_TIMER") or 86400),  # 24 hours
	"AIRLINE_DATA": int(env.get("AIRLINE_DATA_CACHE_TIMER") or 43200),  # 12 hours
	"ROUTE_METADATA": int(env.get("ROUTE_METADATA_CACHE_TIMER") or 21600),  # 6 hours

	# Memory cache TTL (optimized for hot data)
	"MEMORY_CACHE_TTL": int(env.get("MEMORY_CACHE_TTL") or 60),  # 1 minute (increased for better hit rate)
	"MEMORY_CACHE_HOT_TTL": int(env.get("MEMORY_CACHE_HOT_TTL") or 120),  # 2 minutes for hot data
}

# Cache warming configuration
cache_warming_config = {
	"enabled": env.get("CACHE_WARMING_ENABLED", "true").lower() == "true",
	"popular_routes_refresh_interval": int(env.get("POPULAR_ROUTES_REFRESH_INTERVAL") or 3600),  # 1 hour
	"max_routes_to_warm": int(env.get("MAX_ROUTES_TO_WARM") or 50),
	"warming_schedule": env.get("CACHE_WARMING_SCHEDULE", "0 */2 * * *"),  # Every 2 hours
}

# Performance monitoring configuration optimized for 3-second target
performance_config = {
	"enable_metrics": env.get("ENABLE_PERFORMANCE_METRICS", "true").lower() == "true",
	"metrics_retention_days": int(env.get("METRICS_RETENTION_DAYS") or 7),
	"slow_query_threshold_ms": int(env.get("SLOW_QUERY_THRESHOLD_MS") or 500),  # Reduced for stricter monitoring
	"cache_hit_rate_alert_threshold": float(env.get("CACHE_HIT_RATE_ALERT_THRESHOLD") or 0.85),  # Increased target
	"response_time_target_ms": int(env.get("RESPONSE_TIME_TARGET_MS") or 3000),  # 3-second target
	"response_time_warning_ms": int(env.get("RESPONSE_TIME_WARNING_MS") or 2000),  # 2-second warning
	"memory_cache_max_size": int(env.get("MEMORY_CACHE_MAX_SIZE") or 10000),  # Increased cache size
	"concurrent_request_limit": int(env.get("CONCURRENT_REQUEST_LIMIT") or 100),  # Concurrent processing limit
}

# Request deduplication configuration
deduplication_config = {
	"enabled": env.get("REQUEST_DEDUPLICATION_ENABLED", "true").lower() == "true",
	"window_seconds": int(env.get("DEDUPLICATION_WINDOW_SECONDS") or 30),  # 30-second deduplication window
	"max_pending_requests": int(env.get("MAX_PENDING_REQUESTS") or 1000),
	"cleanup_interval_seconds": int(env.get("DEDUPLICATION_CLEANUP_INTERVAL") or 60),
}

# Async processing configuration
async_config = {
	"enabled": env.get("ASYNC_PROCESSING_ENABLED", "true").lower() == "true",
	"max_concurrent_requests": int(env.get("MAX_CONCURRENT_REQUESTS") or 50),
	"request_timeout_seconds": int(env.get("REQUEST_TIMEOUT_SECONDS") or 25),  # 25 seconds to stay under 30s limit
	"retry_attempts": int(env.get("RETRY_ATTEMPTS") or 2),
	"retry_delay_seconds": float(env.get("RETRY_DELAY_SECONDS") or 0.5),
}
