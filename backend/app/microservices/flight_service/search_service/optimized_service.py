"""
Optimized flight search service designed to achieve sub-3-second response times.
Implements intelligent caching, async processing, request deduplication, and performance monitoring.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor

from app.config import RedisCache
from app.microservices.flight_service.cache_service import flight_cache_service, generate_search_cache_key
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
from app.microservices.flight_service.utils.async_provider_utils import make_async_provider_call
from app.microservices.flight_service.utils.request_deduplication_optimized import request_deduplicator
from app.microservices.flight_service.analytics.performance_service import performance_analytics
from app.microservices.flight_service import config


class OptimizedFlightSearch:
    """
    High-performance flight search service optimized for sub-3-second response times.
    """

    def __init__(self):
        self.cache_service = flight_cache_service
        self.redis_client = RedisCache.connection()
        self.deduplicator = request_deduplicator
        self.thread_pool = ThreadPoolExecutor(max_workers=config.async_config["max_concurrent_requests"])

        # Performance tracking
        self.performance_stats = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "async_requests": 0,
            "deduplication_hits": 0,
            "average_response_time": 0.0,
            "sub_3s_responses": 0
        }

    async def search_async(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async flight search optimized for performance.

        Args:
            request_data: Flight search request parameters

        Returns:
            Flight search results with performance metadata
        """
        start_time = time.time()
        self.performance_stats["total_requests"] += 1

        try:
            # Step 1: Generate cache key and request hash for deduplication
            cache_key = generate_search_cache_key(request_data)
            request_hash = self.deduplicator.generate_request_hash(request_data, "flight_search")

            # Step 2: Check for duplicate requests
            if config.deduplication_config["enabled"]:
                duplicate_result = await self._check_duplicate_request(request_hash, cache_key)
                if duplicate_result:
                    self.performance_stats["deduplication_hits"] += 1
                    return self._add_performance_metadata(duplicate_result, start_time, "deduplication")

            # Step 3: Check multi-layer cache
            cached_result = await self._check_cache_layers(cache_key)
            if cached_result:
                self.performance_stats["cache_hits"] += 1
                # Trigger background refresh if data is getting stale
                await self._trigger_background_refresh_if_needed(request_data, cached_result)
                return self._add_performance_metadata(cached_result, start_time, "cache")

            # Step 4: Cache miss - fetch from provider with async optimization
            self.performance_stats["cache_misses"] += 1
            return await self._fetch_with_async_optimization(request_data, cache_key, start_time)

        except Exception as e:
            await performance_analytics.record_metric(
                "search_error_rate", 1.0, "optimized_search", {"error": str(e)}
            )
            return self._create_error_response(request_data, str(e), start_time)

    async def _check_duplicate_request(self, request_hash: str, cache_key: str) -> Optional[Dict[str, Any]]:
        """Check if this is a duplicate request and return cached result if available."""
        try:
            # Check if request is already being processed
            pending_result = await self.deduplicator.get_pending_result(request_hash)
            if pending_result:
                return pending_result

            # Check if we have a recent result for this exact request
            recent_result = self.redis_client.get_cache(f"recent:{request_hash}")
            if recent_result:
                return recent_result

        except Exception as e:
            print(f"Deduplication check error: {str(e)}")

        return None

    async def _check_cache_layers(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Check all cache layers for existing results."""
        try:
            # L1: Memory cache (fastest)
            memory_result = await self.cache_service.get_from_memory_cache(cache_key)
            if memory_result:
                return memory_result

            # L2: Redis cache
            redis_result = self.redis_client.get_cache(cache_key)
            if redis_result:
                # Populate L1 cache for next time
                await self.cache_service.set_memory_cache(cache_key, redis_result)
                return redis_result

        except Exception as e:
            print(f"Cache layer check error: {str(e)}")

        return None

    async def _fetch_with_async_optimization(
        self,
        request_data: Dict[str, Any],
        cache_key: str,
        start_time: float
    ) -> Dict[str, Any]:
        """Fetch flight data with async optimization and intelligent fallback."""
        try:
            # Register this request as pending to avoid duplicates
            request_hash = self.deduplicator.generate_request_hash(request_data, "flight_search")
            await self.deduplicator.register_pending_request(request_hash, cache_key)

            # Translate request for TripJack API
            provider_payload = flight_search_translate_client_to_provider(request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"

            # Make async API call with timeout protection
            provider_response = await asyncio.wait_for(
                make_async_provider_call(provider_api_url, provider_payload, "tripjack"),
                timeout=config.async_config["request_timeout_seconds"]
            )

            # Translate response to client format
            translator = SearchProviderResponseTranslator(provider_response, request_data)
            client_response = translator.translate()

            # Cache the result with intelligent TTL
            await self._cache_result_with_intelligent_ttl(cache_key, client_response, request_data)

            # Mark request as completed
            await self.deduplicator.complete_request(request_hash, client_response)

            self.performance_stats["async_requests"] += 1
            return self._add_performance_metadata(client_response, start_time, "provider_async")

        except asyncio.TimeoutError:
            # Timeout - return immediate response and process in background
            return await self._handle_timeout_with_background_processing(request_data, cache_key, start_time)

        except Exception as e:
            print(f"Async fetch error: {str(e)}")
            # Try fallback to sync processing if async fails
            return await self._fallback_to_sync_processing(request_data, cache_key, start_time)

    async def _cache_result_with_intelligent_ttl(
        self,
        cache_key: str,
        result: Dict[str, Any],
        request_data: Dict[str, Any]
    ):
        """Cache result with intelligent TTL based on route popularity and data freshness."""
        try:
            # Determine cache TTL based on route popularity
            route_popularity = await self._get_route_popularity(request_data)

            if route_popularity >= 0.8:  # Very popular route
                ttl = config.cache_timer["FLIGHT_SEARCH_HOT"]
                cache_level = "hot"
            elif route_popularity >= 0.5:  # Popular route
                ttl = config.cache_timer["FLIGHT_SEARCH_POPULAR"]
                cache_level = "popular"
            else:  # Regular route
                ttl = config.cache_timer["FLIGHT_SEARCH"]
                cache_level = "regular"

            # Cache in Redis with calculated TTL
            self.redis_client.set_cache(cache_key, result, ttl)

            # Cache in memory for hot data
            if cache_level in ["hot", "popular"]:
                memory_ttl = config.cache_timer["MEMORY_CACHE_HOT_TTL"] if cache_level == "hot" else config.cache_timer["MEMORY_CACHE_TTL"]
                await self.cache_service.set_memory_cache(cache_key, result, memory_ttl)

            # Store recent result for deduplication
            request_hash = self.deduplicator.generate_request_hash(request_data, "flight_search")
            self.redis_client.set_cache(f"recent:{request_hash}", result, 300)  # 5 minutes for deduplication

        except Exception as e:
            print(f"Intelligent caching error: {str(e)}")

    async def _get_route_popularity(self, request_data: Dict[str, Any]) -> float:
        """Calculate route popularity score (0.0 to 1.0)."""
        try:
            # Extract route information
            trips = request_data.get("Trips", [])
            if not trips:
                return 0.0

            route_key = f"{trips[0].get('From', '')}-{trips[0].get('To', '')}"

            # Get popularity data from cache/analytics
            popularity_data = self.redis_client.get_cache(f"route_popularity:{route_key}")
            if popularity_data:
                return min(popularity_data.get("score", 0.0), 1.0)

            # Default popularity for new routes
            return 0.3

        except Exception:
            return 0.3  # Default moderate popularity

    async def _trigger_background_refresh_if_needed(
        self,
        request_data: Dict[str, Any],
        cached_result: Dict[str, Any]
    ):
        """Trigger background refresh if cached data is getting stale."""
        try:
            # Check if data is in the last 25% of its TTL
            cache_timestamp = cached_result.get("cached_at", 0)
            current_time = time.time()
            age = current_time - cache_timestamp

            # Trigger refresh if data is older than 75% of TTL
            ttl_threshold = config.cache_timer["FLIGHT_SEARCH"] * 0.75

            if age > ttl_threshold:
                # Trigger background refresh (fire and forget)
                asyncio.create_task(self._background_refresh(request_data))

        except Exception as e:
            print(f"Background refresh trigger error: {str(e)}")

    async def _background_refresh(self, request_data: Dict[str, Any]):
        """Refresh cache data in background."""
        try:
            cache_key = generate_search_cache_key(request_data)
            await self._fetch_with_async_optimization(request_data, cache_key, time.time())
        except Exception as e:
            print(f"Background refresh error: {str(e)}")

    async def _handle_timeout_with_background_processing(
        self,
        request_data: Dict[str, Any],
        cache_key: str,
        start_time: float
    ) -> Dict[str, Any]:
        """Handle timeout by returning immediate response and processing in background."""
        # Trigger background processing
        asyncio.create_task(self._background_refresh(request_data))

        return {
            "TUI": cache_key,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": ["Search in progress. Results will be available shortly."],
            "Trips": [],
            "Code": "202",
            "Msg": ["Flight search initiated. Please check back in a few moments."],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "background_processing",
            "search_status": "processing"
        }

    async def _fallback_to_sync_processing(
        self,
        request_data: Dict[str, Any],
        cache_key: str,
        start_time: float
    ) -> Dict[str, Any]:
        """Fallback to synchronous processing if async fails."""
        try:
            # Use thread pool for sync processing to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.thread_pool,
                self._sync_provider_call,
                request_data
            )

            if result:
                # Cache the result
                await self._cache_result_with_intelligent_ttl(cache_key, result, request_data)
                return self._add_performance_metadata(result, start_time, "provider_sync_fallback")

        except Exception as e:
            print(f"Sync fallback error: {str(e)}")

        # Final fallback - background processing
        return await self._handle_timeout_with_background_processing(request_data, cache_key, start_time)

    def _sync_provider_call(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Synchronous provider call for fallback scenarios."""
        try:
            from app.microservices.flight_service.utils.provider_utils import make_provider_api_call

            provider_payload = flight_search_translate_client_to_provider(request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"

            provider_response = make_provider_api_call(provider_api_url, provider_payload)
            translator = SearchProviderResponseTranslator(provider_response, request_data)
            return translator.translate()

        except Exception as e:
            print(f"Sync provider call error: {str(e)}")
            return None

    def _add_performance_metadata(
        self,
        result: Dict[str, Any],
        start_time: float,
        data_source: str
    ) -> Dict[str, Any]:
        """Add performance metadata to response."""
        response_time = time.time() - start_time
        response_time_ms = round(response_time * 1000, 2)

        # Update performance stats
        self.performance_stats["average_response_time"] = (
            (self.performance_stats["average_response_time"] * (self.performance_stats["total_requests"] - 1) + response_time) /
            self.performance_stats["total_requests"]
        )

        if response_time_ms <= 3000:
            self.performance_stats["sub_3s_responses"] += 1

        # Add metadata to result
        result.update({
            "sh_price": False,
            "cache_hit": data_source in ["cache", "deduplication"],
            "response_time_ms": response_time_ms,
            "data_source": data_source,
            "performance_target_met": response_time_ms <= 3000,
            "cached_at": time.time()
        })

        return result

    def _create_error_response(
        self,
        request_data: Dict[str, Any],
        error_message: str,
        start_time: float
    ) -> Dict[str, Any]:
        """Create standardized error response with performance metadata."""
        return {
            "TUI": None,
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Error: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Internal server error occurred"],
            "sh_price": False,
            "cache_hit": False,
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "data_source": "error",
            "error_details": error_message,
            "performance_target_met": False
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        total_requests = max(self.performance_stats["total_requests"], 1)

        return {
            **self.performance_stats,
            "cache_hit_rate": self.performance_stats["cache_hits"] / total_requests,
            "sub_3s_success_rate": self.performance_stats["sub_3s_responses"] / total_requests,
            "deduplication_rate": self.performance_stats["deduplication_hits"] / total_requests,
            "async_processing_rate": self.performance_stats["async_requests"] / total_requests
        }


# Global optimized search service instance
optimized_flight_search = OptimizedFlightSearch()
