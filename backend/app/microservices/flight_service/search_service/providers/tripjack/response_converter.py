from datetime import datetime
from app.microservices.flight_service.utils.utils import format_fare_identifier, minutes_to_hours_minutes


# Function to calculate the total price of tickets based on fare data and passenger counts.
def calculate_total_price(fare_data, adult_count, child_count, infant_count):
    """
    Calculate the total fare based on fare data for adults, children, and infants.

    Args:
        fare_data (dict): Dictionary containing fare details for different passenger categories.
        adult_count (int): Number of adult passengers.
        child_count (int): Number of child passengers.
        infant_count (int): Number of infant passengers.

    Returns:
        float: Total fare calculated for all passengers.
    """

    # Internal function to retrieve fare sum for a specific category.
    def get_fare_sum(category):
        return float(fare_data[category]['fC']['TF'])  # Get total fare for the category

    # Calculate fares for each category, default to 0 if there are no passengers.
    adult_fare = (get_fare_sum('ADULT') * adult_count) if adult_count else 0
    child_fare = (get_fare_sum('CHILD') * child_count) if child_count else 0
    infant_fare = (get_fare_sum('INFANT') * infant_count) if infant_count else 0

    # Sum all fares to get the total fare.
    total_fare = adult_fare + child_fare + infant_fare
    return total_fare


class FareProcessor:
    """
    A class to process fare information for a flight based on passenger counts.

    Attributes:
        fare_data (dict): A dictionary containing fare details for different passenger categories (ADULT, CHILD, INFANT).
        adult_count (int): The number of adult passengers.
        child_count (int): The number of child passengers.
        infant_count (int): The number of infant passengers.
    """
    def __init__(self, fare_data, adult_count=0, child_count=0, infant_count=0):
        """
        Initializes the FareProcessor with fare data and passenger counts.

        Args:
            fare_data (dict): A dictionary with fare details.
            adult_count (int): The number of adult passengers (default is 0).
            child_count (int): The number of child passengers (default is 0).
            infant_count (int): The number of infant passengers (default is 0).
        """
        self.fare_data = fare_data
        self.adult_count = adult_count
        self.child_count = child_count
        self.infant_count = infant_count

    def get_fare_sum(self, category):
        """
        Retrieves the total fare for a specified passenger category.

        Args:
            category (str): The category for which to retrieve the fare (e.g., 'ADULT', 'CHILD', 'INFANT').

        Returns:
            float: The total fare for the specified category.
        """
        return float(self.fare_data[category]['fC']['TF'])  # Extract total fare from fare data.

    def calculate_total_price(self):
        """
        Calculates the total price of tickets based on passenger counts.

        Returns:
            float: The total fare calculated for all passengers.
        """
        # Calculate fare for adults, children, and infants
        adult_fare = (self.get_fare_sum('ADULT') * self.adult_count) if self.adult_count else 0
        child_fare = (self.get_fare_sum('CHILD') * self.child_count) if self.child_count else 0
        infant_fare = (self.get_fare_sum('INFANT') * self.infant_count) if self.infant_count else 0

        # Sum the fares to get the total fare
        total_fare = adult_fare + child_fare + infant_fare
        return total_fare


class SearchProviderResponseTranslator:
    def __init__(self, provider_response, request):
        # Enhanced logging for debugging TripJack response structure
        print("=" * 80)
        print("🔍 TRIPJACK SEARCH RESPONSE CONVERTER DEBUG")
        print("=" * 80)
        print(f"Request: {request}")
        print(f"Provider Response Type: {type(provider_response)}")

        # Check for None response
        if provider_response is None:
            print("❌ ERROR: Provider response is None!")
            print("=" * 80)
            raise ValueError("Provider response is None")

        # Check for required fields in search response
        if 'searchResult' in provider_response:
            search_result = provider_response['searchResult']
            print(f"✅ Found searchResult: {type(search_result)}")
            if 'tripInfos' in search_result:
                print(f"✅ Found tripInfos: {type(search_result['tripInfos'])}")
                print(f"TripInfos keys: {list(search_result['tripInfos'].keys()) if isinstance(search_result['tripInfos'], dict) else 'Not a dict'}")
            else:
                print("❌ ERROR: Missing 'tripInfos' in searchResult")
        else:
            print("❌ ERROR: Missing 'searchResult' in provider response")

        print("=" * 80)

        # Initialize with the provider's response and the request details
        self.provider_response = provider_response
        self.request = request

        # Get counts for adults, children, and infants from the request
        self.adult_count = request.get('ADT', 0)  # Adult count, defaults to 0 if not provided
        self.child_count = request.get('CHD', 0)  # Child count, defaults to 0 if not provided
        self.infant_count = request.get('INF', 0)  # Infant count, defaults to 0 if not provided

        # Initialize lists to hold onward and return journey information
        self.onward_list = []  # List for onward journeys
        self.return_list = []  # List for return journeys

        # Initialize a list to store trip information
        self.trips = []  # List to hold all trips

        # Client response structure to be returned after processing
        self.client_response = {
            "TUI": "",  # Placeholder for TUI information (could be airline or travel provider)
            "Completed": "True",  # Status of the response
            "CeilingInfo": "",  # Placeholder for additional info (TODO: needs implementation)
            "TripType": None,  # Type of trip (one-way, round-trip, etc.)
            "ElapsedTime": "",  # To store any elapsed time information if needed
            "Notices": None,  # Placeholder for notices or alerts to the client
            "Msg": [  # Message list to convey the status of the request
                "Success"
            ],
            "Code": 200,  # HTTP status code indicating success
            "Trips": self.trips  # List of trips to be included in the response
        }

    def connections(self, journey_info):
        # Initialize a list to store connection details
        connections = []

        # Create a copy of the journey_info to keep original data intact
        all_journey = journey_info.copy()

        # Remove the last journey segment from journey_info for processing
        journey_info.pop()

        # Initialize total duration in minutes
        total_duration_minutes = 0

        # Loop through each journey segment except the last one
        for idx, journey in enumerate(journey_info):
            # Get the arrival time of the current journey
            at_datetime = datetime.fromisoformat(journey.get('at'))

            # Get the departure time of the next journey
            dt_datetime = datetime.fromisoformat(all_journey[idx + 1].get('dt'))

            # Calculate the time difference between arrival and next departure
            time_difference = at_datetime - dt_datetime

            # Convert the time difference to hours and minutes
            hours, remainder = divmod(time_difference.seconds, 3600)
            minutes = remainder // 60

            # Format the time difference as "HHh MMm"
            formatted_time_difference = f"{hours:02}h {minutes:02}m"

            # Calculate total duration of the journey including connection time (cT)
            total_duration_minutes += journey.get('duration') + journey.get('cT', 0)

            # Append connection details to the connections list
            connections.append({
                "Airport": journey.get("aa", {}).get("code", ""),  # Get the airport code
                "ArrAirportName": f'{journey.get("aa", {}).get("city", "")}|{journey.get("aa", {}).get("city", "")}|{journey.get("aa", {}).get("countryCode", "")}|{journey.get("aa", {}).get("country", "")}',  # Format arrival airport name
                "Duration": formatted_time_difference,  # Store formatted duration
                "MAC": f'{journey.get("fD", {}).get("aI", {}).get("code", "")}|{journey.get("fD", {}).get("aI", {}).get("name", "")}',  # Format airline code and name
                "Type": "C"  # Type of connection (to be defined)
            })

        # Return the list of connections and total duration in minutes
        return connections, total_duration_minutes

    def journey(self, journey_infos, context={}):
        trips = []
        for idx, journey_info in enumerate(journey_infos):
            journey = journey_info.get("sI", [])[0]
            journey_end = journey_info.get("sI")[-1]
            stops = len(journey_info.get("sI", [])) - 1
            connection, total_duration_minutes = self.connections(journey_info.get("sI", []))
            total_duration_minutes += journey_end.get('duration')  # Total duration = sum of all durations + connections

            for priceList in journey_info.get("totalPriceList", []):
                # Get the unique fare ID for this specific price list
                fare_id = priceList.get('id')
                fare_details = priceList.get("fd", {})
                fare_processor = FareProcessor(fare_details, self.adult_count, self.child_count, self.infant_count)
                total_fare = fare_processor.calculate_total_price()

                # Handling terminal information for arrival and departure
                arrival_terminal = journey.get('aa', {}).get("terminal", "")
                if arrival_terminal != "":
                    arrival_terminal = f',{arrival_terminal}'
                    arrival_terminal = arrival_terminal.replace("Terminal", "T").replace(" ", "")

                departure_terminal = journey.get('da', {}).get("terminal", "")
                if departure_terminal != "":
                    departure_terminal = f',{departure_terminal}'
                    departure_terminal = departure_terminal.replace("Terminal", "T").replace(" ", "")

                journey_fare = {
                    "Stops": stops,  # Number of stops in the journey
                    "Seats": fare_details.get("ADULT", {}).get("sR"),  # Available seats for booking
                    "ReturnIdentifier": context.get('ReturnIdentifier'),  # Identifier for return journey
                    "Index": f"{fare_id}|{idx}|TJ",  # Unique index for journey tracking using specific fare ID
                    "Provider": journey.get("fD", {}).get("aI", {}).get("code", ""),  # Airline provider code
                    "FlightNo": journey.get("fD", {}).get("fN", ""),  # Flight number
                    "VAC": journey.get("fD", {}).get("aI", {}).get("code", ""),  # Vacation airline code
                    "MAC": journey.get("fD", {}).get("aI", {}).get("code", ""),  # Main airline code
                    "OAC": journey.get("fD", {}).get("aI", {}).get("code", ""),  # Other airline code for verification
                    "ArrivalTime": journey_end.get("at", ""),  # Expected arrival time
                    "DepartureTime": journey.get("dt", ""),  # Scheduled departure time
                    "FareClass": fare_details.get("ADULT", {}).get("cc", ""),  # Class of service for fare
                    "Duration": minutes_to_hours_minutes(total_duration_minutes),  # Total journey duration in hours and minutes
                    "TotalFare": fare_details.get("ADULT", {}).get("TF", 0),  # Total fare for the journey
                    "GrossFare": total_fare,  # Final gross fare calculated
                    "TotalCommission": sum(fare_details.get("ADULT", {}).get("afC", {}).get("TAF", {}).values()),  # Total commission calculated
                    "NetFare": total_fare,  # Net fare after deductions
                    "Hops": stops,  # Number of hops in the journey
                    "Promo": "",  # Promotional information (if any)
                    "PromoLink": "",  # Link for promotions
                    "PromoType": "AvailabilityPromotion",  # Type of promotion
                    "Refundable": "Y" if journey.get("isRs", False) else "N",  # Indicates if the fare is refundable
                    "Alliances": "",  # Placeholder for airline alliances
                    "Amenities": "",  # Placeholder for flight amenities
                    "Hold": False,  # Status for holding the booking
                    "Connections": connection,  # Connection details during the journey
                    "From": journey.get("da", {}).get("code", ""),  # Departure airport code
                    "To": journey_end.get("aa", {}).get("code", ""),  # Arrival airport code
                    "FromName": f"{journey.get('da', {}).get('city', '')}|{journey.get('da', {}).get('city', '')}{departure_terminal}|{journey.get('da', {}).get('countryCode', '')} |{journey.get('da', {}).get('country', '')}",  # Detailed departure airport name
                    "ToName": f"{journey_end.get('aa', {}).get('city', '')}|{journey_end.get('aa', {}).get('city', '')}{arrival_terminal}|{journey_end.get('aa', {}).get('countryCode', '')}|{journey_end.get('aa', {}).get('country', '')}",  # Detailed arrival airport name
                    "AirlineName": f'{journey.get("fD", {}).get("aI", {}).get("name", "")}|{journey.get("fD", {}).get("aI", {}).get("name", "")}|{journey.get("fD", {}).get("aI", {}).get("name", "")}',  # Name of the airline
                    "AirCraft": journey.get("fD", {}).get("eT", ""),  # Type of aircraft
                    "RBD": fare_details.get("ADULT", {}).get("cB", ""),  # Reservation booking designator
                    "Cabin": fare_details.get("ADULT", {}).get("cc", ""),  # Cabin class for the fare
                    "FareBasisCode": fare_details.get("ADULT", {}).get("fB", ""),  # Fare basis code for pricing
                    "FCType": format_fare_identifier(fareIdentifier=priceList.get('fareIdentifier'), fare_class=fare_details.get("ADULT", {}).get("cc", "")),  # Identifier for fare class type #TODO:
                    "Recommended": False,  # Placeholder for recommendation logic
                    "Premium": False,  # Placeholder for premium logic
                    "JourneyKey": f"{journey.get('da', {}).get('code', '')}-{journey.get('aa', {}).get('code', '')},{journey.get('fD', {}).get('fN', '')},{journey.get('fD', {}).get('aI', {}).get('code', '')},{journey.get('dt', '')},{fare_details.get('ADULT', {}).get('cc', '')}",  # Unique key for the journey
                    "FareKey": f"{fare_details.get('ADULT', {}).get('cc', '')},{fare_details.get('ADULT', {}).get('fB', '')},{fare_details.get('ADULT', {}).get('TF', 0)}",  # Unique key for fare details
                    "VACAirlineLogo": f"AirlineLogo{journey.get('fD', {}).get('aI', {}).get('code', '')}.jpg",  # File name for vacation airline logo
                    "MACAirlineLogo": f"AirlineLogo{journey.get('fD', {}).get('aI', {}).get('code', '')}.jpg",  # File name for main airline logo
                    "OACAirlineLogo": f"AirlineLogo{journey.get('fD', {}).get('aI', {}).get('code', '')}.jpg"  # File name for other airline logo
                }
                trips.append(journey_fare)  # Add the journey fare to the trips list

        return trips  # Return the list of trips with journey fare details

    def journeyCombo(self, journey_infos):
        # Loop through each journey information provided
        for idx, journey_info in enumerate(journey_infos):
            # Initialize dictionaries to hold onward and return journey information
            onward_dict = {"sI": [], "totalPriceList": journey_info.get('totalPriceList', [])}
            return_dict = {"sI": [], "totalPriceList": journey_info.get('totalPriceList', [])}

            # Classify journeys into onward and return based on the 'isRs' flag
            for journey in journey_info.get('sI', []):
                if journey.get('isRs') == True:  # If the journey is a return journey
                    return_dict["sI"].append(journey)  # Add to return dictionary
                else:  # If the journey is an onward journey
                    onward_dict["sI"].append(journey)  # Add to onward dictionary

            # Extend the onward and return lists with the processed journeys
            self.onward_list.extend(self.journey([onward_dict], context={"ReturnIdentifier": idx}))
            self.return_list.extend(self.journey([return_dict], context={"ReturnIdentifier": idx}))

    def translate(self):
        # Extract trip information from the provider's response
        trip_infos = self.provider_response.get("searchResult", {}).get("tripInfos", {})

        if trip_infos:  # Check if trip information is available
            if trip_infos.get('COMBO'):  # Check if there are combo trips
                self.journeyCombo(trip_infos.get('COMBO', []))  # Process combo journeys
                # Append onward and return journeys to trips list
                self.trips.append({"Journey": self.return_list})
                self.trips.append({"Journey": self.onward_list})
            elif trip_infos.get('RETURN') or trip_infos.get('ONWARD'):  # Check for return or onward journeys
                onward_list = self.journey(trip_infos.get('ONWARD', []))  # Process onward journeys
                self.trips.append({"Journey": onward_list})  # Add onward journeys to trips
                return_list = self.journey(trip_infos.get('RETURN', []))  # Process return journeys
                self.trips.append({"Journey": return_list})  # Add return journeys to trips
            elif trip_infos.get('ONWARD'):  # If only onward journeys exist
                onward = self.journey(trip_infos.get('ONWARD', []))  # Process onward journeys
                self.trips.append({"Journey": onward})  # Add onward journeys to trips
            else:
                # Multicity structure example (for future use)
                """
                "tripInfos": {
                                    "0": [],
                                    "1": [],
                                    "2": []
                                    }
                """
                pass  # Placeholder for future implementation
        else:
            # In case there are no search results from the supplier, return the response with httpStatus 200
            pass

        return self.client_response  # Return the client response with journey details