"""
This file defines background tasks, typically using Celery. It allows you to implement asynchronous processes related to hotel searching, such as caching results or sending notifications. This helps keep the application responsive to user requests.
"""
# tasks.py
from app.config import celery_app
from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
from app.microservices.flight_service import config
from app.microservices.flight_service.search_service.utils import generate_cache_key, generate_search_cache_key
from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
from app.config import RedisCache
from datetime import datetime, timedelta
import time
import json

@celery_app.task()
def fetch_flight_search_task(provider_name, request_data, cache_key): 
    # Translate the client request data into the provider's format
    provider_payload = flight_search_translate_client_to_provider(request_data)

    # Construct the API URL for the flight provider
    provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"

    # Make the API call to the flight provider and get the response
    provider_response = make_provider_api_call(provider_api_url, provider_payload)
    
    print(f"provider_response {provider_response}")

    # Initialize the translator for converting provider response to client response
    transilator = SearchProviderResponseTranslator(provider_response, request_data)

    # Translate the provider response into the client's expected format
    client_response = transilator.translate()

    # Add the cache key to the client response for reference
    client_response["TUI"] = cache_key

    # Store the client response in Redis cache with a TTL of fixed seconds
    RedisCache.connection().set_cache(cache_key, client_response, config.cache_timer.get('FLIGHT_SEARCH'))
    return True


# def demo_search_availability_cache(request_data):

#     def generate_demo_search_cache_json(request_data):
#         cache_json = {
#             "FareType": request_data["FareType"],
#             "From":request_data["Trips"][0]["From"],
#             "To":request_data["Trips"][0]["To"]
#         }
#         return generate_cache_key(cache_json)
    
#     def provider_call(request_data, cache_key):    
#         # Translate the client request data into the provider's format
#         provider_payload = flight_search_translate_client_to_provider(request_data)
#         # Construct the API URL for the flight provider
#         provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"
#         # Make the API call to the flight provider and get the response
#         provider_response = make_provider_api_call(provider_api_url, provider_payload)
#         # Initialize the translator for converting provider response to client response
#         transilator = SearchProviderResponseTranslator(provider_response, request_data)
#         # Translate the provider response into the client's expected format
#         client_response = transilator.translate()
#         # Add the cache key to the client response for reference
#         client_response["TUI"] = cache_key
#         RedisCache.connection().set_cache(cache_key, client_response, 7890048)
#         return client_response
    
#     def format_to_demo_cache(results,request_data):
#         final_onward_results = []
#         print(f"error results {results}")
#         # breakpoint()
#         if results["Trips"] != []:
#             for result in results["Trips"][0]["Journey"]:
#                 UpdateDepartureTime = result["ArrivalTime"].split("T")
#                 UpdateDepartureTime[0] = request_data["Trips"][0]["OnwardDate"]
#                 UpdateDepartureTime = f'{UpdateDepartureTime[0]}T{UpdateDepartureTime[1]}'
#                 result["DepartureTime"] = UpdateDepartureTime

#                 # Parse the departure time to a datetime object
#                 departure_time = datetime.strptime(UpdateDepartureTime, '%Y-%m-%dT%H:%M')

#                 # Extract hours and minutes from the duration string
#                 hours, minutes = map(int, result["Duration"].replace('h', '').replace('m', '').split())

#                 # Create a timedelta object for the duration
#                 duration = timedelta(hours=hours, minutes=minutes)

#                 # Calculate the arrival time
#                 arrival_time = departure_time + duration
#                 # Print the arrival time
#                 result["ArrivalTime"] = arrival_time.strftime('%Y-%m-%dT%H:%M')
#                 final_onward_results.append(result)

#         results["Trips"][0]["Journey"] = final_onward_results

#         cache_key = generate_search_cache_key(request_data)
#         results["TUI"] = cache_key
#         results["sh_price"] = False
#         return results
   
#     demo_cache_key = generate_demo_search_cache_json(request_data)
#     results = RedisCache.connection().get_cache(demo_cache_key)
#     if not results:
#         provider_call(request_data,demo_cache_key)
#         results = RedisCache.connection().get_cache(demo_cache_key)
#     return format_to_demo_cache(results,request_data)

def demo_search_availability_cache(request_data):
    """
    Handle the demo search availability cache logic with proper error handling
    and waiting for the provider response on first call.
    """
    
    def provider_call(request_data, cache_key):    
        """
        Make a call to the provider API and process the response.
        """
        # Translate the client request data into the provider's format
        provider_payload = flight_search_translate_client_to_provider(request_data)
        
        # Log the request payload
        print(f"Provider request payload: {provider_payload}")
        
        # Construct the API URL for the flight provider
        provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"
        
        # Make the API call to the flight provider and get the response
        provider_response = make_provider_api_call(provider_api_url, provider_payload)
        
        # Log the raw provider response
        print(f"Raw provider response: {provider_response}")
        
        # Initialize the translator for converting provider response to client response
        transilator = SearchProviderResponseTranslator(provider_response, request_data)
        
        # Translate the provider response into the client's expected format
        client_response = transilator.translate()
        
        # Log the translated response
        print(f"Translated client response: {client_response}")
        
        # Add the cache key to the client response for reference
        client_response["TUI"] = cache_key
        
        # Store the client response in Redis cache with a TTL
        RedisCache.connection().set_cache(cache_key, client_response, 7890048)
        
        return client_response
    
    def format_to_demo_cache(results, request_data):
        """
        Format the results from the cache or provider for display,
        handling empty results gracefully.
        """
        final_onward_results = []
        print(f"Processing results: {results}")
        
        # If Trips is empty or doesn't exist, initialize it with basic structure
        if not results.get("Trips") or len(results["Trips"]) == 0:
            results["Trips"] = [{"Journey": [], "From": request_data["Trips"][0]["From"], "To": request_data["Trips"][0]["To"]}]
        elif "Journey" not in results["Trips"][0]:
            results["Trips"][0]["Journey"] = []
        
        # Process Journey data if it exists
        if results["Trips"][0].get("Journey"):
            for result in results["Trips"][0]["Journey"]:
                UpdateDepartureTime = result["ArrivalTime"].split("T")
                UpdateDepartureTime[0] = request_data["Trips"][0]["OnwardDate"]
                UpdateDepartureTime = f'{UpdateDepartureTime[0]}T{UpdateDepartureTime[1]}'
                result["DepartureTime"] = UpdateDepartureTime

                # Parse the departure time to a datetime object
                departure_time = datetime.strptime(UpdateDepartureTime, '%Y-%m-%dT%H:%M')

                # Extract hours and minutes from the duration string
                hours, minutes = map(int, result["Duration"].replace('h', '').replace('m', '').split())

                # Create a timedelta object for the duration
                duration = timedelta(hours=hours, minutes=minutes)

                # Calculate the arrival time
                arrival_time = departure_time + duration
                # Set the arrival time
                result["ArrivalTime"] = arrival_time.strftime('%Y-%m-%dT%H:%M')
                final_onward_results.append(result)
            
            results["Trips"][0]["Journey"] = final_onward_results
        
        cache_key = generate_search_cache_key(request_data)
        results["TUI"] = cache_key
        results["sh_price"] = False
        
        # Add a message if no flights were found
        if not results["Trips"][0].get("Journey") or len(results["Trips"][0]["Journey"]) == 0:
            results["NoFlightsMessage"] = f"No flights found for the route {request_data['Trips'][0]['From']} to {request_data['Trips'][0]['To']}"
        
        return results
   
    # Use the standard search cache key generation to ensure consistency
    cache_key = generate_search_cache_key(request_data)
    results = RedisCache.connection().get_cache(cache_key)
    
    if not results:
        try:
            # For first-time searches, call the provider directly and wait for the response
            print("Cache miss. Making direct provider call...")
            results = provider_call(request_data, cache_key)
            print("Direct provider call successful")
        except Exception as e:
            print(f"Direct provider call failed: {str(e)}")
            # Return a default structure with error message
            return {
                "Trips": [{"Journey": [], "From": request_data["Trips"][0]["From"], "To": request_data["Trips"][0]["To"]}],
                "NoFlightsMessage": f"Error retrieving flights for the route {request_data['Trips'][0]['From']} to {request_data['Trips'][0]['To']}",
                "TUI": cache_key,
                "sh_price": False,
                "ErrorMessage": str(e)
            }
    else:
        print("Cache hit. Using cached results.")
    
    return format_to_demo_cache(results, request_data)