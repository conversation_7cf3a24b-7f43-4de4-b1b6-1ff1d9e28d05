"""
Enhanced Flight Search Service with improved ranking algorithms and multi-provider support.
Optimized for accuracy and performance.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

# Handle optional dependencies gracefully
try:
    from app.microservices.flight_service.cache_service import flight_cache_service
    CACHE_SERVICE_AVAILABLE = True
except ImportError:
    CACHE_SERVICE_AVAILABLE = False
    flight_cache_service = None

try:
    from app.microservices.flight_service.utils.provider_utils import make_provider_api_call
    from app.microservices.flight_service.search_service.providers.tripjack.request_creator import flight_search_translate_client_to_provider
    from app.microservices.flight_service.search_service.providers.tripjack.response_converter import SearchProviderResponseTranslator
    from app.microservices.flight_service import config
    PROVIDER_UTILS_AVAILABLE = True
except ImportError:
    PROVIDER_UTILS_AVAILABLE = False
    make_provider_api_call = None
    flight_search_translate_client_to_provider = None
    SearchProviderResponseTranslator = None
    config = None


@dataclass
class FlightScore:
    """Flight scoring components for ranking algorithm."""
    price_score: float = 0.0
    duration_score: float = 0.0
    schedule_score: float = 0.0
    airline_score: float = 0.0
    stops_score: float = 0.0
    total_score: float = 0.0


class EnhancedFlightSearchService:
    """
    Enhanced flight search service with intelligent ranking and multi-provider support.
    """

    def __init__(self):
        self.cache_service = flight_cache_service
        self.search_stats = {
            "total_searches": 0,
            "cache_hits": 0,
            "provider_calls": 0,
            "avg_response_time": 0.0,
            "accuracy_score": 0.0
        }

        # Scoring weights for flight ranking
        self.scoring_weights = {
            "price": 0.35,      # 35% - Price competitiveness
            "duration": 0.25,   # 25% - Flight duration
            "schedule": 0.20,   # 20% - Departure/arrival times
            "airline": 0.15,    # 15% - Airline reputation
            "stops": 0.05       # 5% - Number of stops
        }

    async def enhanced_search(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced flight search with intelligent ranking and caching.

        Args:
            request_data: Flight search request parameters

        Returns:
            Enhanced search results with relevance scores
        """
        start_time = time.time()
        self.search_stats["total_searches"] += 1

        try:
            # Generate cache key
            cache_key = self._generate_enhanced_cache_key(request_data)

            # Check cache first
            cached_results = await self._get_cached_results(cache_key)
            if cached_results:
                self.search_stats["cache_hits"] += 1
                return self._add_performance_metadata(cached_results, start_time, "cache")

            # Fetch from provider
            raw_results = await self._fetch_from_provider(request_data)

            # Enhance and rank results
            enhanced_results = await self._enhance_and_rank_results(raw_results, request_data)

            # Cache enhanced results
            await self._cache_enhanced_results(cache_key, enhanced_results)

            # Update statistics
            response_time = time.time() - start_time
            self._update_performance_stats(response_time)

            return self._add_performance_metadata(enhanced_results, start_time, "provider")

        except Exception as e:
            print(f"Enhanced search error: {str(e)}")
            return self._create_error_response(request_data, str(e))

    async def _fetch_from_provider(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch flight data from provider with error handling."""
        self.search_stats["provider_calls"] += 1

        try:
            # Translate request for TripJack API
            provider_payload = flight_search_translate_client_to_provider(request_data)
            provider_api_url = f"{config.PROVIDER_CONFIG.get('tripjack').get('base_url')}fms/v1/air-search-all"

            # Make provider API call
            provider_response = make_provider_api_call(provider_api_url, provider_payload)

            # Translate response to client format
            translator = SearchProviderResponseTranslator(provider_response, request_data)
            return translator.translate()

        except Exception as e:
            print(f"Provider fetch error: {str(e)}")
            raise

    async def _enhance_and_rank_results(self, raw_results: Dict[str, Any], request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance search results with intelligent ranking and additional metadata.
        """
        if not raw_results.get("Trips"):
            return raw_results

        enhanced_trips = []

        for trip in raw_results["Trips"]:
            if not trip.get("Options"):
                continue

            enhanced_options = []

            for option in trip["Options"]:
                # Calculate flight score
                flight_score = self._calculate_flight_score(option, request_data)

                # Add enhanced metadata
                enhanced_option = {
                    **option,
                    "relevance_score": flight_score.total_score,
                    "score_breakdown": {
                        "price_score": flight_score.price_score,
                        "duration_score": flight_score.duration_score,
                        "schedule_score": flight_score.schedule_score,
                        "airline_score": flight_score.airline_score,
                        "stops_score": flight_score.stops_score
                    },
                    "enhanced_metadata": self._generate_enhanced_metadata(option),
                    "recommendations": self._generate_recommendations(option, flight_score)
                }

                enhanced_options.append(enhanced_option)

            # Sort options by relevance score
            enhanced_options.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            enhanced_trip = {
                **trip,
                "Options": enhanced_options,
                "best_option_score": enhanced_options[0].get("relevance_score", 0) if enhanced_options else 0
            }

            enhanced_trips.append(enhanced_trip)

        # Sort trips by best option score
        enhanced_trips.sort(key=lambda x: x.get("best_option_score", 0), reverse=True)

        return {
            **raw_results,
            "Trips": enhanced_trips,
            "enhanced": True,
            "ranking_applied": True,
            "total_options": sum(len(trip.get("Options", [])) for trip in enhanced_trips)
        }

    def _calculate_flight_score(self, option: Dict[str, Any], request_data: Dict[str, Any]) -> FlightScore:
        """
        Calculate comprehensive flight score based on multiple factors.
        """
        score = FlightScore()

        # Extract flight details
        flights = option.get("Flights", [])
        if not flights:
            return score

        # 1. Price Score (35% weight)
        score.price_score = self._calculate_price_score(option)

        # 2. Duration Score (25% weight)
        score.duration_score = self._calculate_duration_score(flights)

        # 3. Schedule Score (20% weight)
        score.schedule_score = self._calculate_schedule_score(flights, request_data)

        # 4. Airline Score (15% weight)
        score.airline_score = self._calculate_airline_score(flights)

        # 5. Stops Score (5% weight)
        score.stops_score = self._calculate_stops_score(option)

        # Calculate weighted total score
        score.total_score = (
            score.price_score * self.scoring_weights["price"] +
            score.duration_score * self.scoring_weights["duration"] +
            score.schedule_score * self.scoring_weights["schedule"] +
            score.airline_score * self.scoring_weights["airline"] +
            score.stops_score * self.scoring_weights["stops"]
        )

        return score

    def _calculate_price_score(self, option: Dict[str, Any]) -> float:
        """Calculate price competitiveness score (0-100)."""
        try:
            total_fare = float(option.get("TotalFare", 0))
            if total_fare <= 0:
                return 0.0

            # Price scoring based on typical fare ranges
            # This could be enhanced with historical price data
            if total_fare < 5000:
                return 100.0  # Excellent price
            elif total_fare < 10000:
                return 85.0   # Good price
            elif total_fare < 15000:
                return 70.0   # Average price
            elif total_fare < 25000:
                return 50.0   # Above average
            else:
                return 25.0   # Expensive

        except (ValueError, TypeError):
            return 50.0  # Default score if price parsing fails

    def _calculate_duration_score(self, flights: List[Dict[str, Any]]) -> float:
        """Calculate flight duration efficiency score (0-100)."""
        try:
            total_duration_minutes = 0

            for flight in flights:
                duration_str = flight.get("Duration", "0h 0m")
                minutes = self._parse_duration_to_minutes(duration_str)
                total_duration_minutes += minutes

            if total_duration_minutes <= 0:
                return 0.0

            # Duration scoring (shorter is better)
            if total_duration_minutes < 120:    # < 2 hours
                return 100.0
            elif total_duration_minutes < 180:  # < 3 hours
                return 90.0
            elif total_duration_minutes < 300:  # < 5 hours
                return 75.0
            elif total_duration_minutes < 480:  # < 8 hours
                return 60.0
            elif total_duration_minutes < 720:  # < 12 hours
                return 40.0
            else:
                return 20.0

        except Exception:
            return 50.0

    def _calculate_schedule_score(self, flights: List[Dict[str, Any]], request_data: Dict[str, Any]) -> float:
        """Calculate schedule convenience score (0-100)."""
        try:
            if not flights:
                return 0.0

            first_flight = flights[0]
            departure_time = first_flight.get("DepartureTime", "")

            if not departure_time:
                return 50.0

            # Parse departure time
            dep_hour = int(departure_time.split("T")[1].split(":")[0]) if "T" in departure_time else 12

            # Score based on departure time preferences
            if 6 <= dep_hour <= 9:      # Early morning
                return 95.0
            elif 10 <= dep_hour <= 12:  # Late morning
                return 90.0
            elif 13 <= dep_hour <= 16:  # Afternoon
                return 85.0
            elif 17 <= dep_hour <= 20:  # Evening
                return 80.0
            elif 21 <= dep_hour <= 23:  # Night
                return 60.0
            else:                       # Late night/early morning
                return 40.0

        except Exception:
            return 50.0

    def _calculate_airline_score(self, flights: List[Dict[str, Any]]) -> float:
        """Calculate airline reputation score (0-100)."""
        try:
            if not flights:
                return 0.0

            # Airline reputation mapping (this could be enhanced with real data)
            airline_scores = {
                "Air India": 75,
                "IndiGo": 85,
                "SpiceJet": 70,
                "Vistara": 90,
                "GoAir": 65,
                "AirAsia": 70,
                "Emirates": 95,
                "Qatar Airways": 95,
                "Singapore Airlines": 95,
                "Lufthansa": 90
            }

            total_score = 0
            for flight in flights:
                airline = flight.get("Airline", "").split("|")[0] if flight.get("Airline") else ""
                score = airline_scores.get(airline, 60)  # Default score for unknown airlines
                total_score += score

            return total_score / len(flights) if flights else 60.0

        except Exception:
            return 60.0

    def _calculate_stops_score(self, option: Dict[str, Any]) -> float:
        """Calculate score based on number of stops (0-100)."""
        try:
            stops = option.get("Stops", 0)

            if stops == 0:
                return 100.0  # Direct flight
            elif stops == 1:
                return 70.0   # One stop
            elif stops == 2:
                return 40.0   # Two stops
            else:
                return 20.0   # Multiple stops

        except Exception:
            return 50.0

    def _parse_duration_to_minutes(self, duration_str: str) -> int:
        """Parse duration string to minutes."""
        try:
            # Handle formats like "2h 30m", "1h 45m ", etc.
            duration_str = duration_str.strip()
            total_minutes = 0

            if "h" in duration_str:
                hours_part = duration_str.split("h")[0].strip()
                total_minutes += int(hours_part) * 60

            if "m" in duration_str:
                minutes_part = duration_str.split("h")[-1].replace("m", "").strip()
                if minutes_part:
                    total_minutes += int(minutes_part)

            return total_minutes

        except Exception:
            return 0

    def _generate_enhanced_metadata(self, option: Dict[str, Any]) -> Dict[str, Any]:
        """Generate additional metadata for flight option."""
        return {
            "is_direct": option.get("Stops", 0) == 0,
            "total_travel_time": self._calculate_total_travel_time(option),
            "price_per_hour": self._calculate_price_per_hour(option),
            "departure_day_type": self._get_departure_day_type(option),
            "cabin_class": option.get("Flights", [{}])[0].get("Cabin", "Unknown") if option.get("Flights") else "Unknown"
        }

    def _generate_recommendations(self, option: Dict[str, Any], score: FlightScore) -> List[str]:
        """Generate recommendations based on flight analysis."""
        recommendations = []

        if score.price_score >= 85:
            recommendations.append("Great price for this route")

        if score.duration_score >= 90:
            recommendations.append("Quick flight duration")

        if option.get("Stops", 0) == 0:
            recommendations.append("Direct flight - no layovers")

        if score.schedule_score >= 85:
            recommendations.append("Convenient departure time")

        if score.airline_score >= 85:
            recommendations.append("Highly rated airline")

        if not recommendations:
            recommendations.append("Standard flight option")

        return recommendations

    def _calculate_total_travel_time(self, option: Dict[str, Any]) -> str:
        """Calculate total travel time including layovers."""
        flights = option.get("Flights", [])
        if not flights:
            return "Unknown"

        try:
            first_departure = flights[0].get("DepartureTime", "")
            last_arrival = flights[-1].get("ArrivalTime", "")

            if first_departure and last_arrival:
                # This is a simplified calculation
                # In practice, you'd parse the datetime strings properly
                return f"Total journey time calculation needed"

        except Exception:
            pass

        return "Unknown"

    def _calculate_price_per_hour(self, option: Dict[str, Any]) -> float:
        """Calculate price per hour of travel."""
        try:
            total_fare = float(option.get("TotalFare", 0))
            flights = option.get("Flights", [])

            total_minutes = sum(
                self._parse_duration_to_minutes(flight.get("Duration", "0h 0m"))
                for flight in flights
            )

            if total_minutes > 0:
                return total_fare / (total_minutes / 60)

        except Exception:
            pass

        return 0.0

    def _get_departure_day_type(self, option: Dict[str, Any]) -> str:
        """Determine if departure is on weekend or weekday."""
        try:
            flights = option.get("Flights", [])
            if flights:
                departure_time = flights[0].get("DepartureTime", "")
                if departure_time:
                    # Parse date and determine day type
                    # This is simplified - you'd use proper datetime parsing
                    return "weekday"  # or "weekend"
        except Exception:
            pass

        return "unknown"

    def _generate_enhanced_cache_key(self, request_data: Dict[str, Any]) -> str:
        """Generate cache key for enhanced search results."""
        key_parts = [
            "enhanced_search",
            str(request_data.get("Trips", [])),
            str(request_data.get("ADT", 1)),
            str(request_data.get("CHD", 0)),
            str(request_data.get("INF", 0)),
            str(request_data.get("Cabin", "E")),
            str(request_data.get("FareType", "REGULAR"))
        ]
        return ":".join(key_parts)

    async def _get_cached_results(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get enhanced results from cache."""
        try:
            return await self.cache_service.get_flight_search_results({"cache_key": cache_key})
        except Exception as e:
            print(f"Cache retrieval error: {e}")
            return None

    async def _cache_enhanced_results(self, cache_key: str, results: Dict[str, Any]) -> None:
        """Cache enhanced search results."""
        try:
            await self.cache_service.set_flight_search_results(
                {"cache_key": cache_key},
                results,
                strategy="WRITE_THROUGH"
            )
        except Exception as e:
            print(f"Cache storage error: {e}")

    def _add_performance_metadata(self, results: Dict[str, Any], start_time: float, source: str) -> Dict[str, Any]:
        """Add performance metadata to results."""
        response_time = (time.time() - start_time) * 1000

        return {
            **results,
            "performance_metadata": {
                "response_time_ms": round(response_time, 2),
                "data_source": source,
                "cache_hit": source == "cache",
                "enhanced": True,
                "timestamp": datetime.now().isoformat()
            }
        }

    def _create_error_response(self, request_data: Dict[str, Any], error_message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "TUI": "",
            "Completed": False,
            "CeilingInfo": None,
            "CurrencyCode": "INR",
            "Notices": [f"Search failed: {error_message}"],
            "Trips": None,
            "Code": "500",
            "Msg": ["Enhanced search error"],
            "enhanced": True,
            "error": error_message
        }

    def _update_performance_stats(self, response_time: float) -> None:
        """Update performance statistics."""
        total_searches = self.search_stats["total_searches"]
        current_avg = self.search_stats["avg_response_time"]

        # Calculate new average response time
        new_avg = ((current_avg * (total_searches - 1)) + response_time) / total_searches
        self.search_stats["avg_response_time"] = new_avg

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get enhanced search performance statistics."""
        total = self.search_stats["total_searches"]
        if total == 0:
            return self.search_stats

        return {
            **self.search_stats,
            "cache_hit_rate": self.search_stats["cache_hits"] / total,
            "provider_call_rate": self.search_stats["provider_calls"] / total,
            "avg_response_time_ms": self.search_stats["avg_response_time"] * 1000,
            "scoring_weights": self.scoring_weights
        }


# Global instance
enhanced_flight_search_service = EnhancedFlightSearchService()
