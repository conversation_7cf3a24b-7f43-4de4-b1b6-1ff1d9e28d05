from datetime import datetime, date
import json
import hashlib
from typing import Dict, Any, List

def generate_search_cache_key(data: Dict[str, Any]) -> str:
    """
    Generate a hierarchical cache key for flight search with improved granularity.
    Creates keys that support partial cache hits and better invalidation.
    """
    # Normalize trip data for consistent caching
    normalized_trips = []
    for trip in data.get("Trips", []):
        normalized_trip = {
            "From": trip.get("From", "").upper(),
            "To": trip.get("To", "").upper(),
            "OnwardDate": trip.get("OnwardDate", ""),
        }
        if "ReturnDate" in trip:
            normalized_trip["ReturnDate"] = trip.get("ReturnDate")
        normalized_trips.append(normalized_trip)

    # Create hierarchical cache structure
    cache_json = {
        "route": f"{normalized_trips[0]['From']}-{normalized_trips[0]['To']}",
        "date": normalized_trips[0]["OnwardDate"],
        "passengers": {
            "ADT": data.get("ADT", 0),
            "CHD": data.get("CHD", 0),
            "INF": data.get("INF", 0)
        },
        "preferences": {
            "FareType": data.get("FareType", ""),
            "Cabin": data.get("Cabin", "E")
        },
        "trips": normalized_trips
    }

    # Generate base key for route-date combination
    base_key = f"flight_search:{cache_json['route']}:{cache_json['date']}"

    # Generate specific key for exact search parameters
    specific_key = generate_cache_key(cache_json)

    return f"{base_key}:{specific_key}"

def generate_detail_cache_key(fare_id: str, request_data: Dict[str, Any]) -> str:
    """
    Generate cache key for flight detail/pricing requests.
    """
    cache_json = {
        "fare_id": fare_id,
        "passengers": {
            "ADT": request_data.get("ADT", 0),
            "CHD": request_data.get("CHD", 0),
            "INF": request_data.get("INF", 0)
        }
    }

    detail_key = generate_cache_key(cache_json)
    return f"flight_detail:{fare_id}:{detail_key}"

def generate_cache_key(data: Dict[str, Any]) -> str:
    """
    Generate a consistent hash for any data structure.
    """
    # Ensure consistent serialization
    serialized_data = json.dumps(data, sort_keys=True, separators=(',', ':')).encode('utf-8')
    hash_object = hashlib.sha256(serialized_data)
    return hash_object.hexdigest()[:16]  # Shorter hash for readability

def get_cache_patterns_for_route(from_code: str, to_code: str, date_str: str) -> List[str]:
    """
    Get cache key patterns for invalidating related searches.
    """
    route_pattern = f"flight_search:{from_code.upper()}-{to_code.upper()}:{date_str}"
    return [f"{route_pattern}:*"]

def is_cache_key_expired(cache_key: str, max_age_minutes: int = 15) -> bool:
    """
    Check if a cache key should be considered expired based on naming convention.
    """
    # This would be implemented with Redis TTL checking
    # For now, return False as placeholder
    return False