from pydantic import BaseModel
from typing import List, Optional, Any

# Flight Search Schemas - Match YAML specification exactly
class TripSegment(BaseModel):
    From: str
    To: str
    OnwardDate: str
    ReturnDate: Optional[str] = None
    TUI: Optional[str] = ""

class SearchParameters(BaseModel):
    Airlines: Optional[str] = ""
    GroupType: Optional[str] = ""
    IsDirect: Optional[bool] = False
    IsNearbyAirport: Optional[bool] = True
    IsStudentFare: Optional[bool] = False
    Refundable: Optional[str] = ""

class FlightSearchRequest(BaseModel):
    SecType: Optional[str] = "D"
    FareType: str
    ADT: int
    CHD: int
    INF: int
    Cabin: str
    Source: Optional[str] = "CF"
    Mode: Optional[str] = "AS"
    ClientID: Optional[str] = ""
    IsMultipleCarrier: Optional[bool] = False
    IsRefundable: Optional[bool] = False
    preferedAirlines: Optional[Any] = None
    TUI: Optional[str] = ""
    YTH: Optional[int] = 0
    Trips: List[TripSegment]
    Parameters: Optional[SearchParameters] = None

# Search List Request
class SearchListRequest(BaseModel):
    ClientID: Optional[str] = ""
    TUI: str

# Pricing Request
class PricingTrip(BaseModel):
    Amount: float
    Index: str
    ChannelCode: Optional[str] = None
    OrderID: int
    TUI: str

class PricingRequest(BaseModel):
    Trips: List[PricingTrip]
    ClientID: Optional[str] = ""
    Mode: Optional[str] = "SS"
    Options: Optional[str] = "A"
    Source: Optional[str] = "SF"
    TripType: str

# Pricing List Request
class PricingListRequest(BaseModel):
    TUI: str

# Details Request
class DetailsRequest(BaseModel):
    Trips: List[PricingTrip]
    ClientID: Optional[str] = ""
    Mode: Optional[str] = "SS"
    Options: Optional[str] = "A"
    Source: Optional[str] = "SF"
    TripType: str

# Service Request
class ServiceRequest(BaseModel):
    Trips: List[PricingTrip]
    ClientID: Optional[str] = ""
    Mode: Optional[str] = "SS"
    Options: Optional[str] = "A"
    Source: Optional[str] = "SF"
    TripType: Optional[str] = "O"  # Made optional with default value
    PaidSSR: Optional[bool] = False  # Added PaidSSR field

# Rules Request
class RulesRequest(BaseModel):
    Trips: List[PricingTrip]
    ClientID: Optional[str] = ""
    Mode: Optional[str] = "SS"
    Options: Optional[str] = "A"
    Source: Optional[str] = "SF"
    TripType: Optional[str] = "O"  # Made optional with default value
    PaidSSR: Optional[bool] = False  # Added PaidSSR field

# Airport Search Request
class AirportSearchRequest(BaseModel):
    search_text: str
