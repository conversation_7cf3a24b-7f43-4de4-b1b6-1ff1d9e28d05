"""
Database query optimization utilities and index management.
Provides query analysis, optimization suggestions, and index recommendations.
"""

import re
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from app.microservices.flight_service.database.connection_pool import db_connection_pool


class QueryType(Enum):
    """Types of database queries."""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    CREATE = "CREATE"
    ALTER = "ALTER"
    DROP = "DROP"


@dataclass
class QueryAnalysis:
    """Query analysis results."""
    query_type: QueryType
    tables_involved: List[str]
    columns_used: List[str]
    where_conditions: List[str]
    joins: List[str]
    order_by: List[str]
    group_by: List[str]
    estimated_complexity: str  # low, medium, high
    optimization_suggestions: List[str]


@dataclass
class IndexRecommendation:
    """Index recommendation for query optimization."""
    table_name: str
    columns: List[str]
    index_type: str  # btree, hash, fulltext
    reason: str
    estimated_improvement: str
    priority: str  # low, medium, high


class QueryOptimizer:
    """
    Database query optimizer with analysis and recommendation capabilities.
    """
    
    def __init__(self):
        self.db_pool = db_connection_pool
        
        # Common optimization patterns
        self.optimization_patterns = {
            "missing_where_clause": {
                "pattern": r"SELECT.*FROM\s+\w+(?:\s+\w+)?\s*(?:ORDER|GROUP|LIMIT|$)",
                "suggestion": "Consider adding WHERE clause to limit result set"
            },
            "select_star": {
                "pattern": r"SELECT\s+\*\s+FROM",
                "suggestion": "Avoid SELECT * - specify only needed columns"
            },
            "no_limit": {
                "pattern": r"SELECT.*FROM.*(?!.*LIMIT)",
                "suggestion": "Consider adding LIMIT clause for large result sets"
            },
            "inefficient_like": {
                "pattern": r"LIKE\s+['\"]%.*%['\"]",
                "suggestion": "Leading wildcard in LIKE prevents index usage"
            },
            "function_in_where": {
                "pattern": r"WHERE\s+\w+\([^)]*\)\s*[=<>]",
                "suggestion": "Functions in WHERE clause prevent index usage"
            }
        }
        
        # Index recommendations based on query patterns
        self.index_patterns = {
            "where_equality": {
                "pattern": r"WHERE\s+(\w+)\s*=",
                "index_type": "btree",
                "priority": "high"
            },
            "where_range": {
                "pattern": r"WHERE\s+(\w+)\s*[<>]",
                "index_type": "btree",
                "priority": "medium"
            },
            "order_by": {
                "pattern": r"ORDER\s+BY\s+(\w+)",
                "index_type": "btree",
                "priority": "medium"
            },
            "group_by": {
                "pattern": r"GROUP\s+BY\s+(\w+)",
                "index_type": "btree",
                "priority": "medium"
            }
        }
    
    def analyze_query(self, query: str) -> QueryAnalysis:
        """
        Analyze a SQL query and provide optimization insights.
        
        Args:
            query: SQL query string
            
        Returns:
            Query analysis results
        """
        query_clean = query.strip().upper()
        
        # Determine query type
        query_type = self._get_query_type(query_clean)
        
        # Extract components
        tables = self._extract_tables(query_clean)
        columns = self._extract_columns(query_clean)
        where_conditions = self._extract_where_conditions(query_clean)
        joins = self._extract_joins(query_clean)
        order_by = self._extract_order_by(query_clean)
        group_by = self._extract_group_by(query_clean)
        
        # Estimate complexity
        complexity = self._estimate_complexity(query_clean, tables, joins, where_conditions)
        
        # Generate optimization suggestions
        suggestions = self._generate_optimization_suggestions(query)
        
        return QueryAnalysis(
            query_type=query_type,
            tables_involved=tables,
            columns_used=columns,
            where_conditions=where_conditions,
            joins=joins,
            order_by=order_by,
            group_by=group_by,
            estimated_complexity=complexity,
            optimization_suggestions=suggestions
        )
    
    def _get_query_type(self, query: str) -> QueryType:
        """Determine the type of SQL query."""
        if query.startswith('SELECT'):
            return QueryType.SELECT
        elif query.startswith('INSERT'):
            return QueryType.INSERT
        elif query.startswith('UPDATE'):
            return QueryType.UPDATE
        elif query.startswith('DELETE'):
            return QueryType.DELETE
        elif query.startswith('CREATE'):
            return QueryType.CREATE
        elif query.startswith('ALTER'):
            return QueryType.ALTER
        elif query.startswith('DROP'):
            return QueryType.DROP
        else:
            return QueryType.SELECT  # Default
    
    def _extract_tables(self, query: str) -> List[str]:
        """Extract table names from query."""
        tables = []
        
        # FROM clause
        from_match = re.search(r'FROM\s+(\w+)(?:\s+AS\s+\w+)?', query)
        if from_match:
            tables.append(from_match.group(1).lower())
        
        # JOIN clauses
        join_matches = re.findall(r'JOIN\s+(\w+)(?:\s+AS\s+\w+)?', query)
        for match in join_matches:
            tables.append(match.lower())
        
        # INSERT INTO
        insert_match = re.search(r'INSERT\s+INTO\s+(\w+)', query)
        if insert_match:
            tables.append(insert_match.group(1).lower())
        
        # UPDATE
        update_match = re.search(r'UPDATE\s+(\w+)', query)
        if update_match:
            tables.append(update_match.group(1).lower())
        
        return list(set(tables))
    
    def _extract_columns(self, query: str) -> List[str]:
        """Extract column names from query."""
        columns = []
        
        # SELECT columns
        select_match = re.search(r'SELECT\s+(.*?)\s+FROM', query, re.DOTALL)
        if select_match:
            select_part = select_match.group(1)
            if '*' not in select_part:
                # Extract individual columns
                column_matches = re.findall(r'(\w+)(?:\s+AS\s+\w+)?', select_part)
                columns.extend([col.lower() for col in column_matches])
        
        return list(set(columns))
    
    def _extract_where_conditions(self, query: str) -> List[str]:
        """Extract WHERE conditions from query."""
        conditions = []
        
        where_match = re.search(r'WHERE\s+(.*?)(?:\s+ORDER|\s+GROUP|\s+LIMIT|$)', query, re.DOTALL)
        if where_match:
            where_part = where_match.group(1)
            # Split by AND/OR
            condition_parts = re.split(r'\s+(?:AND|OR)\s+', where_part)
            conditions = [cond.strip() for cond in condition_parts]
        
        return conditions
    
    def _extract_joins(self, query: str) -> List[str]:
        """Extract JOIN clauses from query."""
        joins = []
        
        join_matches = re.findall(r'((?:INNER|LEFT|RIGHT|FULL)?\s*JOIN\s+\w+\s+ON\s+[^)]+)', query)
        joins = [join.strip() for join in join_matches]
        
        return joins
    
    def _extract_order_by(self, query: str) -> List[str]:
        """Extract ORDER BY columns from query."""
        order_by = []
        
        order_match = re.search(r'ORDER\s+BY\s+(.*?)(?:\s+LIMIT|$)', query)
        if order_match:
            order_part = order_match.group(1)
            order_columns = re.findall(r'(\w+)(?:\s+(?:ASC|DESC))?', order_part)
            order_by = [col.lower() for col in order_columns]
        
        return order_by
    
    def _extract_group_by(self, query: str) -> List[str]:
        """Extract GROUP BY columns from query."""
        group_by = []
        
        group_match = re.search(r'GROUP\s+BY\s+(.*?)(?:\s+ORDER|\s+LIMIT|$)', query)
        if group_match:
            group_part = group_match.group(1)
            group_columns = re.findall(r'(\w+)', group_part)
            group_by = [col.lower() for col in group_columns]
        
        return group_by
    
    def _estimate_complexity(self, query: str, tables: List[str], joins: List[str], conditions: List[str]) -> str:
        """Estimate query complexity."""
        complexity_score = 0
        
        # Table count
        complexity_score += len(tables) * 2
        
        # Join count
        complexity_score += len(joins) * 3
        
        # Condition complexity
        for condition in conditions:
            if 'LIKE' in condition:
                complexity_score += 2
            elif any(op in condition for op in ['=', '<', '>', '<=', '>=']):
                complexity_score += 1
            else:
                complexity_score += 3
        
        # Subqueries
        if 'SELECT' in query and query.count('SELECT') > 1:
            complexity_score += 5
        
        # Functions
        if re.search(r'\w+\([^)]*\)', query):
            complexity_score += 2
        
        if complexity_score <= 5:
            return "low"
        elif complexity_score <= 15:
            return "medium"
        else:
            return "high"
    
    def _generate_optimization_suggestions(self, query: str) -> List[str]:
        """Generate optimization suggestions for query."""
        suggestions = []
        
        for pattern_name, pattern_info in self.optimization_patterns.items():
            if re.search(pattern_info["pattern"], query, re.IGNORECASE):
                suggestions.append(pattern_info["suggestion"])
        
        return suggestions
    
    def recommend_indexes(self, query: str) -> List[IndexRecommendation]:
        """
        Recommend indexes for query optimization.
        
        Args:
            query: SQL query string
            
        Returns:
            List of index recommendations
        """
        recommendations = []
        analysis = self.analyze_query(query)
        
        # Recommend indexes based on WHERE conditions
        for condition in analysis.where_conditions:
            column_match = re.search(r'(\w+)\s*[=<>]', condition)
            if column_match:
                column = column_match.group(1).lower()
                
                # Determine table (simplified - assumes single table for now)
                table = analysis.tables_involved[0] if analysis.tables_involved else "unknown"
                
                recommendation = IndexRecommendation(
                    table_name=table,
                    columns=[column],
                    index_type="btree",
                    reason=f"WHERE condition on {column}",
                    estimated_improvement="20-50% faster queries",
                    priority="high"
                )
                recommendations.append(recommendation)
        
        # Recommend indexes for ORDER BY
        if analysis.order_by:
            table = analysis.tables_involved[0] if analysis.tables_involved else "unknown"
            
            recommendation = IndexRecommendation(
                table_name=table,
                columns=analysis.order_by,
                index_type="btree",
                reason="ORDER BY optimization",
                estimated_improvement="10-30% faster sorting",
                priority="medium"
            )
            recommendations.append(recommendation)
        
        # Recommend composite indexes for multiple WHERE conditions
        if len(analysis.where_conditions) > 1:
            where_columns = []
            for condition in analysis.where_conditions:
                column_match = re.search(r'(\w+)\s*[=<>]', condition)
                if column_match:
                    where_columns.append(column_match.group(1).lower())
            
            if len(where_columns) > 1:
                table = analysis.tables_involved[0] if analysis.tables_involved else "unknown"
                
                recommendation = IndexRecommendation(
                    table_name=table,
                    columns=where_columns,
                    index_type="btree",
                    reason="Composite index for multiple WHERE conditions",
                    estimated_improvement="30-70% faster queries",
                    priority="high"
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    async def analyze_slow_queries(self, threshold_ms: float = 1000) -> List[Dict[str, Any]]:
        """
        Analyze slow queries from database performance stats.
        
        Args:
            threshold_ms: Threshold in milliseconds for slow queries
            
        Returns:
            List of slow query analyses
        """
        try:
            db_stats = self.db_pool.get_performance_stats()
            slow_queries = db_stats.get("query_stats", {}).get("slow_queries", [])
            
            analyses = []
            for slow_query in slow_queries:
                if slow_query.get("avg_execution_time", 0) * 1000 > threshold_ms:
                    # For now, we'll use a placeholder query since we don't store the actual query
                    # In a real implementation, you'd store the actual query text
                    placeholder_query = f"-- Slow query hash: {slow_query.get('query_hash', 'unknown')}"
                    
                    analysis = {
                        "query_hash": slow_query.get("query_hash"),
                        "query_type": slow_query.get("query_type"),
                        "avg_execution_time_ms": slow_query.get("avg_execution_time", 0) * 1000,
                        "execution_count": slow_query.get("execution_count", 0),
                        "optimization_suggestions": [
                            "Consider adding appropriate indexes",
                            "Review WHERE clause efficiency",
                            "Check for unnecessary JOINs",
                            "Consider query result caching"
                        ]
                    }
                    analyses.append(analysis)
            
            return analyses
            
        except Exception as e:
            print(f"Error analyzing slow queries: {str(e)}")
            return []
    
    async def generate_index_creation_sql(self, recommendations: List[IndexRecommendation]) -> List[str]:
        """
        Generate SQL statements for creating recommended indexes.
        
        Args:
            recommendations: List of index recommendations
            
        Returns:
            List of CREATE INDEX SQL statements
        """
        sql_statements = []
        
        for i, rec in enumerate(recommendations):
            # Generate index name
            columns_str = "_".join(rec.columns)
            index_name = f"idx_{rec.table_name}_{columns_str}"
            
            # Generate CREATE INDEX statement
            columns_list = ", ".join(rec.columns)
            
            if rec.index_type.lower() == "btree":
                sql = f"CREATE INDEX {index_name} ON {rec.table_name} ({columns_list});"
            elif rec.index_type.lower() == "hash":
                sql = f"CREATE INDEX {index_name} USING HASH ON {rec.table_name} ({columns_list});"
            elif rec.index_type.lower() == "fulltext":
                sql = f"CREATE FULLTEXT INDEX {index_name} ON {rec.table_name} ({columns_list});"
            else:
                sql = f"CREATE INDEX {index_name} ON {rec.table_name} ({columns_list});"
            
            sql_statements.append(sql)
        
        return sql_statements
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """Get query optimization statistics."""
        db_stats = self.db_pool.get_performance_stats()
        
        return {
            "total_unique_queries": db_stats.get("query_stats", {}).get("total_unique_queries", 0),
            "slow_queries_count": db_stats.get("query_stats", {}).get("slow_queries_count", 0),
            "cache_hit_rate": db_stats.get("query_stats", {}).get("cache_hit_rate", 0),
            "optimization_patterns_available": len(self.optimization_patterns),
            "index_patterns_available": len(self.index_patterns)
        }


# Global query optimizer instance
query_optimizer = QueryOptimizer()
