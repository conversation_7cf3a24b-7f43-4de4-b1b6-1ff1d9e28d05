from fastapi import APIRouter, HTTPException, Query
from .schemas import PaymentRequest, PaymentCallbackRequest, PaymentResponse
from .services import PaymentService

router = APIRouter()

@router.post("/b2capis/payments/v1/")
async def process_payment(request: PaymentRequest):
    """
    Process payment for a booking - matches YAML specification exactly.
    
    Args:
        request (PaymentRequest): Payment request containing booking_id
        
    Returns:
        PaymentResponse: Payment processing result
    """
    try:
        result = await PaymentService.process_payment(request.booking_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/apis/b2capis/payments/callback/")
async def payment_callback(
    request: PaymentCallbackRequest = None,
    payment: str = Query(None)
):
    """
    Handle payment callback - matches YAML specification exactly.
    
    Args:
        request (PaymentCallbackRequest): Optional request body
        payment (str): Payment ID from query parameter
        
    Returns:
        dict: Callback processing result
    """
    try:
        payment_id = payment or (request.payment if request else None)
        result = await PaymentService.handle_callback(payment_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
