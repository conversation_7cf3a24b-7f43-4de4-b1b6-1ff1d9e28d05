from fastapi import APIRouter, HTTPException, Depends
from ..booking.booking_client import get_booking_details, get_all_booking_details
from ..auth.auth_middleware import get_current_user

router = APIRouter()

@router.get("/dashboard/bookings/{booking_reference}")
async def fetch_booking_details(
    booking_reference: str,
    current_user: dict = Depends(get_current_user),
):
    """
    Fetch booking details for the admin dashboard.

    Args:
        booking_reference (str): The refernce string of the booking to retrieve.

    Returns:
        dict: Booking details.
    """
    try:
        booking_details = await get_booking_details(booking_reference)
        return booking_details
    except HTTPException as exc:
        raise HTTPException(status_code=exc.status_code, detail=exc.detail)
    
@router.get("/dashboard/all-bookings/")
async def fetch_all_booking_details(
    current_user: dict = Depends(get_current_user)
):
    """
    Fetch all booking details for the admin dashboard.

    Returns:
        dict: Booking details.
    """
    try:
        booking_details = await get_all_booking_details()
        return booking_details
    except HTTPException as exc:
        raise HTTPException(status_code=exc.status_code, detail=exc.detail)