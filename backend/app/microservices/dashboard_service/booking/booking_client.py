import httpx
from fastapi import HTTPException

BOOKING_SERVICE_URL = "http://127.0.0.1:8000"

async def get_booking_details(booking_reference: str):
    """
    Fetch booking details from the booking service.

    Args:
        booking_id (int): The ID of the booking to retrieve.

    Returns:
        dict: Booking details from the booking service.

    Raises:
        HTTPException: If the booking service is unreachable or returns an error.
    """
    url = f"{BOOKING_SERVICE_URL}/apis/get-booking/{booking_reference}"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url)
            response.raise_for_status()  # Raise an error if status >= 400
            return response.json()
        except httpx.RequestError as exc:
            raise HTTPException(
                status_code=503, detail=f"Booking service unavailable: {exc}"
            )
        except httpx.HTTPStatusError as exc:
            raise HTTPException(
                status_code=exc.response.status_code,
                detail=exc.response.json(),
            )

async def get_all_booking_details():
    """
    Fetch all booking details from the booking service.

    Returns:
        dict: Booking details from the booking service.

    Raises:
        HTTPException: If the booking service is unreachable or returns an error.
    """
    url = f"{BOOKING_SERVICE_URL}/apis/get-all-bookings/"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url)
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as exc:
            raise HTTPException(
                status_code=503, detail=f"Booking service unavailable: {exc}"
            )
        except httpx.HTTPStatusError as exc:
            raise HTTPException(
                status_code=exc.response.status_code,
                detail=exc.response.json(),
            )