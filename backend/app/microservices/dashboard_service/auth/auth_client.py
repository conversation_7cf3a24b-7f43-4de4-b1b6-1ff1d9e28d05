import httpx
from fastapi import HTTPException

AUTH_SERVICE_URL = "http://127.0.0.1:8000"

async def verify_token_with_auth_service(token: str):
    """
    Call the authentication service to verify the token.

    Args:
        token (str): JWT token to be verified.

    Returns:
        dict: User details from the token if valid.

    Raises:
        HTTPException: If the token is invalid or the auth service is unreachable.
    """
    url = f"{AUTH_SERVICE_URL}/apis/auth/verify-token"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url, headers={"Authorization": f"Bearer {token}"})
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as exc:
            raise HTTPException(
                status_code=503, detail=f"Auth service unavailable: {exc}"
            )
        except httpx.HTTPStatusError as exc:
            raise HTTPException(
                status_code=exc.response.status_code, detail=exc.response.json()
            )