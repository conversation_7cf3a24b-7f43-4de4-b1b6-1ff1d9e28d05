import random
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .config import database
from .models import User
from .schemas import UserCreate
from sqlalchemy import select

async def create_user(user_data: UserCreate) -> User:
    """
    Create a new user in the database (MySQL compatible).

    Args:
        user_data (UserCreate): User information from the request.

    Returns:
        User: The created user object from the database, including its ID.
    """
    insert_values = {
        "email": user_data.email,
        "name": user_data.name,
        "phone_number": user_data.phone_number,
        "phone_country_code": user_data.phone_country_code,
        "is_verified": False,
        "role": user_data.role
        # Ensure you are not trying to insert an 'id' here; it should be auto-generated
    }

    # For MySQL, database.execute() on an insert query should return the last inserted ID.
    # Do NOT use .returning() for MySQL with this syntax.
    # Ensure User.__table__ correctly refers to your SQLAlchemy Table object for users.
    query = User.__table__.insert().values(**insert_values)
    
    try:
        last_record_id = await database.execute(query)
    except Exception as e:
        # Catch potential errors during execute, though specific pymysql errors are usually raised deeper
        print(f"Database execute error: {e}")
        raise HTTPException(status_code=500, detail=f"Database error during user creation: {e}")

    if not last_record_id:
        # This check is important. If last_record_id is 0 or None, something went wrong.
        # For MySQL, lastrowid (which database.execute should return) is 0 if the last statement
        # didn't modify any rows or for statements that don't generate IDs (like UPDATE without effect).
        # An INSERT that successfully creates an auto-increment ID should return a positive integer.
        print(f"DEBUG: last_record_id was {last_record_id}, which is not a valid new ID.")
        raise HTTPException(status_code=500, detail="Failed to retrieve ID after user creation. last_record_id was not valid.")

    # Now fetch the newly created user from the database using the ID
    select_query = select(User.__table__).where(User.__table__.c.id == last_record_id)
    created_user_row = await database.fetch_one(select_query)

    if not created_user_row:
        raise HTTPException(status_code=500, detail="Could not fetch user after creation, even with a retrieved ID.")

    # Convert the database row (RowProxy) to your User Pydantic model or SQLAlchemy model instance
    created_user_object = User(**dict(created_user_row))
    
    print(f"DEBUG: Returning created user: ID={created_user_object.id}, Email={created_user_object.email}")
    return created_user_object

async def user_by_email(email: str):
    """
    Retrieve a user by their email.

    Args:
        email (str): The email address of the user.

    Returns:
        User: The user object if found, otherwise None.
    """
    # Query the database for a user with the specified email
    query = select(User).where(User.email == email)
    return await database.fetch_one(query)

def generate_otp() -> str:
    """
    Generate a random 6-digit OTP.

    Returns:
        str: A 6-digit OTP as a string.
    """
    return str("123456")

async def send_otp(user: User):
    """
    Simulate sending OTP to the user's phone.

    Args:
        user (User): User object to whom the OTP will be sent.
    """
    otp = generate_otp()
    # Here, you should implement the logic to send the OTP via SMS or email.
    # For example: send_sms(user.phone_number, otp)
    print(f"Sending OTP {otp} to {user.phone_number}")
    # Store OTP in the user's record in the database
    query = User.__table__.update().where(User.email == user.email).values(otp=otp)
    await database.execute(query)

async def verify_otp(email: str, otp: str):
    """
    Verify the OTP provided by the user.

    Args:
        email (str): The email of the user.
        otp (str): The OTP entered by the user.

    Returns:
        bool: True if OTP is correct, otherwise False.
    """
    user = await user_by_email(email)
    if user and user.otp == otp:
        # Clear OTP after verification
        query = User.__table__.update().where(User.email == email).values(otp=None)
        await database.execute(query)
        return True
    return False