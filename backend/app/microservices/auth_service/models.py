from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, Boolean, Enum
from app.models import BaseModel,Base
import enum

class UserRole(enum.Enum):
    ADMIN = "admin"
    CUSTOMER = "customer"

# User model representing the 'users' table in the database
class User(BaseModel):
    __tablename__ = "users"

    # Unique identifier for each user (Primary Key)
    id = Column(Integer, primary_key=True, index=True)
    # Unique email address for user authentication (Indexed for quick lookup)
    email = Column(String(length=100), nullable=False)
    # Optional field for user's name
    name = Column(String(length=50), nullable=True)
    # Optional field for user's phone number
    phone_number = Column(String(length=20), nullable=True)
    # Optional field for user's phone country code
    phone_country_code = Column(String(length=5), nullable=True)
    # Optional field for storing OTP for verification
    otp = Column(String(length=6), nullable=True)
    #  field for storing the is_verified value
    is_verified = Column(Boolean, default=False, nullable=False)
    
    role = Column(Enum(UserRole), nullable=True)