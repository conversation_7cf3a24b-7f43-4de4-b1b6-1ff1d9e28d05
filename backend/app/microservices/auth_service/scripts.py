import mysql.connector
from mysql.connector import Error
from app.config import get_database_connection

def create_users_table():
    try:
        # Connect to MySQL database
        connection = get_database_connection()
        if connection.is_connected():
            cursor = connection.cursor()

            # SQL query to create users table
            create_table_query = """
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                phone_number VARCHAR(15) NOT NULL,
                phone_country_code VARCHAR(5) NOT NULL,
                otp VARCHAR(10),
                is_verified BOOLEAN NOT NULL DEFAULT FALSE
            );
            """

            # Execute the query
            cursor.execute(create_table_query)
            connection.commit()
            print("Users table created successfully.")

    except Error as e:
        print(f"Error: {e}")

    finally:
        # Close the connection
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection closed.")