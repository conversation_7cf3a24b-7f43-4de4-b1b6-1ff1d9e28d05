from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from app.microservices.auth_service.models import Base  # Ensure this import is correct


# Configuration setup
config = context.config
# config.set_main_option('sqlalchemy.url', DATABASE_URL)

fileConfig(config.config_file_name)
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # include_object=include_object,
        # include_schemas=True,
        # version_table='auth_service_alembic_version',
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            # include_schemas=True,  # Allows targeting specific schemas
            # include_object=include_object,
            # version_table='auth_service_alembic_version',
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()