"""Add User role field

Revision ID: 2857445f72a9
Revises: 5cb3c8a9ecc3
Create Date: 2024-12-18 17:09:13.997322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2857445f72a9'
down_revision: Union[str, None] = '5cb3c8a9ecc3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('role', sa.Enum('ADMIN', 'CUSTOMER', name='userrole'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'role')
    # ### end Alembic commands ###
