# auth_service/app/utils/auth_utils.py
from passlib.context import Crypt<PERSON>ontext
from jose import jwt, JWTError
from fastapi import HTTPException
from datetime import datetime, timedelta
from app.microservices.auth_service.config import SECRET_KEY, ACCESS_TOKEN_EXPIRE_MINUTES
from sqlalchemy import select
from .models import User
from .config import database
from enum import Enum

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

async def create_access_token(data: dict):
    """
    Create a JWT access token including the user_id.

    Args:
        data (dict): The data to be included in the payload (e.g., user email).
        db (Session): SQLAlchemy database session.

    Returns:
        str: The generated JWT token.
    """
    query = select(User).where(User.email == data.get("sub"))
    user = await database.fetch_one(query)
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    to_encode = data.copy()
    to_encode["user_id"] = user.id
    to_encode["role"] = user.role.value if isinstance(user.role, Enum) else user.role

    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})

    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm="HS256")
    return encoded_jwt

async def verify_access_token(token: str):
    """
    Verifies the given JWT token and returns payload if valid.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
            raise HTTPException(status_code=401, detail="Token expired")

        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(status_code=401, detail="Token missing user_id")
        
        return payload

    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")