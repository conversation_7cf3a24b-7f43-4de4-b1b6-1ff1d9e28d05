# booking_service/services/booking_service.py
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from .config import engine,database
from .schemas import BookingRequest, MasterBookingSchema
import uuid
from .models import FlightBooking, MasterBooking, Trip, Segment, Flight, Fare, Traveller, ContactInfo
from sqlalchemy import select
from datetime import datetime
from .flight_booking.schemas import FlightBookingSchema, TripSchema 
from fastapi.encoders import jsonable_encoder

class BookingService:
    
    @staticmethod
    async def create_master_booking(data: dict):
        booking_reference = str(uuid.uuid4())
        
        user_id = data.get("user_id")
        
        # if not user_id:
        #     raise ValueError("User ID not found in authentication details.")
        
        # Insert data into MasterBooking table
        query = MasterBooking.__table__.insert().values(
            booking_reference=booking_reference,
            service_type="flight",
            status="pending",
            payment_status="unpaid",
            user_id=user_id
        )
        master_booking_id = await database.execute(query)
        
        # Retrieve the inserted MasterBooking instance
        select_query = select(MasterBooking).where(MasterBooking.id == master_booking_id)
        master_booking = await database.fetch_one(select_query)
        
        if not master_booking:
            return {"detail": "Master booking creation failed"}
        
        # Create FlightBooking
        await BookingService.create_flight_booking(master_booking=master_booking_id, data=data)
        
        # Fetch and assemble related data for the full response
        return await BookingService.get_booking_details(master_booking["booking_reference"])

    @staticmethod
    async def create_flight_booking(master_booking, data):
        flight_booking_reference = str(uuid.uuid4())
        flight_booking = data.get('flight_booking')
        
        query = FlightBooking.__table__.insert().values(
            master_booking_id=master_booking,
            TransactionID=flight_booking_reference,
            provider_info=flight_booking.get('provider_info'),
            TUI=flight_booking.get('TUI'),
            ADT=flight_booking.get('ADT'),
            CHD=flight_booking.get('CHD'),
            INF=flight_booking.get('INF'),
            NetAmount=flight_booking.get('NetAmount'),
            AirlineNetFare=flight_booking.get('AirlineNetFare'),
            SSRAmount=flight_booking.get('SSRAmount'),
            GrossAmount=flight_booking.get('GrossAmount'),
            ActualHoldTime=flight_booking.get('ActualHoldTime'),
            ActualDisplayTime=flight_booking.get('ActualDisplayTime'),
            Hold=flight_booking.get('Hold'),
            ContactInfo={"TESTINFO": "TESTINFO"}
        )
        
        last_record_id = await database.execute(query)
        
        # Create ContactInfo
        contact_info = data.get("ContactInfo")
        if contact_info:
            await BookingService.create_contact_info(
                flight_booking_id=last_record_id, data=contact_info
            )
        
        # Create Travellers
        travellers = data.get("Travellers", [])
        for traveller_data in travellers:
            await BookingService.create_traveller(
                flight_booking_id=last_record_id, data=traveller_data)
        
        # Create Trips
        trips = flight_booking.get('Trips', [])
        for trip in trips:
            await BookingService.create_trip(flight_booking=last_record_id, trip_data=trip)

    @staticmethod
    async def create_trip(flight_booking, trip_data):
        trip = trip_data.get('Journey', [])[0]
        query = Trip.__table__.insert().values(
            flight_booking_id=flight_booking,
            Provider=trip.get('Provider'),
            Stops=trip.get('Stops'),
            Offer=trip.get('Offer'),
            OrderID=trip.get('OrderID'),
            GrossFare=trip.get('GrossFare'),
            NetFare=trip.get('NetFare'),
            Promo=trip.get('Promo'),
        )
        last_record_id = await database.execute(query)
        
        # Create Flight Segments
        segments = trip.get('Segments', [])
        for segment in segments:
            await BookingService.create_flight_segment(trip_id=last_record_id, segment_data=segment)

    @staticmethod
    async def create_flight_segment(trip_id, segment_data):
        segment_query = Segment.__table__.insert().values(
            trip_id=trip_id
        )
        segment_record_id = await database.execute(segment_query)
        
        flight_data = segment_data.get("Flight")
        flight_query = Flight.__table__.insert().values(
            segment_id=segment_record_id,
            FUID=flight_data.get("FUID"),
            VAC=flight_data.get("VAC"),
            MAC=flight_data.get("MAC"),
            OAC=flight_data.get("OAC"),
            FareBasisCode=flight_data.get("FareBasisCode"),
            Airline=flight_data.get("Airline"),
            FlightNo=flight_data.get("FlightNo"),
            ArrivalTime=flight_data.get("ArrivalTime"),
            DepartureTime=flight_data.get("DepartureTime"),
            ArrivalCode=flight_data.get("ArrivalCode"),
            DepartureCode=flight_data.get("DepartureCode"),
            ArrAirportName=flight_data.get("ArrAirportName"),
            DepAirportName=flight_data.get("DepAirportName"),
            ArrivalTerminal=flight_data.get("ArrivalTerminal"),
            DepartureTerminal=flight_data.get("DepartureTerminal"),
            EquipmentType=flight_data.get("EquipmentType"),
            RBD=flight_data.get("RBD"),
            Cabin=flight_data.get("Cabin"),
            Refundable=flight_data.get("Refundable"),
            Duration=flight_data.get("Duration"),
        )
        await database.execute(flight_query)

    @staticmethod
    async def create_traveller(flight_booking_id: int, data: dict):
        query = Traveller.__table__.insert().values(
            flight_booking_id=flight_booking_id,
            PaxID=data.get("PaxID"),
            Title=data.get("Title"),
            FName=data.get("FName"),
            LName=data.get("LName"),
            Age=data.get("Age"),
            DOB=data.get("DOB"),
            Gender=data.get("Gender"),
            PTC=data.get("PTC"),
            Nationality=data.get("Nationality"),
            PassportNo=data.get("PassportNo"),
            PLI=data.get("PLI"),
            PDOE=data.get("PDOE"),
            VisaType=data.get("VisaType"),
            DocType=data.get("DocType"),
        )
        await database.execute(query)

    @staticmethod
    async def create_contact_info(flight_booking_id: int, data: dict):
        query = ContactInfo.__table__.insert().values(
            flight_booking_id=flight_booking_id,
            Title=data.get("Title"),
            FName=data.get("FName"),
            LName=data.get("LName"),
            Mobile=data.get("Mobile"),
            Phone=data.get("Phone"),
            Email=data.get("Email"),
            Address=data.get("Address"),
            CountryCode=data.get("CountryCode"),
            MobileCountryCode=data.get("MobileCountryCode"),
            State=data.get("State"),
            City=data.get("City"),
            PIN=data.get("PIN"),
            GSTAddress=data.get("GSTAddress"),
            GSTCompanyName=data.get("GSTCompanyName"),
            GSTTIN=data.get("GSTTIN"),
            UpdateProfile=data.get("UpdateProfile", False),
            IsGuest=data.get("IsGuest", True),
            SaveGST=data.get("SaveGST", False),
            Language=data.get("Language"),
        )
        await database.execute(query)
                
    @staticmethod
    async def get_booking_details(booking_reference: str):
        """
        Retrieve detailed booking information by booking_reference.
        """
        query = select(MasterBooking).where(MasterBooking.booking_reference == booking_reference)
        master_booking = await database.fetch_one(query)

        if not master_booking:
            return {"detail": f"Booking with reference {booking_reference} not found"}

        master_booking_data = jsonable_encoder(master_booking)

        query = select(FlightBooking).where(FlightBooking.master_booking_id == master_booking.id)
        flight_booking = await database.fetch_one(query)

        if not flight_booking:
            flight_booking_data = None
        else:
            flight_booking_data = jsonable_encoder(flight_booking)

            # Fetch trips for the flight booking
            query = select(Trip).where(Trip.flight_booking_id == flight_booking.id)
            trips = await database.fetch_all(query)

            trip_details = []
            for trip in trips:
                trip_data = jsonable_encoder(trip)
                query = select(Segment).where(Segment.trip_id == trip["id"])
                segments = await database.fetch_all(query)

                segment_details = []
                for segment in segments:
                    segment_data = jsonable_encoder(segment)

                    query = select(Flight).where(Flight.segment_id == segment["id"])
                    flight = await database.fetch_one(query)
                    segment_data["Flight"] = jsonable_encoder(flight) if flight else None

                    query = select(Fare).where(Fare.segment_id == segment["id"])
                    fare = await database.fetch_one(query)
                    segment_data["Fares"] = jsonable_encoder(fare) if fare else None

                    segment_details.append(segment_data)

                trip_data["Segments"] = segment_details
                trip_details.append(trip_data)

            # Fetch travellers for the flight booking
            query = select(Traveller).where(Traveller.flight_booking_id == flight_booking.id)
            travellers = await database.fetch_all(query)
            traveller_data = [jsonable_encoder(traveller) for traveller in travellers]

            # Fetch contact info for the flight booking
            query = select(ContactInfo).where(ContactInfo.flight_booking_id == flight_booking.id)
            contact_info = await database.fetch_one(query)
            contact_info_data = jsonable_encoder(contact_info) if contact_info else None

            # Nest Trips, Travellers, and ContactInfo inside the flight booking
            flight_booking_data["Trips"] = trip_details
            flight_booking_data["Travellers"] = traveller_data
            flight_booking_data["ContactInfo"] = contact_info_data

        return {
            "MasterBooking": master_booking_data,
            "FlightBooking": flight_booking_data,
        }
    
    @staticmethod
    async def get_bookings_by_user(user_id: int):
        """
        Retrieve all bookings for a specific user.
        """
        try:
            query = select(MasterBooking).where(MasterBooking.user_id == user_id)
            bookings = await database.fetch_all(query)

            if not bookings:
                return {"detail": f"No bookings found for user ID {user_id}"}

            bookings_data = [jsonable_encoder(booking) for booking in bookings]

            detailed_bookings = []
            for booking in bookings_data:
                booking_reference = booking["booking_reference"]
                details = await BookingService.get_booking_details(booking_reference)
                detailed_bookings.append(details)

            return detailed_bookings
        except Exception as e:
            raise Exception(f"Error fetching bookings for user: {e}")
    
    @staticmethod
    async def get_all_bookings():
        """
        Retrieve all bookings with detailed information from the database.
        """
        try:
            query = select(MasterBooking)
            all_bookings = await database.fetch_all(query)

            if not all_bookings:
                return {"detail": "No bookings found in the database."}

            all_bookings_data = [jsonable_encoder(booking) for booking in all_bookings]

            detailed_bookings = []
            for booking in all_bookings_data:
                booking_reference = booking["booking_reference"]
                details = await BookingService.get_booking_details(booking_reference)
                detailed_bookings.append(details)

            return detailed_bookings
        except Exception as e:
            raise Exception(f"Error fetching all bookings: {e}")
