# booking_master_service/models/master_booking.py
from sqlalchemy import Column, Integer, String, Enum, DateTime, select
from sqlalchemy import Column, String, Integer, Float, Boolean, JSON, DateTime

from datetime import datetime
import uuid
from .enums import BookingStatus, ServiceType  # Enum definitions
from app.models import Base,BaseModel

class MasterBooking(BaseModel):
    __tablename__ = 'master_bookings'

    id = Column(Integer, primary_key=True, index=True)
    booking_reference = Column(String(length=100), default=lambda: str(uuid.uuid4()), unique=True, nullable=False, index=True)
    service_type = Column(Enum(ServiceType), nullable=False)
    status = Column(Enum(BookingStatus), default=BookingStatus.PENDING, nullable=False)
    payment_status = Column(String(length=10), default="unpaid", nullable=False)
    user_id = Column(Integer, index=True)

    def __repr__(self):
        return f"<MasterBooking(booking_reference={self.booking_reference}, service_type={self.service_type}, status={self.status})>"
    
class FlightBooking(BaseModel):
    __tablename__ = 'flight_bookings'
    # Unique identifier for each flight booking record
    id = Column(Integer, primary_key=True, index=True)
    # Logical link to MasterBooking, not a foreign key to keep loose coupling
    master_booking_id = Column(String(length=36), nullable=False, index=True)
    # JSON field to store provider information
    provider_info = Column(JSON, nullable=False)
    # Unique transaction identifier
    TUI = Column(String(length=255), nullable=False, index=True)
    # Mode of the transaction (e.g., 'Online', 'Offline')
    Mode = Column(String(length=50), nullable=True)
    # Transaction ID related to the booking
    TransactionID = Column(String(length=36), nullable=False, index=True)
    # Number of adult passengers
    ADT = Column(Integer, nullable=False)
    # Number of child passengers
    CHD = Column(Integer, nullable=False)
    # Number of infant passengers
    INF = Column(Integer, nullable=False)
    # Net amount of the booking
    NetAmount = Column(Float, nullable=False)
    # Net fare amount provided by the airline
    AirlineNetFare = Column(Float, nullable=False)
    # Amount related to special service requests
    SSRAmount = Column(Float, nullable=False)
    # Gross total amount of the booking
    GrossAmount = Column(Float, nullable=False)
    # Boolean indicating if the booking is on hold
    Hold = Column(Boolean, nullable=False)
    # Actual hold time duration in minutes
    ActualHoldTime = Column(Integer, nullable=False)
    # Actual display time duration in minutes
    ActualDisplayTime = Column(Integer, nullable=False)
    # Status code related to the booking
    Code = Column(String(length=50), nullable=True, index=True)
    # JSON field for storing messages or additional information
    Msg = Column(JSON, nullable=True)
    # JSON field for contact information
    ContactInfo = Column(JSON, nullable=False)

class Trip(Base):
    __tablename__ = 'trips'
    # Unique identifier for each trip record
    id = Column(Integer, primary_key=True, index=True)
    flight_booking_id = Column(String(length=36), nullable=False, index=True)
    # Provider for the trip (e.g., 'Tripjack')
    Provider = Column(String(length=50), nullable=False)
    # Number of stops for the trip
    Stops = Column(String(length=10), nullable=False)
    # Offer details (if any)
    Offer = Column(String(length=255), nullable=True)
    # Order ID for the trip
    OrderID = Column(Integer, nullable=False)
    # Gross fare for the trip
    GrossFare = Column(Float, nullable=False)
    # Net fare for the trip
    NetFare = Column(Float, nullable=False)
    # Promo details (if any)
    Promo = Column(String(length=255), nullable=True)

class Segment(Base):
    __tablename__ = 'segments'
    # Unique identifier for each segment
    id = Column(Integer, primary_key=True, index=True)
    trip_id = Column(String(length=36), nullable=False, index=True)

class Flight(Base):
    __tablename__ = 'flights'
    # Unique identifier for each flight record
    id = Column(Integer, primary_key=True, index=True)
    segment_id = Column(String(length=36), nullable=False, index=True)
    # Flight unique identifier
    FUID = Column(String(length=50), nullable=False, index=True)
    # Airline codes (VAC, MAC, OAC)
    VAC = Column(String(length=10), nullable=False)
    MAC = Column(String(length=10), nullable=False)
    OAC = Column(String(length=10), nullable=False)
    # Fare basis code
    FareBasisCode = Column(String(length=50), nullable=False)
    # Airline name
    Airline = Column(String(length=255), nullable=False)
    # Flight number
    FlightNo = Column(String(length=10), nullable=False)
    # Arrival and departure details
    ArrivalTime = Column(DateTime, nullable=False)
    DepartureTime = Column(DateTime, nullable=False)
    ArrivalCode = Column(String(length=10), nullable=False)
    DepartureCode = Column(String(length=10), nullable=False)
    ArrAirportName = Column(String(length=255), nullable=False)
    DepAirportName = Column(String(length=255), nullable=False)
    ArrivalTerminal = Column(String(length=50), nullable=True)
    DepartureTerminal = Column(String(length=50), nullable=True)
    EquipmentType = Column(String(length=50), nullable=True)
    # Reservation booking designator
    RBD = Column(String(length=10), nullable=False)
    # Cabin type (e.g., 'E' for economy)
    Cabin = Column(String(length=10), nullable=False)
    # Whether the flight is refundable or not
    Refundable = Column(String(length=10), nullable=False)
    # Duration of the flight
    Duration = Column(String(length=20), nullable=False)

class Fare(Base):
    __tablename__ = 'fares'
    # Unique identifier for each fare record
    id = Column(Integer, primary_key=True, index=True)
    segment_id = Column(String(length=36), nullable=False, index=True)
    # # Passenger type code (e.g., 'ADT')
    # PTC = Column(String(length=10), nullable=True)
    # Various fare-related fields
    Fare = Column(Float, nullable=True)
    YQ = Column(Float, nullable=True)
    PSF = Column(Float, nullable=True)
    Tax = Column(Float, nullable=True)
    GrossFare = Column(Float, nullable=False)
    NetFare = Column(Float, nullable=False)

class Traveller(Base):
    __tablename__ = 'travellers'
    # Unique identifier for each traveller record
    id = Column(Integer, primary_key=True, index=True)
    flight_booking_id = Column(String(length=36), nullable=False, index=True)
    # Passenger ID for internal tracking
    PaxID = Column(Integer, nullable=False)
    # Title of the traveller (e.g., 'Mr', 'Ms')
    Title = Column(String(length=10), nullable=False)
    # First name of the traveller
    FName = Column(String(length=100), nullable=False)
    # Last name of the traveller
    LName = Column(String(length=100), nullable=False)
    # Age of the traveller
    Age = Column(Integer, nullable=False)
    # Date of birth of the traveller
    DOB = Column(DateTime, nullable=False)
    # Gender of the traveller (e.g., 'M', 'F')
    Gender = Column(String(length=1), nullable=False)
    # Passenger type code (e.g., 'ADT' for adult)
    PTC = Column(String(length=10), nullable=False)
    # Nationality of the traveller
    Nationality = Column(String(length=50), nullable=False)
    # Passport number of the traveller
    PassportNo = Column(String(length=20), nullable=False, index=True)
    # Passport issuing country
    PLI = Column(String(length=50), nullable=False)
    # Passport date of expiration
    PDOE = Column(DateTime, nullable=False)
    # Type of visa (if applicable)
    VisaType = Column(String(length=50), nullable=True)
    # Document type (e.g., 'P' for passport)
    DocType = Column(String(length=10), nullable=False)

class ContactInfo(Base):
    __tablename__ = 'contact_info'
    # Unique identifier for each contact record
    id = Column(Integer, primary_key=True, index=True)
    flight_booking_id = Column(String(length=36), nullable=False, index=True)
    # Title of the contact person (e.g., 'Mr', 'Ms')
    Title = Column(String(length=10), nullable=True)
    # First name of the contact person
    FName = Column(String(length=100), nullable=False)
    # Last name of the contact person
    LName = Column(String(length=100), nullable=False)
    # Mobile number of the contact person
    Mobile = Column(String(length=15), nullable=False, index=True)
    # Phone number of the contact person
    Phone = Column(String(length=15), nullable=True)
    # Email address of the contact person
    Email = Column(String(length=255), nullable=False, index=True)
    # Address of the contact person or organization
    Address = Column(String(length=255), nullable=True)
    # Country code of the contact
    CountryCode = Column(String(length=5), nullable=False)
    # Mobile country code
    MobileCountryCode = Column(String(length=5), nullable=True)
    # State of the contact
    State = Column(String(length=100), nullable=True)
    # City of the contact
    City = Column(String(length=100), nullable=True)
    # Postal/ZIP code
    PIN = Column(String(length=10), nullable=True)
    # GST-related address if applicable
    GSTAddress = Column(String(length=255), nullable=True)
    # Company name for GST purposes
    GSTCompanyName = Column(String(length=255), nullable=True)
    # GST identification number
    GSTTIN = Column(String(length=50), nullable=True)
    # Boolean to indicate if the profile update is requested
    UpdateProfile = Column(Boolean, default=False)
    # Boolean to indicate if the contact is a guest
    IsGuest = Column(Boolean, default=True)
    # Boolean to indicate if GST information should be saved
    SaveGST = Column(Boolean, default=False)
    # Preferred language of the contact
    Language = Column(String(length=50), nullable=True)

    @staticmethod
    async def create_contact_info(data, database):
        contact_info_id = str(uuid.uuid4())
        query = ContactInfo.__table__.insert().values(
            id=contact_info_id,
            Title=data.Title,
            FName=data.FName,
            LName=data.LName,
            Mobile=data.Mobile,
            Phone=data.Phone,
            Email=data.Email,
            Address=data.Address,
            CountryCode=data.CountryCode,
            MobileCountryCode=data.MobileCountryCode,
            State=data.State,
            City=data.City,
            PIN=data.PIN,
            GSTAddress=data.GSTAddress,
            GSTCompanyName=data.GSTCompanyName,
            GSTTIN=data.GSTTIN,
            UpdateProfile=data.UpdateProfile,
            IsGuest=data.IsGuest,
            SaveGST=data.SaveGST,
            Language=data.Language,
            GSTaddress=data.GSTaddress
        )
        last_record_id = await database.execute(query)
        select_query = select(ContactInfo).where(ContactInfo.id == last_record_id)
        result = await database.fetch_one(select_query)
        if result:
            return dict(result)
        return {"detail": "Record creation failed"}

class SSR(Base):
    __tablename__ = 'ssrs'
    # Unique identifier for each special service request record
    id = Column(Integer, primary_key=True, index=True)
    flight_booking_id = Column(String(length=36), nullable=False, index=True)
    # Passenger type code
    PTC = Column(String(length=10), nullable=False)
    # Passenger ID related to the SSR
    PaxId = Column(String(length=36), nullable=False, index=True)
    # Flight unique identifier
    FUID = Column(String(length=36), nullable=False)
    # Code of the SSR (e.g., 'BAG' for baggage)
    Code = Column(String(length=10), nullable=False)
    # Description of the SSR
    Description = Column(String(length=255), nullable=False)
    #
