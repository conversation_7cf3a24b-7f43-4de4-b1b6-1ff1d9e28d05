"""Added Flight Booking all Models

Revision ID: 04e9be79d0c3
Revises: 22eb2750a7b9
Create Date: 2024-11-02 20:16:42.573660

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '04e9be79d0c3'
down_revision: Union[str, None] = '22eb2750a7b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contact_info',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('flight_booking_id', sa.String(length=36), nullable=False),
    sa.Column('Title', sa.String(length=10), nullable=True),
    sa.Column('FName', sa.String(length=100), nullable=False),
    sa.Column('LName', sa.String(length=100), nullable=False),
    sa.Column('Mobile', sa.String(length=15), nullable=False),
    sa.Column('Phone', sa.String(length=15), nullable=True),
    sa.Column('Email', sa.String(length=255), nullable=False),
    sa.Column('Address', sa.String(length=255), nullable=True),
    sa.Column('CountryCode', sa.String(length=5), nullable=False),
    sa.Column('MobileCountryCode', sa.String(length=5), nullable=True),
    sa.Column('State', sa.String(length=100), nullable=True),
    sa.Column('City', sa.String(length=100), nullable=True),
    sa.Column('PIN', sa.String(length=10), nullable=True),
    sa.Column('GSTAddress', sa.String(length=255), nullable=True),
    sa.Column('GSTCompanyName', sa.String(length=255), nullable=True),
    sa.Column('GSTTIN', sa.String(length=50), nullable=True),
    sa.Column('UpdateProfile', sa.Boolean(), nullable=True),
    sa.Column('IsGuest', sa.Boolean(), nullable=True),
    sa.Column('SaveGST', sa.Boolean(), nullable=True),
    sa.Column('Language', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contact_info_Email'), 'contact_info', ['Email'], unique=False)
    op.create_index(op.f('ix_contact_info_Mobile'), 'contact_info', ['Mobile'], unique=False)
    op.create_index(op.f('ix_contact_info_flight_booking_id'), 'contact_info', ['flight_booking_id'], unique=False)
    op.create_index(op.f('ix_contact_info_id'), 'contact_info', ['id'], unique=False)
    op.create_table('fares',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('segment_id', sa.String(length=36), nullable=False),
    sa.Column('PTC', sa.String(length=10), nullable=False),
    sa.Column('Fare', sa.Float(), nullable=False),
    sa.Column('YQ', sa.Float(), nullable=False),
    sa.Column('PSF', sa.Float(), nullable=False),
    sa.Column('Tax', sa.Float(), nullable=False),
    sa.Column('GrossFare', sa.Float(), nullable=False),
    sa.Column('NetFare', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_fares_id'), 'fares', ['id'], unique=False)
    op.create_index(op.f('ix_fares_segment_id'), 'fares', ['segment_id'], unique=False)
    op.create_table('flight_bookings',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('master_booking_id', sa.String(length=36), nullable=False),
    sa.Column('provider_info', sa.JSON(), nullable=False),
    sa.Column('TUI', sa.String(length=255), nullable=False),
    sa.Column('Mode', sa.String(length=50), nullable=True),
    sa.Column('TransactionID', sa.Integer(), nullable=False),
    sa.Column('ADT', sa.Integer(), nullable=False),
    sa.Column('CHD', sa.Integer(), nullable=False),
    sa.Column('INF', sa.Integer(), nullable=False),
    sa.Column('NetAmount', sa.Float(), nullable=False),
    sa.Column('AirlineNetFare', sa.Float(), nullable=False),
    sa.Column('SSRAmount', sa.Float(), nullable=False),
    sa.Column('GrossAmount', sa.Float(), nullable=False),
    sa.Column('Hold', sa.Boolean(), nullable=False),
    sa.Column('ActualHoldTime', sa.Integer(), nullable=False),
    sa.Column('ActualDisplayTime', sa.Integer(), nullable=False),
    sa.Column('Code', sa.String(length=50), nullable=True),
    sa.Column('Msg', sa.JSON(), nullable=True),
    sa.Column('ContactInfo', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_flight_bookings_Code'), 'flight_bookings', ['Code'], unique=False)
    op.create_index(op.f('ix_flight_bookings_TUI'), 'flight_bookings', ['TUI'], unique=False)
    op.create_index(op.f('ix_flight_bookings_TransactionID'), 'flight_bookings', ['TransactionID'], unique=False)
    op.create_index(op.f('ix_flight_bookings_id'), 'flight_bookings', ['id'], unique=False)
    op.create_index(op.f('ix_flight_bookings_master_booking_id'), 'flight_bookings', ['master_booking_id'], unique=False)
    op.create_table('flights',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('segment_id', sa.String(length=36), nullable=False),
    sa.Column('FUID', sa.String(length=50), nullable=False),
    sa.Column('VAC', sa.String(length=10), nullable=False),
    sa.Column('MAC', sa.String(length=10), nullable=False),
    sa.Column('OAC', sa.String(length=10), nullable=False),
    sa.Column('FareBasisCode', sa.String(length=50), nullable=False),
    sa.Column('Airline', sa.String(length=255), nullable=False),
    sa.Column('FlightNo', sa.String(length=10), nullable=False),
    sa.Column('ArrivalTime', sa.DateTime(), nullable=False),
    sa.Column('DepartureTime', sa.DateTime(), nullable=False),
    sa.Column('ArrivalCode', sa.String(length=10), nullable=False),
    sa.Column('DepartureCode', sa.String(length=10), nullable=False),
    sa.Column('ArrAirportName', sa.String(length=255), nullable=False),
    sa.Column('DepAirportName', sa.String(length=255), nullable=False),
    sa.Column('ArrivalTerminal', sa.String(length=50), nullable=True),
    sa.Column('DepartureTerminal', sa.String(length=50), nullable=True),
    sa.Column('EquipmentType', sa.String(length=50), nullable=True),
    sa.Column('RBD', sa.String(length=10), nullable=False),
    sa.Column('Cabin', sa.String(length=10), nullable=False),
    sa.Column('Refundable', sa.String(length=10), nullable=False),
    sa.Column('Duration', sa.String(length=20), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_flights_FUID'), 'flights', ['FUID'], unique=False)
    op.create_index(op.f('ix_flights_id'), 'flights', ['id'], unique=False)
    op.create_index(op.f('ix_flights_segment_id'), 'flights', ['segment_id'], unique=False)
    op.create_table('segments',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('trip_id', sa.String(length=36), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_segments_id'), 'segments', ['id'], unique=False)
    op.create_index(op.f('ix_segments_trip_id'), 'segments', ['trip_id'], unique=False)
    op.create_table('ssrs',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('flight_booking_id', sa.String(length=36), nullable=False),
    sa.Column('PTC', sa.String(length=10), nullable=False),
    sa.Column('PaxId', sa.String(length=36), nullable=False),
    sa.Column('FUID', sa.String(length=36), nullable=False),
    sa.Column('Code', sa.String(length=10), nullable=False),
    sa.Column('Description', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ssrs_PaxId'), 'ssrs', ['PaxId'], unique=False)
    op.create_index(op.f('ix_ssrs_flight_booking_id'), 'ssrs', ['flight_booking_id'], unique=False)
    op.create_index(op.f('ix_ssrs_id'), 'ssrs', ['id'], unique=False)
    op.create_table('travellers',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('flight_booking_id', sa.String(length=36), nullable=False),
    sa.Column('PaxID', sa.Integer(), nullable=False),
    sa.Column('Title', sa.String(length=10), nullable=False),
    sa.Column('FName', sa.String(length=100), nullable=False),
    sa.Column('LName', sa.String(length=100), nullable=False),
    sa.Column('Age', sa.Integer(), nullable=False),
    sa.Column('DOB', sa.DateTime(), nullable=False),
    sa.Column('Gender', sa.String(length=1), nullable=False),
    sa.Column('PTC', sa.String(length=10), nullable=False),
    sa.Column('Nationality', sa.String(length=50), nullable=False),
    sa.Column('PassportNo', sa.String(length=20), nullable=False),
    sa.Column('PLI', sa.String(length=50), nullable=False),
    sa.Column('PDOE', sa.DateTime(), nullable=False),
    sa.Column('VisaType', sa.String(length=50), nullable=True),
    sa.Column('DocType', sa.String(length=10), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_travellers_PassportNo'), 'travellers', ['PassportNo'], unique=False)
    op.create_index(op.f('ix_travellers_flight_booking_id'), 'travellers', ['flight_booking_id'], unique=False)
    op.create_index(op.f('ix_travellers_id'), 'travellers', ['id'], unique=False)
    op.create_table('trips',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('flight_booking_id', sa.String(length=36), nullable=False),
    sa.Column('Provider', sa.String(length=50), nullable=False),
    sa.Column('Stops', sa.String(length=10), nullable=False),
    sa.Column('Offer', sa.String(length=255), nullable=True),
    sa.Column('OrderID', sa.Integer(), nullable=False),
    sa.Column('GrossFare', sa.Float(), nullable=False),
    sa.Column('NetFare', sa.Float(), nullable=False),
    sa.Column('Promo', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_trips_flight_booking_id'), 'trips', ['flight_booking_id'], unique=False)
    op.create_index(op.f('ix_trips_id'), 'trips', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_trips_id'), table_name='trips')
    op.drop_index(op.f('ix_trips_flight_booking_id'), table_name='trips')
    op.drop_table('trips')
    op.drop_index(op.f('ix_travellers_id'), table_name='travellers')
    op.drop_index(op.f('ix_travellers_flight_booking_id'), table_name='travellers')
    op.drop_index(op.f('ix_travellers_PassportNo'), table_name='travellers')
    op.drop_table('travellers')
    op.drop_index(op.f('ix_ssrs_id'), table_name='ssrs')
    op.drop_index(op.f('ix_ssrs_flight_booking_id'), table_name='ssrs')
    op.drop_index(op.f('ix_ssrs_PaxId'), table_name='ssrs')
    op.drop_table('ssrs')
    op.drop_index(op.f('ix_segments_trip_id'), table_name='segments')
    op.drop_index(op.f('ix_segments_id'), table_name='segments')
    op.drop_table('segments')
    op.drop_index(op.f('ix_flights_segment_id'), table_name='flights')
    op.drop_index(op.f('ix_flights_id'), table_name='flights')
    op.drop_index(op.f('ix_flights_FUID'), table_name='flights')
    op.drop_table('flights')
    op.drop_index(op.f('ix_flight_bookings_master_booking_id'), table_name='flight_bookings')
    op.drop_index(op.f('ix_flight_bookings_id'), table_name='flight_bookings')
    op.drop_index(op.f('ix_flight_bookings_TransactionID'), table_name='flight_bookings')
    op.drop_index(op.f('ix_flight_bookings_TUI'), table_name='flight_bookings')
    op.drop_index(op.f('ix_flight_bookings_Code'), table_name='flight_bookings')
    op.drop_table('flight_bookings')
    op.drop_index(op.f('ix_fares_segment_id'), table_name='fares')
    op.drop_index(op.f('ix_fares_id'), table_name='fares')
    op.drop_table('fares')
    op.drop_index(op.f('ix_contact_info_id'), table_name='contact_info')
    op.drop_index(op.f('ix_contact_info_flight_booking_id'), table_name='contact_info')
    op.drop_index(op.f('ix_contact_info_Mobile'), table_name='contact_info')
    op.drop_index(op.f('ix_contact_info_Email'), table_name='contact_info')
    op.drop_table('contact_info')
    # ### end Alembic commands ###
