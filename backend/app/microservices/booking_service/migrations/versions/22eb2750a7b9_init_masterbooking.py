"""init MasterBooking

Revision ID: 22eb2750a7b9
Revises: 
Create Date: 2024-11-02 16:55:56.419849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '22eb2750a7b9'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('master_bookings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('booking_reference', sa.String(length=100), nullable=False),
    sa.Column('service_type', sa.Enum('FLIGHT', 'HOTEL', 'RAIL', name='servicetype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'CONFIRMED', 'CANCELED', name='bookingstatus'), nullable=False),
    sa.Column('payment_status', sa.String(length=10), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_master_bookings_booking_reference'), 'master_bookings', ['booking_reference'], unique=True)
    op.create_index(op.f('ix_master_bookings_id'), 'master_bookings', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_master_bookings_id'), table_name='master_bookings')
    op.drop_index(op.f('ix_master_bookings_booking_reference'), table_name='master_bookings')
    op.drop_table('master_bookings')
    # ### end Alembic commands ###
