"""Id changed

Revision ID: 9b25761447a5
Revises: 04e9be79d0c3
Create Date: 2024-11-15 12:11:34.364378

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9b25761447a5'
down_revision: Union[str, None] = '04e9be79d0c3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('contact_info', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('fares', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('flight_bookings', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('flights', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('segments', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ssrs', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('travellers', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('trips', 'id',
               existing_type=mysql.VARCHAR(length=36),
               type_=sa.Integer(),
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('trips', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('travellers', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ssrs', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('segments', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('flights', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('flight_bookings', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('fares', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('contact_info', 'id',
               existing_type=sa.Integer(),
               type_=mysql.VARCHAR(length=36),
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###
