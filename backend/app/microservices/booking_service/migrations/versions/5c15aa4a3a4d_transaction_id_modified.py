"""Transaction ID modified

Revision ID: 5c15aa4a3a4d
Revises: 9b25761447a5
Create Date: 2024-11-15 12:18:08.009813

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '5c15aa4a3a4d'
down_revision: Union[str, None] = '9b25761447a5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('flight_bookings', 'TransactionID',
               existing_type=mysql.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('flight_bookings', 'TransactionID',
               existing_type=sa.String(length=36),
               type_=mysql.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
