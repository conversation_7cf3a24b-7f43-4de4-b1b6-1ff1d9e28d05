"""Fare modifications

Revision ID: d86fb53256c8
Revises: 5c15aa4a3a4d
Create Date: 2024-11-15 15:23:29.125582

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd86fb53256c8'
down_revision: Union[str, None] = '5c15aa4a3a4d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('fares', 'Fare',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('fares', 'YQ',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('fares', 'PSF',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('fares', 'Tax',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.drop_column('fares', 'PTC')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fares', sa.Column('PTC', mysql.VARCHAR(length=10), nullable=False))
    op.alter_column('fares', 'Tax',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('fares', 'PSF',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('fares', 'YQ',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('fares', 'Fare',
               existing_type=mysql.FLOAT(),
               nullable=False)
    # ### end Alembic commands ###
