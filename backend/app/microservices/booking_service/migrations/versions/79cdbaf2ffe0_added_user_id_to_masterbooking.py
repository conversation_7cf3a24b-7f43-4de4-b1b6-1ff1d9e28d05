"""added user_id to masterbooking

Revision ID: 79cdbaf2ffe0
Revises: d86fb53256c8
Create Date: 2024-12-17 10:44:06.068619

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '79cdbaf2ffe0'
down_revision: Union[str, None] = 'd86fb53256c8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('master_bookings', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_master_bookings_user_id'), 'master_bookings', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_master_bookings_user_id'), table_name='master_bookings')
    op.drop_column('master_bookings', 'user_id')
    # ### end Alembic commands ###
