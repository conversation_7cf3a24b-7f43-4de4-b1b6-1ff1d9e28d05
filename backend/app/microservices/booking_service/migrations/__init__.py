from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context
from app.microservices.flight_service.shared_service.models import Base  # Ensure this import is correct

# Configuration setup
config = context.config
fileConfig(config.config_file_name)
target_metadata = Base.metadata


# def include_object(obj, name, is_value, namespace, other,**kw):
   
#     # Only include objects from the specific schema or specific tables
#     if hasattr(obj, 'schema'):
#         # Specify the schema you want to include
#         if obj.schema == 'flight_service_shared_service_service_schema':
#             return True
#         return False  # Exclude all other schemas

#     return False  # Include all other objects without a schema



def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        # include_object=include_object,
        # include_schemas=True,
        # version_table='flight_service_shared_service_alembic_version',
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata,
            # include_schemas=True,  # Allows targeting specific schemas
            # include_object=include_object,
            # version_table='flight_service_shared_service_alembic_version',
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
