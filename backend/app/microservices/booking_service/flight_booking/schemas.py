# booking_service/schemas.py

from marshmallow_sqlalchemy import SQLAlchemySchema, auto_field
from app.microservices.booking_service.models import FlightBooking,Trip

class FlightBookingSchema(SQLAlchemySchema):
    class Meta:
        model = FlightBooking
        load_instance = True

    id = auto_field()
    # booking_reference = auto_field()
    # service_type = auto_field()
    # status = auto_field()
    # payment_status = auto_field()


class TripSchema(SQLAlchemySchema):
    class Meta:
        model = Trip
        load_instance = True

    id = auto_field()
    flight_booking_id = auto_field()