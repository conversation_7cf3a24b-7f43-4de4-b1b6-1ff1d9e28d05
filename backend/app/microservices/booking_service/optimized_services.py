"""
Optimized booking service with database connection pooling and batch operations.
Implements efficient database operations, query optimization, and result caching.
"""

import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Tuple
import json

from app.microservices.flight_service.database.connection_pool import db_connection_pool
from app.config import RedisCache


class OptimizedBookingService:
    """
    Optimized booking service with advanced database operations and caching.
    """
    
    def __init__(self):
        self.db_pool = db_connection_pool
        self.redis_client = RedisCache.connection()
        
        # Performance tracking
        self.service_stats = {
            "total_bookings_created": 0,
            "total_bookings_retrieved": 0,
            "batch_operations": 0,
            "cache_hits": 0,
            "database_queries": 0,
            "avg_booking_creation_time": 0.0,
            "avg_booking_retrieval_time": 0.0
        }
    
    async def create_booking_optimized(
        self, 
        booking_data: Dict[str, Any],
        flight_details: Dict[str, Any],
        travellers: List[Dict[str, Any]],
        contact_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create booking with optimized batch database operations.
        
        Args:
            booking_data: Master booking information
            flight_details: Flight booking details
            travellers: List of traveller information
            contact_info: Contact information
            
        Returns:
            Created booking information with performance metadata
        """
        start_time = time.time()
        self.service_stats["total_bookings_created"] += 1
        
        try:
            # Generate booking ID
            booking_id = self._generate_booking_id()
            
            # Prepare batch queries for atomic operation
            queries = []
            
            # 1. Master booking insert
            master_booking_query = """
                INSERT INTO master_bookings (
                    booking_id, user_id, booking_status, total_amount, 
                    currency, created_at, updated_at
                ) VALUES (
                    %(booking_id)s, %(user_id)s, %(status)s, %(total_amount)s,
                    %(currency)s, %(created_at)s, %(updated_at)s
                )
            """
            
            master_booking_params = {
                "booking_id": booking_id,
                "user_id": booking_data.get("user_id"),
                "status": "PENDING",
                "total_amount": booking_data.get("total_amount", 0),
                "currency": booking_data.get("currency", "INR"),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            queries.append((master_booking_query, master_booking_params))
            
            # 2. Flight booking details insert
            flight_booking_query = """
                INSERT INTO flight_bookings (
                    booking_id, flight_id, fare_id, departure_date, 
                    return_date, cabin_class, fare_type, pnr_number,
                    booking_reference, created_at
                ) VALUES (
                    %(booking_id)s, %(flight_id)s, %(fare_id)s, %(departure_date)s,
                    %(return_date)s, %(cabin_class)s, %(fare_type)s, %(pnr_number)s,
                    %(booking_reference)s, %(created_at)s
                )
            """
            
            flight_booking_params = {
                "booking_id": booking_id,
                "flight_id": flight_details.get("flight_id"),
                "fare_id": flight_details.get("fare_id"),
                "departure_date": flight_details.get("departure_date"),
                "return_date": flight_details.get("return_date"),
                "cabin_class": flight_details.get("cabin_class", "E"),
                "fare_type": flight_details.get("fare_type", "REGULAR"),
                "pnr_number": flight_details.get("pnr_number"),
                "booking_reference": flight_details.get("booking_reference"),
                "created_at": datetime.now(timezone.utc)
            }
            
            queries.append((flight_booking_query, flight_booking_params))
            
            # 3. Travellers batch insert
            for i, traveller in enumerate(travellers):
                traveller_query = """
                    INSERT INTO booking_travellers (
                        booking_id, traveller_index, title, first_name, last_name,
                        date_of_birth, gender, passport_number, nationality,
                        created_at
                    ) VALUES (
                        %(booking_id)s, %(traveller_index)s, %(title)s, %(first_name)s,
                        %(last_name)s, %(date_of_birth)s, %(gender)s, %(passport_number)s,
                        %(nationality)s, %(created_at)s
                    )
                """
                
                traveller_params = {
                    "booking_id": booking_id,
                    "traveller_index": i,
                    "title": traveller.get("title"),
                    "first_name": traveller.get("first_name"),
                    "last_name": traveller.get("last_name"),
                    "date_of_birth": traveller.get("date_of_birth"),
                    "gender": traveller.get("gender"),
                    "passport_number": traveller.get("passport_number"),
                    "nationality": traveller.get("nationality"),
                    "created_at": datetime.now(timezone.utc)
                }
                
                queries.append((traveller_query, traveller_params))
            
            # 4. Contact information insert
            contact_query = """
                INSERT INTO booking_contacts (
                    booking_id, email, phone, address, city, country,
                    postal_code, created_at
                ) VALUES (
                    %(booking_id)s, %(email)s, %(phone)s, %(address)s,
                    %(city)s, %(country)s, %(postal_code)s, %(created_at)s
                )
            """
            
            contact_params = {
                "booking_id": booking_id,
                "email": contact_info.get("email"),
                "phone": contact_info.get("phone"),
                "address": contact_info.get("address"),
                "city": contact_info.get("city"),
                "country": contact_info.get("country"),
                "postal_code": contact_info.get("postal_code"),
                "created_at": datetime.now(timezone.utc)
            }
            
            queries.append((contact_query, contact_params))
            
            # Execute all queries in a batch
            await self.db_pool.execute_batch_async(queries)
            self.service_stats["batch_operations"] += 1
            self.service_stats["database_queries"] += len(queries)
            
            # Create booking response
            booking_response = {
                "booking_id": booking_id,
                "status": "PENDING",
                "total_amount": booking_data.get("total_amount", 0),
                "currency": booking_data.get("currency", "INR"),
                "created_at": datetime.now(timezone.utc).isoformat(),
                "flight_details": flight_details,
                "travellers_count": len(travellers),
                "contact_email": contact_info.get("email")
            }
            
            # Cache the booking for quick retrieval
            cache_key = f"booking:{booking_id}"
            try:
                self.redis_client.set_cache(cache_key, booking_response, 3600)  # 1 hour
            except Exception as e:
                print(f"Failed to cache booking: {str(e)}")
            
            # Update performance stats
            execution_time = time.time() - start_time
            self._update_avg_creation_time(execution_time)
            
            booking_response.update({
                "creation_time_ms": round(execution_time * 1000, 2),
                "database_operations": len(queries),
                "optimized": True
            })
            
            return booking_response
            
        except Exception as e:
            print(f"Optimized booking creation failed: {str(e)}")
            raise
    
    async def get_booking_optimized(self, booking_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve booking with optimized caching and single query.
        
        Args:
            booking_id: Booking identifier
            user_id: User identifier for authorization
            
        Returns:
            Booking information or None if not found
        """
        start_time = time.time()
        self.service_stats["total_bookings_retrieved"] += 1
        
        try:
            # Check cache first
            cache_key = f"booking:{booking_id}"
            cached_booking = self.redis_client.get_cache(cache_key)
            
            if cached_booking:
                self.service_stats["cache_hits"] += 1
                
                # Add performance metadata
                cached_booking.update({
                    "retrieval_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "cache",
                    "cache_hit": True
                })
                
                return cached_booking
            
            # Cache miss - fetch from database with optimized query
            booking_query = """
                SELECT 
                    mb.booking_id, mb.user_id, mb.booking_status, mb.total_amount,
                    mb.currency, mb.created_at as booking_created_at, mb.updated_at,
                    fb.flight_id, fb.fare_id, fb.departure_date, fb.return_date,
                    fb.cabin_class, fb.fare_type, fb.pnr_number, fb.booking_reference,
                    bc.email, bc.phone, bc.address, bc.city, bc.country, bc.postal_code,
                    COUNT(bt.traveller_index) as travellers_count
                FROM master_bookings mb
                LEFT JOIN flight_bookings fb ON mb.booking_id = fb.booking_id
                LEFT JOIN booking_contacts bc ON mb.booking_id = bc.booking_id
                LEFT JOIN booking_travellers bt ON mb.booking_id = bt.booking_id
                WHERE mb.booking_id = %(booking_id)s
                AND (%(user_id)s IS NULL OR mb.user_id = %(user_id)s)
                GROUP BY mb.booking_id, fb.flight_id, bc.email
            """
            
            booking_params = {
                "booking_id": booking_id,
                "user_id": user_id
            }
            
            booking_result = await self.db_pool.execute_async_query(
                booking_query, 
                booking_params, 
                fetch_all=False
            )
            
            self.service_stats["database_queries"] += 1
            
            if not booking_result:
                return None
            
            # Fetch travellers separately for better performance
            travellers_query = """
                SELECT traveller_index, title, first_name, last_name, 
                       date_of_birth, gender, passport_number, nationality
                FROM booking_travellers
                WHERE booking_id = %(booking_id)s
                ORDER BY traveller_index
            """
            
            travellers_result = await self.db_pool.execute_async_query(
                travellers_query,
                {"booking_id": booking_id}
            )
            
            self.service_stats["database_queries"] += 1
            
            # Construct booking response
            booking_response = {
                "booking_id": booking_result["booking_id"],
                "user_id": booking_result["user_id"],
                "status": booking_result["booking_status"],
                "total_amount": float(booking_result["total_amount"]) if booking_result["total_amount"] else 0,
                "currency": booking_result["currency"],
                "created_at": booking_result["booking_created_at"].isoformat() if booking_result["booking_created_at"] else None,
                "updated_at": booking_result["updated_at"].isoformat() if booking_result["updated_at"] else None,
                "flight_details": {
                    "flight_id": booking_result["flight_id"],
                    "fare_id": booking_result["fare_id"],
                    "departure_date": booking_result["departure_date"].isoformat() if booking_result["departure_date"] else None,
                    "return_date": booking_result["return_date"].isoformat() if booking_result["return_date"] else None,
                    "cabin_class": booking_result["cabin_class"],
                    "fare_type": booking_result["fare_type"],
                    "pnr_number": booking_result["pnr_number"],
                    "booking_reference": booking_result["booking_reference"]
                },
                "contact_info": {
                    "email": booking_result["email"],
                    "phone": booking_result["phone"],
                    "address": booking_result["address"],
                    "city": booking_result["city"],
                    "country": booking_result["country"],
                    "postal_code": booking_result["postal_code"]
                },
                "travellers": [dict(traveller) for traveller in travellers_result] if travellers_result else [],
                "travellers_count": booking_result["travellers_count"] or 0
            }
            
            # Cache the result
            try:
                self.redis_client.set_cache(cache_key, booking_response, 3600)  # 1 hour
            except Exception as e:
                print(f"Failed to cache booking retrieval: {str(e)}")
            
            # Update performance stats
            execution_time = time.time() - start_time
            self._update_avg_retrieval_time(execution_time)
            
            booking_response.update({
                "retrieval_time_ms": round(execution_time * 1000, 2),
                "data_source": "database",
                "cache_hit": False,
                "database_queries": 2
            })
            
            return booking_response
            
        except Exception as e:
            print(f"Optimized booking retrieval failed: {str(e)}")
            raise
    
    async def get_user_bookings_optimized(
        self, 
        user_id: str, 
        limit: int = 20, 
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get user bookings with optimized pagination and caching.
        
        Args:
            user_id: User identifier
            limit: Maximum number of bookings to return
            offset: Number of bookings to skip
            
        Returns:
            Paginated booking list with metadata
        """
        start_time = time.time()
        
        try:
            # Check cache for user bookings
            cache_key = f"user_bookings:{user_id}:{limit}:{offset}"
            cached_bookings = self.redis_client.get_cache(cache_key)
            
            if cached_bookings:
                self.service_stats["cache_hits"] += 1
                cached_bookings.update({
                    "retrieval_time_ms": round((time.time() - start_time) * 1000, 2),
                    "data_source": "cache"
                })
                return cached_bookings
            
            # Optimized query with pagination
            bookings_query = """
                SELECT 
                    mb.booking_id, mb.booking_status, mb.total_amount, mb.currency,
                    mb.created_at, fb.departure_date, fb.cabin_class, fb.pnr_number,
                    COUNT(bt.traveller_index) as travellers_count
                FROM master_bookings mb
                LEFT JOIN flight_bookings fb ON mb.booking_id = fb.booking_id
                LEFT JOIN booking_travellers bt ON mb.booking_id = bt.booking_id
                WHERE mb.user_id = %(user_id)s
                GROUP BY mb.booking_id
                ORDER BY mb.created_at DESC
                LIMIT %(limit)s OFFSET %(offset)s
            """
            
            count_query = """
                SELECT COUNT(*) as total_count
                FROM master_bookings
                WHERE user_id = %(user_id)s
            """
            
            # Execute both queries
            bookings_params = {
                "user_id": user_id,
                "limit": limit,
                "offset": offset
            }
            
            bookings_result = await self.db_pool.execute_async_query(bookings_query, bookings_params)
            count_result = await self.db_pool.execute_async_query(count_query, {"user_id": user_id}, fetch_all=False)
            
            self.service_stats["database_queries"] += 2
            
            # Format response
            bookings_response = {
                "bookings": [
                    {
                        "booking_id": booking["booking_id"],
                        "status": booking["booking_status"],
                        "total_amount": float(booking["total_amount"]) if booking["total_amount"] else 0,
                        "currency": booking["currency"],
                        "created_at": booking["created_at"].isoformat() if booking["created_at"] else None,
                        "departure_date": booking["departure_date"].isoformat() if booking["departure_date"] else None,
                        "cabin_class": booking["cabin_class"],
                        "pnr_number": booking["pnr_number"],
                        "travellers_count": booking["travellers_count"] or 0
                    }
                    for booking in (bookings_result or [])
                ],
                "pagination": {
                    "total_count": count_result["total_count"] if count_result else 0,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (count_result["total_count"] if count_result else 0) > (offset + limit)
                },
                "user_id": user_id
            }
            
            # Cache the result for 5 minutes
            try:
                self.redis_client.set_cache(cache_key, bookings_response, 300)
            except Exception as e:
                print(f"Failed to cache user bookings: {str(e)}")
            
            execution_time = time.time() - start_time
            bookings_response.update({
                "retrieval_time_ms": round(execution_time * 1000, 2),
                "data_source": "database",
                "database_queries": 2
            })
            
            return bookings_response
            
        except Exception as e:
            print(f"Optimized user bookings retrieval failed: {str(e)}")
            raise
    
    def _generate_booking_id(self) -> str:
        """Generate unique booking ID."""
        import uuid
        return f"BK{int(time.time())}{str(uuid.uuid4())[:8].upper()}"
    
    def _update_avg_creation_time(self, execution_time: float):
        """Update average booking creation time."""
        current_avg = self.service_stats["avg_booking_creation_time"]
        total_bookings = self.service_stats["total_bookings_created"]
        
        if total_bookings == 1:
            self.service_stats["avg_booking_creation_time"] = execution_time
        else:
            self.service_stats["avg_booking_creation_time"] = (
                (current_avg * (total_bookings - 1) + execution_time) / total_bookings
            )
    
    def _update_avg_retrieval_time(self, execution_time: float):
        """Update average booking retrieval time."""
        current_avg = self.service_stats["avg_booking_retrieval_time"]
        total_retrievals = self.service_stats["total_bookings_retrieved"]
        
        if total_retrievals == 1:
            self.service_stats["avg_booking_retrieval_time"] = execution_time
        else:
            self.service_stats["avg_booking_retrieval_time"] = (
                (current_avg * (total_retrievals - 1) + execution_time) / total_retrievals
            )
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get booking service performance statistics."""
        total_operations = (
            self.service_stats["total_bookings_created"] + 
            self.service_stats["total_bookings_retrieved"]
        )
        
        cache_hit_rate = (
            self.service_stats["cache_hits"] / total_operations 
            if total_operations > 0 else 0
        )
        
        return {
            **self.service_stats,
            "cache_hit_rate": cache_hit_rate,
            "total_operations": total_operations,
            "database_pool_stats": self.db_pool.get_performance_stats()
        }


# Global optimized booking service instance
optimized_booking_service = OptimizedBookingService()
