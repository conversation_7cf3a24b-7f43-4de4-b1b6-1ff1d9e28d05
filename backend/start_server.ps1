# Fast Travel Backend - Local Network Server PowerShell Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Fast Travel Backend - Local Network Server" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and try again" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check if virtual environment exists
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "🔧 Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
} else {
    Write-Host "⚠️  No virtual environment found" -ForegroundColor Yellow
    Write-Host "Using system Python installation" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Starting server for local network access..." -ForegroundColor Green
Write-Host ""
Write-Host "📱 The server will be accessible from other devices on your network" -ForegroundColor Cyan
Write-Host "🔒 Make sure Windows Firewall allows Python through port 8000" -ForegroundColor Yellow
Write-Host ""

# Function to check and configure Windows Firewall
function Test-FirewallRule {
    try {
        $rule = Get-NetFirewallRule -DisplayName "Python*" -ErrorAction SilentlyContinue
        if ($rule) {
            Write-Host "✅ Python firewall rule found" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️  No Python firewall rule found" -ForegroundColor Yellow
            return $false
        }
    } catch {
        Write-Host "⚠️  Could not check firewall rules (requires admin)" -ForegroundColor Yellow
        return $false
    }
}

# Check firewall
Write-Host "🔍 Checking Windows Firewall..." -ForegroundColor Cyan
$firewallOk = Test-FirewallRule

if (-not $firewallOk) {
    Write-Host ""
    Write-Host "🔒 FIREWALL CONFIGURATION NEEDED" -ForegroundColor Red
    Write-Host "To allow network access, you need to:" -ForegroundColor Yellow
    Write-Host "1. Open Windows Defender Firewall" -ForegroundColor White
    Write-Host "2. Click 'Allow an app or feature through Windows Defender Firewall'" -ForegroundColor White
    Write-Host "3. Click 'Change Settings' then 'Allow another app'" -ForegroundColor White
    Write-Host "4. Browse and select Python.exe" -ForegroundColor White
    Write-Host "5. Check both 'Private' and 'Public' networks" -ForegroundColor White
    Write-Host ""
    
    $response = Read-Host "Continue anyway? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "Exiting..." -ForegroundColor Yellow
        exit 0
    }
}

Write-Host ""
Write-Host "🌐 Starting FastAPI server..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Start the server
try {
    python run_server_local_network.py
} catch {
    Write-Host ""
    Write-Host "❌ Failed to start server: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "• Check if port 8000 is already in use" -ForegroundColor White
    Write-Host "• Verify all dependencies are installed: pip install -r requirements.txt" -ForegroundColor White
    Write-Host "• Check if Redis and MySQL services are running" -ForegroundColor White
}

Write-Host ""
Write-Host "👋 Server stopped" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
