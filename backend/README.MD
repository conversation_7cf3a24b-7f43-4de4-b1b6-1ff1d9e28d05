# SET UP:
1. CREATE DATABASES FOR AUTH,FLIGHT,BOOKING 
2. INSTALL REQUIREMENTS 

---

alembic revision --autogenerate -m "Add users table"


Migrations 
###
First Create = alembic.ini 
Go to migrations 

auth service
=======
create migrations folder = alembic init migrations
alembic -c alembic.ini revision --autogenerate -m "Create users table"
alembic upgrade head


NEW#
alembic -c app/microservices/auth_service/alembic.ini revision --autogenerate -m "Add NewModel table"
alembic -c alembic.ini upgrade head 


##
New Command:
alembic -c app/microservices/auth_service/alembic.ini revision --autogenerate -m "Add NewModel as Test table"
alembic -c app/microservices/auth_service/alembic.ini upgrade head

## Migrations Command 
1. Initailize Migrations in the service folder 
*   alembic init folderpath/migrations
    exampl:: alembic init app/microservices/flight_service/shared_service/migrations
1. alembic -c app/microservices/auth_service/alembic.ini revision --autogenerate -m "Add NewModel as Test table" -> Create migration file 
2. alembic -c app/microservices/auth_service/alembic.ini upgrade head -> Apply migration 


# Databases;
1. tdb_auth
2. tdb_flight_shared