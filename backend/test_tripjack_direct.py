import requests
import json
from datetime import datetime, timedelta

def test_tripjack_api():
    """Test TripJack API directly with correct parameters"""
    
    url = 'https://apitest.tripjack.com/fms/v1/air-search-all'
    headers = {
        'apikey': '6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9',
        'Content-Type': 'application/json'
    }

    # Use a future date (30 days from now)
    future_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    payload = {
        'searchQuery': {
            'cabinClass': 'ECONOMY',
            'paxInfo': {
                'ADULT': '1',
                'CHILD': '0',
                'INFANT': '0'
            },
            'routeInfos': [
                {
                    'fromCityOrAirport': {'code': 'DEL'},
                    'toCityOrAirport': {'code': 'BOM'},
                    'travelDate': future_date
                }
            ],
            'searchModifiers': {},
            'preferredAirline': []
        }
    }

    print('Testing TripJack API with future date...')
    print(f'Travel Date: {future_date}')
    print(f'URL: {url}')
    print(f'Payload: {json.dumps(payload, indent=2)}')
    print('-' * 50)

    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f'Status Code: {response.status_code}')
        print(f'Response Headers: {dict(response.headers)}')
        
        if response.status_code == 200:
            print('✅ SUCCESS! TripJack API is working')
            result = response.json()
            print(f'Response keys: {list(result.keys())}')
            if 'searchResult' in result:
                search_result = result['searchResult']
                print(f'Search result keys: {list(search_result.keys())}')
                if 'tripInfos' in search_result:
                    print(f'Trip infos available: {len(search_result["tripInfos"])} trips found')
        else:
            print(f'❌ ERROR Response Body: {response.text}')
            
    except requests.exceptions.Timeout:
        print('❌ Request timed out')
    except requests.exceptions.RequestException as e:
        print(f'❌ Request error: {e}')
    except Exception as e:
        print(f'❌ Unexpected error: {e}')

if __name__ == "__main__":
    test_tripjack_api()
