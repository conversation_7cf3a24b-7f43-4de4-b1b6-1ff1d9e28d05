# TripJack Payment Integration and Booking Management

This document describes the implementation of TripJack payment integration and booking management features in the Go backend.

## Overview

The TripJack payment integration implements the Hold & Confirm booking flow:
1. **Review** → Get booking details and pricing
2. **Hold Booking** → Reserve the booking with TripJack
3. **Payment Processing** → Handle payment through various gateways
4. **Confirm Booking** → Confirm the held booking after successful payment
5. **Unhold Booking** → Release the booking if payment fails

## Architecture

### Services

#### FlightService Enhancements
- `ConfirmBooking(ctx, bookingID)` - Calls TripJack `/oms/v1/air/confirm-book` API
- `UnholdBooking(ctx, bookingID)` - Calls TripJack `/oms/v1/air/unhold` API  
- `GetBookingDetails(ctx, bookingID)` - Calls TripJack `/oms/v1/booking-details` API

#### PaymentService Enhancements
- `confirmTripJackBooking(ctx, bookingReference)` - Confirms booking after successful payment
- `unholdTripJackBooking(ctx, bookingReference)` - Releases booking after failed payment
- Enhanced payment callbacks to trigger TripJack booking confirmation/unhold

#### BookingService Integration
- Maintains reference to FlightService for TripJack API calls
- Stores TripJack booking ID and PNR in database
- Updates booking status based on TripJack operations

### API Endpoints

#### New Booking Management Endpoints

```
POST /apis/booking/{reference}/confirm
```
- Confirms a held booking with TripJack after payment
- Requires authentication
- Updates local booking status to confirmed
- Returns ticket numbers if available

```
POST /apis/booking/{reference}/unhold
```
- Releases a held booking with TripJack
- Requires authentication  
- Updates local booking status to cancelled
- Used for payment failures or manual cancellations

```
GET /apis/booking/{reference}/tripjack-details
```
- Retrieves detailed booking information from TripJack
- Requires authentication
- Returns both TripJack and local booking details
- Useful for debugging and customer support

### Payment Flow Integration

#### Successful Payment Flow
1. Payment gateway callback received
2. Payment status updated to completed
3. `confirmTripJackBooking()` called automatically
4. TripJack confirm booking API called
5. Local booking status updated to confirmed
6. Ticket numbers stored if provided

#### Failed Payment Flow
1. Payment gateway callback received with failure status
2. Payment status updated to failed
3. `unholdTripJackBooking()` called automatically
4. TripJack unhold booking API called
5. Local booking status updated to cancelled
6. Booking released from TripJack system

## Data Models

### TripJackConfirmResponse
```go
type TripJackConfirmResponse struct {
    BookingID      string   `json:"bookingId"`
    Status         string   `json:"status"`
    TicketNumbers  []string `json:"ticketNumbers,omitempty"`
    ResponseTimeMS float64  `json:"response_time_ms"`
    RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}
```

### ReleasePNRResponse
```go
type ReleasePNRResponse struct {
    BookingID      string   `json:"bookingId"`
    Status         string   `json:"status"`
    Message        string   `json:"message"`
    ResponseTimeMS float64  `json:"response_time_ms"`
    RawResponse    map[string]interface{} `json:"raw_response,omitempty"`
}
```

### BookingDetailsResponse
```go
type BookingDetailsResponse struct {
    BookingID      string   `json:"bookingId"`
    Status         string   `json:"status"`
    BookingDetails map[string]interface{} `json:"bookingDetails"`
    ResponseTimeMS float64  `json:"response_time_ms"`
}
```

## Configuration

### TripJack API Configuration
```go
type TripJackConfig struct {
    BaseURL string // https://apitest.tripjack.com/ for UAT
    APIKey  string // TripJack API key
}
```

### Environment Variables
```
TRIPJACK_BASE_URL=https://apitest.tripjack.com/
TRIPJACK_API_KEY=your_tripjack_api_key
```

## Error Handling

### TripJack API Errors
- Circuit breaker pattern implemented for resilience
- Comprehensive logging for debugging
- Graceful degradation when TripJack APIs fail
- Retry mechanisms for critical operations

### Payment Integration Errors
- Payment callbacks continue to work even if TripJack operations fail
- Warning headers added to responses when local updates fail
- Separate error handling for payment vs booking operations

## Testing

### Unit Tests
- Comprehensive test suite in `tripjack_payment_integration_test.go`
- Tests for all booking management endpoints
- Payment callback integration tests
- Error scenario testing

### Test Coverage
- Successful booking confirmation flow
- Successful booking unhold flow
- TripJack booking details retrieval
- Payment callback triggering booking operations
- Error handling for missing provider booking IDs
- Authentication and authorization testing

## Security Considerations

### Authentication
- All booking management endpoints require authentication
- JWT token validation for user access
- Booking ownership verification

### API Security
- TripJack API key securely stored in environment variables
- Request/response logging for audit trails
- Rate limiting through circuit breaker pattern

## Monitoring and Logging

### Logging
- Comprehensive logging for all TripJack API calls
- Request/response logging with truncation for large payloads
- Performance metrics (response times) tracked
- Error logging with context for debugging

### Monitoring
- Circuit breaker metrics for TripJack API health
- Payment callback success/failure rates
- Booking confirmation/unhold success rates
- Response time monitoring for performance optimization

## Deployment Considerations

### Environment Setup
- UAT environment uses `https://apitest.tripjack.com/`
- Production environment uses `https://api.tripjack.com/`
- API keys must be configured per environment

### Database Migrations
- No new database schema changes required
- Existing booking tables support TripJack integration
- `provider_booking_id` field stores TripJack booking ID
- `pnr` field stores TripJack PNR

## Integration with Frontend

### API Compatibility
- Endpoints follow existing API patterns
- Response formats match frontend expectations
- Error responses use standard error format

### Frontend Integration Points
- Payment success page can call confirm booking endpoint
- Booking management page can show TripJack details
- Error handling for TripJack operation failures

## Troubleshooting

### Common Issues
1. **Missing Provider Booking ID**: Ensure booking was created through TripJack hold booking flow
2. **TripJack API Failures**: Check API key configuration and network connectivity
3. **Payment Callback Issues**: Verify payment gateway webhook configuration
4. **Authentication Errors**: Ensure valid JWT token is provided

### Debug Information
- Check application logs for TripJack API request/response details
- Verify booking status in database matches TripJack status
- Use TripJack booking details endpoint for status verification

## Future Enhancements

### Planned Features
- Automatic retry mechanisms for failed TripJack operations
- Webhook integration for TripJack status updates
- Enhanced error recovery and reconciliation
- Performance optimizations for high-volume operations

### Monitoring Improvements
- Dashboard for TripJack integration health
- Alerting for high failure rates
- Performance analytics and optimization recommendations
