# Server Configuration
PORT=8080
GIN_MODE=debug

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_USER=root
DATABASE_PASSWORD=Vishnu123

# Database Names
AUTH_DATABASE_NAME=tdb_auth
FLIGHT_DATABASE_NAME=tdb_flight
BOOKING_DATABASE_NAME=tdb_booking

# Redis Configuration
REDIS_URL=redis://redis:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_secure
JWT_EXPIRATION_HOURS=24
JWT_ISSUER=fast-travel-backend

# TripJack API Configuration
TRIPJACK_BASE_URL=https://api.tripjack.com/
TRIPJACK_API_KEY=6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9
TRIPJACK_USERNAME=your_tripjack_username
TRIPJ<PERSON>K_PASSWORD=your_tripjack_password

# Cache Configuration
MEMORY_CACHE_TTL_MINUTES=15
REDIS_CACHE_TTL_HOURS=1
CACHE_CLEANUP_INTERVAL_MINUTES=5

# External API Configuration
EXTERNAL_API_TIMEOUT_SECONDS=30
MAX_CONCURRENT_REQUESTS=100
CIRCUIT_BREAKER_THRESHOLD=5

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Performance Configuration
MAX_IDLE_CONNECTIONS=10
MAX_OPEN_CONNECTIONS=100
CONNECTION_MAX_LIFETIME_HOURS=1
CONNECTION_MAX_IDLE_TIME_MINUTES=30

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# CORS Configuration
CORS_ALLOWED_ORIGINS=*
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Origin,Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30

# Background Job Configuration
BACKGROUND_JOB_WORKERS=5
BACKGROUND_JOB_QUEUE_SIZE=1000

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
TRACING_ENABLED=false

# Email Configuration (for OTP and notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

# SMS Configuration (for OTP)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf
UPLOAD_PATH=./uploads

# Security Configuration
BCRYPT_COST=12
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15

# Development Configuration
DEBUG_MODE=true
ENABLE_PROFILING=false
ENABLE_SWAGGER=true

# Testing Configuration
TESTING_MODE=false
ENABLE_UNIVERSAL_OTP=false
UNIVERSAL_OTP=123456
