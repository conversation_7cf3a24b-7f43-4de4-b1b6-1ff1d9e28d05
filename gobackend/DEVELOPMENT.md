# Fast Travel Backend Go - Development Guide

## 🚀 Quick Start

### Prerequisites
- <PERSON><PERSON> and Docker Compose
- Go 1.21+ (optional for local development)
- Make (for build automation)

### One-Command Setup
```bash
# Clone and setup complete development environment
git clone <repository-url>
cd gobackend
make dev-setup
```

This will:
- Install development tools
- Copy development environment configuration
- Build Docker containers
- Start all services with seed data
- Display service URLs

## 🛠 Development Environment

### Environment Configuration

The development environment uses `.env.development` which includes:

- **Database**: MySQL 8.0 with development optimizations
- **Cache**: Redis 7 with development-friendly settings
- **API**: TripJack test environment
- **Monitoring**: Optional Prometheus + Grafana
- **Tools**: php<PERSON>y<PERSON><PERSON><PERSON>, <PERSON>is Commander, Mailhog

### Services Overview

| Service | Port | Description | Access |
|---------|------|-------------|---------|
| **Go Backend** | 8080 | Main API server | http://localhost:8080 |
| **MySQL** | 3306 | Database server | Direct connection |
| **Redis** | 6379 | Cache server | Direct connection |
| **phpMyAdmin** | 8082 | Database management | http://localhost:8082 |
| **Redis Commander** | 8081 | Redis management | http://localhost:8081 |
| **Mailhog** | 8025 | Email testing | http://localhost:8025 |
| **Prometheus** | 9090 | Metrics collection | http://localhost:9090 |
| **Grafana** | 3000 | Monitoring dashboard | http://localhost:3000 |

## 📋 Development Commands

### Environment Management
```bash
# Start development environment
make dev-up

# Stop development environment
make dev-down

# Restart development environment
make dev-restart

# View logs
make dev-logs

# Check status
make dev-status

# Access container shell
make dev-shell
```

### Development Tools
```bash
# Start development tools (phpMyAdmin, Redis Commander)
make dev-tools-up

# Start monitoring stack (Prometheus, Grafana)
make dev-monitoring-up

# Stop tools
make dev-tools-down
make dev-monitoring-down
```

### Database Management
```bash
# Reset database with fresh seed data
make dev-db-reset

# Clear Redis cache
make dev-cache-clear

# Reset entire environment
make dev-reset
```

### Code Development
```bash
# Run tests
make test

# Run with coverage
make test-coverage

# Lint code
make lint

# Format code
make format

# Security scan
make security-scan
```

## 🗄 Database Setup

### Development Databases

The development environment creates three databases matching the Python backend:

- **tdb_auth**: User authentication and management
- **tdb_flight**: Flight search and details
- **tdb_booking**: Booking and payment data

### Seed Data

Development seed data includes:
- 5 test users (admin and customers)
- 5 sample bookings with different statuses
- Contact information and traveller details
- Payment records with various states

### Database Access

**Via phpMyAdmin**: http://localhost:8082
- Server: mysql-dev
- Username: root
- Password: Vishnu123

**Direct Connection**:
```bash
mysql -h localhost -P 3306 -u root -pVishnu123
```

**Container Access**:
```bash
docker-compose -f docker-compose.dev.yml exec mysql-dev mysql -u root -pVishnu123
```

## 🔧 Configuration

### Environment Variables

Key development settings in `.env.development`:

```bash
# Server
PORT=8080
GIN_MODE=debug
DEBUG_MODE=true

# Database
DATABASE_HOST=localhost
DATABASE_PASSWORD=Vishnu123

# TripJack (Test Environment)
TRIPJACK_BASE_URL=https://apitest.tripjack.com/
TRIPJACK_API_KEY=6124735e5cf0cd-d1de-4581-a6f4-a86799dba1d9

# Cache (Development Optimized)
MEMORY_CACHE_TTL_MINUTES=15
FLIGHT_SEARCH_CACHE_TIMER=300
```

### Hot Reload

The development environment includes Air for hot reload:
- Automatically rebuilds on code changes
- Excludes test files and temporary directories
- Logs build errors to `build-errors.log`

### Debugging

Delve debugger is available on port 2345:
```bash
# Connect with your IDE or dlv
dlv connect localhost:2345
```

## 🧪 Testing

### Test Data

Use the provided seed data for testing:

**Test Users**:
- <EMAIL> (Admin)
- <EMAIL> (Customer)
- <EMAIL> (Customer)

**Test Bookings**:
- BOOK_DEV_001 (Confirmed, Paid)
- BOOK_DEV_002 (Pending, Unpaid)
- BOOK_DEV_003 (Confirmed, Paid)

### API Testing

**Health Check**:
```bash
curl http://localhost:8080/health
```

**User Registration**:
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "phone_number": "**********",
    "phone_country_code": "+91",
    "role": "customer"
  }'
```

**Flight Search**:
```bash
curl -X POST http://localhost:8080/api/flight/search \
  -H "Content-Type: application/json" \
  -d '{
    "trips": [{"from": "DEL", "to": "BOM", "onward_date": "2024-12-25"}],
    "adt": 1,
    "chd": 0,
    "inf": 0,
    "cabin": "E"
  }'
```

## 🔍 Monitoring

### Application Metrics

**Prometheus**: http://localhost:9090
- Application metrics
- Database connection stats
- Cache hit ratios
- Response times

**Grafana**: http://localhost:3000
- Username: admin
- Password: admin
- Pre-configured dashboards for Go applications

### Logs

**Application Logs**:
```bash
# Follow application logs
make dev-logs

# View specific service logs
docker-compose -f docker-compose.dev.yml logs mysql-dev
docker-compose -f docker-compose.dev.yml logs redis-dev
```

**Database Logs**:
- General log: `/var/log/mysql/general.log`
- Error log: `/var/log/mysql/error.log`
- Slow query log: `/var/log/mysql/slow.log`

## 🐛 Troubleshooting

### Common Issues

**Port Conflicts**:
```bash
# Check what's using ports
lsof -i :8080
lsof -i :3306
lsof -i :6379

# Stop conflicting services
sudo service mysql stop
sudo service redis-server stop
```

**Database Connection Issues**:
```bash
# Check MySQL status
make dev-status

# Reset database
make dev-db-reset

# View MySQL logs
docker-compose -f docker-compose.dev.yml logs mysql-dev
```

**Cache Issues**:
```bash
# Clear Redis cache
make dev-cache-clear

# Check Redis status
docker-compose -f docker-compose.dev.yml exec redis-dev redis-cli ping
```

**Container Issues**:
```bash
# Rebuild containers
make dev-build

# Reset environment
make dev-reset

# Check container logs
docker-compose -f docker-compose.dev.yml logs
```

### Performance Issues

**Slow Database Queries**:
- Check slow query log in phpMyAdmin
- Monitor query performance in Prometheus
- Use `EXPLAIN` for query optimization

**Cache Misses**:
- Monitor cache hit ratios in Grafana
- Check Redis memory usage
- Verify cache TTL settings

## 🔄 Migration from Python

### API Compatibility

The Go implementation maintains 100% API compatibility with the Python backend:
- Same endpoint URLs and methods
- Identical request/response formats
- Compatible authentication tokens
- Same database schema

### Environment Mapping

| Python Setting | Go Equivalent | Notes |
|----------------|---------------|-------|
| `DATABASE_URL` | `DATABASE_HOST` + `DATABASE_PORT` | Split into components |
| `REDIS_URL` | `REDIS_URL` | Direct mapping |
| `SECRET_KEY` | `JWT_SECRET_KEY` | Same purpose |
| `TRIPJACK_*` | `TRIPJACK_*` | Direct mapping |

### Data Migration

The development environment uses the same database schema as Python:
- User tables are identical
- Booking structure matches
- Payment records compatible
- Cache keys use same format

## 📚 Additional Resources

### Documentation
- [API Documentation](./api/README.md)
- [Database Schema](./database/README.md)
- [Deployment Guide](./DEPLOYMENT_GUIDE.md)

### Development Tools
- [Air (Hot Reload)](https://github.com/cosmtrek/air)
- [Delve (Debugger)](https://github.com/go-delve/delve)
- [golangci-lint](https://golangci-lint.run/)

### Monitoring
- [Prometheus Queries](./monitoring/prometheus-queries.md)
- [Grafana Dashboards](./monitoring/grafana/)

## 🤝 Contributing

1. Create feature branch from `develop`
2. Make changes with tests
3. Run `make test-all` to verify
4. Submit pull request
5. Ensure CI passes

### Code Standards
- Follow Go conventions
- Add tests for new features
- Update documentation
- Use meaningful commit messages
