# Development Dockerfile for Fast Travel Backend Go
# Optimized for development with hot reload and debugging capabilities

FROM golang:1.23-alpine AS development

# Install development tools and dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    curl \
    make \
    gcc \
    musl-dev \
    binutils \
    binutils-gold \
    sqlite \
    tzdata

# Set timezone
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Install development tools
RUN go install github.com/air-verse/air@latest && \
    go install github.com/go-delve/delve/cmd/dlv@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest && \
    go install github.com/swaggo/swag/cmd/swag@latest

# Create app directory
WORKDIR /app

# Create non-root user for development
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download && go mod verify

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p uploads logs tmp && \
    chown -R appuser:appgroup /app

# Create air configuration for hot reload
RUN echo 'root = "."' > .air.toml && \
    echo 'testdata_dir = "testdata"' >> .air.toml && \
    echo 'tmp_dir = "tmp"' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[build]' >> .air.toml && \
    echo '  args_bin = []' >> .air.toml && \
    echo '  bin = "./tmp/main"' >> .air.toml && \
    echo '  cmd = "go build -o ./tmp/main cmd/server/main.go"' >> .air.toml && \
    echo '  delay = 1000' >> .air.toml && \
    echo '  exclude_dir = ["assets", "tmp", "vendor", "testdata", "uploads", "logs"]' >> .air.toml && \
    echo '  exclude_file = []' >> .air.toml && \
    echo '  exclude_regex = ["_test.go"]' >> .air.toml && \
    echo '  exclude_unchanged = false' >> .air.toml && \
    echo '  follow_symlink = false' >> .air.toml && \
    echo '  full_bin = ""' >> .air.toml && \
    echo '  include_dir = []' >> .air.toml && \
    echo '  include_ext = ["go", "tpl", "tmpl", "html"]' >> .air.toml && \
    echo '  kill_delay = "0s"' >> .air.toml && \
    echo '  log = "build-errors.log"' >> .air.toml && \
    echo '  send_interrupt = false' >> .air.toml && \
    echo '  stop_on_root = false' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[color]' >> .air.toml && \
    echo '  app = ""' >> .air.toml && \
    echo '  build = "yellow"' >> .air.toml && \
    echo '  main = "magenta"' >> .air.toml && \
    echo '  runner = "green"' >> .air.toml && \
    echo '  watcher = "cyan"' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[log]' >> .air.toml && \
    echo '  time = false' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[misc]' >> .air.toml && \
    echo '  clean_on_exit = false' >> .air.toml && \
    echo '' >> .air.toml && \
    echo '[screen]' >> .air.toml && \
    echo '  clear_on_rebuild = false' >> .air.toml

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8080 2345

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Default command for development
CMD ["air", "-c", ".air.toml"]
