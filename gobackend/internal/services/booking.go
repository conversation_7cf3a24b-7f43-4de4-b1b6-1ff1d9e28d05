package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"gorm.io/gorm"

	"gobackend/internal/database"
	"gobackend/internal/models"
	"gobackend/pkg/cache"
)

// BookingService handles booking-related business logic
type BookingService struct {
	db            *gorm.DB
	cacheService  *cache.MultiLayerCache
	flightService *FlightService
}

// NewBookingService creates a new booking service
func NewBookingService(cacheService *cache.MultiLayerCache, flightService *FlightService) *BookingService {
	return &BookingService{
		db:            database.DB.BookingDB,
		cacheService:  cacheService,
		flightService: flightService,
	}
}

// NewBookingServiceWithDB creates a new booking service with provided database
func NewBookingServiceWithDB(db *gorm.DB, cacheService *cache.MultiLayerCache, flightService *FlightService) *BookingService {
	return &BookingService{
		db:            db,
		cacheService:  cacheService,
		flightService: flightService,
	}
}

// CreateBooking creates a new flight booking
func (s *BookingService) CreateBooking(ctx context.Context, req models.BookingRequest, userID uint) (*models.BookingResponse, error) {
	// Handle test mode (when db is nil)
	if s.db == nil {
		return s.createMockBooking(ctx, req, userID)
	}

	// Start database transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Extract pricing information from the new structure
	tui := req.TUI

	// If using new structure, extract from flight_booking
	if req.FlightBooking.TUI != "" {
		tui = req.FlightBooking.TUI
	}

	// Convert to new pricing request structure
	var pricingTrips []models.PricingTrip

	// Extract price IDs from flight booking trips directly (like Python backend)
	if req.FlightBooking.TUI != "" && len(req.FlightBooking.Trips) > 0 {
		for i, tripReq := range req.FlightBooking.Trips {
			for j, journeyReq := range tripReq.Journey {
				// Extract price ID from journey segments (like Python backend)
				priceID := s.extractPriceIDFromJourney(journeyReq)
				if priceID == "" {
					// If no price ID found in segments, this is likely a test booking
					// Use a placeholder that will fail gracefully
					log.Printf("Warning: No price ID found in journey %d of trip %d, using placeholder", j, i)
					priceID = "test-price-id-placeholder"
				}

				pricingTrip := models.PricingTrip{
					Amount:  journeyReq.GrossFare,
					Index:   priceID,
					OrderID: (i * 10) + j + 1, // Unique order ID
					TUI:     tui,
				}
				pricingTrips = append(pricingTrips, pricingTrip)
			}
		}
	} else {
		// Fallback for legacy format or missing data
		return nil, fmt.Errorf("no valid trip data found for pricing validation - flight_booking.Trips is required")
	}

	// Extract price IDs directly from the booking request (they should be in Journey.Index)
	var priceIDs []string
	for _, trip := range req.FlightBooking.Trips {
		for _, journey := range trip.Journey {
			if journey.Index != "" {
				// Clean the index to get the core price ID (remove |0|TJ suffix)
				cleanIndex := s.cleanFlightIndex(journey.Index)
				if cleanIndex != "" {
					priceIDs = append(priceIDs, cleanIndex)
					log.Printf("Extracted price ID from Journey.Index: %s -> %s", journey.Index, cleanIndex)
				}
			}
		}
	}

	// If we only have one price ID but the booking request suggests multiple segments,
	// we need to use just the single price ID (this is common for single-segment flights)
	if len(priceIDs) == 1 {
		log.Printf("Using single price ID for booking: %s", priceIDs[0])
	}

	// If no price IDs found in Journey.Index, try to get from cached search results
	if len(priceIDs) == 0 {
		log.Printf("No price IDs found in Journey.Index, trying to get from cached search results using TUI: %s", tui)
		cachedPriceIDs, err := s.getValidPriceIDsFromTUI(ctx, tui)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to get valid price IDs: %w", err)
		}

		// For booking, we should only use the FIRST price ID (the selected flight)
		// not all price IDs from the search results
		if len(cachedPriceIDs) > 0 {
			priceIDs = []string{cachedPriceIDs[0]} // Use only the first price ID
			log.Printf("Using first price ID from cached search results: %s", priceIDs[0])
		}
	}

	if len(priceIDs) == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("no valid price IDs found - please perform a fresh flight search or include Journey.Index in the request")
	}

	log.Printf("Using price IDs for TripJack Review API: %v", priceIDs)

	// Step 1: Call TripJack Review API to get booking ID (correct TripJack flow)
	reviewResult, err := s.flightService.ReviewFlight(ctx, priceIDs)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to review flight: %w", err)
	}

	// Extract pricing details from review result
	pricingResult := &models.PricingResponse{
		PricingDetails: models.FlightPricingDetails{
			BaseFare:  reviewResult.TotalPriceInfo.TotalFareDetail.FC.BF,
			TotalFare: reviewResult.TotalPriceInfo.TotalFareDetail.FC.TF,
			Currency:  "INR",
		},
	}

	// Generate unique booking reference
	bookingReference := s.generateBookingReference()

	// Create master booking
	masterBooking := models.MasterBooking{
		BookingReference: bookingReference,
		ServiceType:      models.ServiceTypeFlight,
		Status:           models.BookingStatusPending,
		PaymentStatus:    "unpaid",
		UserID:           userID,
	}

	if err := tx.Create(&masterBooking).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create master booking: %w", err)
	}

	// Create flight booking
	// Extract first price ID for provider info
	firstPriceID := "unknown"
	if len(pricingTrips) > 0 {
		firstPriceID = pricingTrips[0].Index
	}

	providerInfo, _ := json.Marshal(map[string]interface{}{
		"provider": "tripjack",
		"fare_id":  firstPriceID,
		"tui":      tui,
	})

	// Use values from new structure if available, otherwise use legacy values
	netAmount := pricingResult.PricingDetails.BaseFare
	grossAmount := pricingResult.PricingDetails.TotalFare
	airlineNetFare := pricingResult.PricingDetails.BaseFare
	ssrAmount := 0.0
	hold := false
	actualHoldTime := 0
	actualDisplayTime := 30

	if req.FlightBooking.TUI != "" {
		netAmount = req.FlightBooking.NetAmount
		grossAmount = req.FlightBooking.GrossAmount
		airlineNetFare = req.FlightBooking.AirlineNetFare
		ssrAmount = req.FlightBooking.SSRAmount
		hold = req.FlightBooking.Hold
		actualHoldTime = req.FlightBooking.ActualHoldTime
		actualDisplayTime = req.FlightBooking.ActualDisplayTime
	}

	// Calculate passenger counts from travellers
	adtCount := len(s.filterTravellersByType(req.Travellers, "ADT"))
	chdCount := len(s.filterTravellersByType(req.Travellers, "CHD"))
	infCount := len(s.filterTravellersByType(req.Travellers, "INF"))

	flightBooking := models.FlightBooking{
		MasterBookingID:   fmt.Sprintf("%d", masterBooking.ID),
		ProviderInfo:      providerInfo,
		TUI:               tui,
		Mode:              "Online",
		TransactionID:     s.generateTransactionID(),
		ADT:               adtCount,
		CHD:               chdCount,
		INF:               infCount,
		NetAmount:         netAmount,
		AirlineNetFare:    airlineNetFare,
		SSRAmount:         ssrAmount,
		GrossAmount:       grossAmount,
		Hold:              hold,
		ActualHoldTime:    actualHoldTime,
		ActualDisplayTime: actualDisplayTime,
	}

	if err := tx.Create(&flightBooking).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create flight booking: %w", err)
	}

	// Create contact information - handle both new and legacy structures
	var contactInfoReq models.ContactInfoRequest
	if req.ContactInfo.Email != "" {
		contactInfoReq = req.ContactInfo
	} else if len(req.Travellers) > 0 && req.Travellers[0].ContactInfo != nil {
		contactInfoReq = *req.Travellers[0].ContactInfo
	} else {
		// Default contact info if none provided
		contactInfoReq = models.ContactInfoRequest{
			Email:       "<EMAIL>",
			PhoneNumber: "0000000000",
			FirstName:   "Default",
			LastName:    "User",
		}
	}

	// Use the appropriate country code field
	countryCode := contactInfoReq.CountryCode
	if countryCode == "" {
		countryCode = contactInfoReq.PhnCountryCode
	}

	contactInfo := models.ContactInfo{
		FlightBookingID: fmt.Sprintf("%d", flightBooking.ID),
		Email:           contactInfoReq.Email,
		PhoneNumber:     contactInfoReq.PhoneNumber,
		CountryCode:     countryCode,
		Title:           contactInfoReq.Title,
		FirstName:       contactInfoReq.FirstName,
		LastName:        contactInfoReq.LastName,
		Address:         contactInfoReq.Address,
		City:            contactInfoReq.City,
		Country:         contactInfoReq.Country,
		PostalCode:      contactInfoReq.PostalCode,
	}

	if err := tx.Create(&contactInfo).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create contact info: %w", err)
	}

	// Create travellers - handle both new and legacy field names
	var travellers []models.Traveller
	for _, travellerReq := range req.Travellers {
		// Use new field names if available, otherwise use legacy field names
		title := travellerReq.Title
		firstName := travellerReq.FName
		lastName := travellerReq.LName
		gender := travellerReq.Gender
		dobStr := travellerReq.DOB
		passportNumber := travellerReq.PassportNo
		passportExpiryStr := travellerReq.PDOE
		nationality := travellerReq.Nationality
		passengerType := travellerReq.PTC

		// Fallback to legacy field names if new ones are empty
		if firstName == "" {
			firstName = travellerReq.FirstName
		}
		if lastName == "" {
			lastName = travellerReq.LastName
		}
		if dobStr == "" {
			dobStr = travellerReq.DateOfBirth
		}
		if passportNumber == "" {
			passportNumber = travellerReq.PassportNumber
		}
		if passportExpiryStr == "" {
			passportExpiryStr = travellerReq.PassportExpiry
		}
		if passengerType == "" {
			passengerType = travellerReq.PassengerType
		}

		dateOfBirth, err := time.Parse("2006-01-02", dobStr)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("invalid date of birth format: %w", err)
		}

		var passportExpiry *time.Time
		if passportExpiryStr != "" {
			expiry, err := time.Parse("2006-01-02", passportExpiryStr)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("invalid passport expiry format: %w", err)
			}
			passportExpiry = &expiry
		}

		traveller := models.Traveller{
			FlightBookingID: fmt.Sprintf("%d", flightBooking.ID),
			Title:           title,
			FirstName:       firstName,
			LastName:        lastName,
			Gender:          gender,
			DateOfBirth:     dateOfBirth,
			PassportNumber:  passportNumber,
			PassportExpiry:  passportExpiry,
			Nationality:     nationality,
			PassengerType:   passengerType,
		}

		travellers = append(travellers, traveller)
	}

	if err := tx.Create(&travellers).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create travellers: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Create a proper booking request with the extracted price IDs and correct amount from Review API
	bookingReq := models.BookingRequest{
		FareID:      priceIDs[0], // Use the first price ID as the main fare ID
		TUI:         tui,
		Trips:       []models.TripInfo{}, // Will be populated from req if needed
		Travellers:  convertTravellersToRequest(req.Travellers),
		ContactInfo: convertContactInfoToRequest(req.ContactInfo),
		FlightBooking: models.FlightBookingRequest{
			GrossAmount: reviewResult.TotalPriceInfo.TotalFareDetail.FC.TF, // Use amount from Review API
			NetAmount:   reviewResult.TotalPriceInfo.TotalFareDetail.FC.BF, // Use base fare from Review API
		},
	}

	// Step 2: Call TripJack Hold Booking API (Hold & Confirm flow)
	holdResult, err := s.flightService.HoldBooking(ctx, reviewResult.BookingID, bookingReq)
	if err != nil {
		log.Printf("Failed to hold booking with TripJack: %v", err)
		// Update booking status to failed
		s.UpdateBookingStatus(ctx, masterBooking.BookingReference, models.BookingStatusFailed)
		return nil, fmt.Errorf("failed to hold booking: %w", err)
	}

	// Update booking with TripJack booking ID and PNR
	if err := s.db.Model(&flightBooking).Updates(map[string]interface{}{
		"provider_booking_id": holdResult.BookingID,
		"pnr":                 holdResult.PNR,
	}).Error; err != nil {
		log.Printf("Failed to update booking with TripJack details: %v", err)
	}

	// Build response
	response := &models.BookingResponse{
		BookingReference: masterBooking.BookingReference,
		Status:           string(masterBooking.Status),
		PaymentStatus:    masterBooking.PaymentStatus,
		TotalAmount:      flightBooking.GrossAmount,
		Currency:         pricingResult.PricingDetails.Currency,
		BookingDetails:   s.buildBookingDetails(flightBooking, travellers, contactInfo, pricingResult),
		Extra:            req.Extra,
		// Add TripJack booking details
		ProviderBookingID: holdResult.BookingID,
		PNR:               holdResult.PNR,
	}

	// Cache the booking for quick retrieval
	cacheKey := cache.GenerateCacheKey("booking", masterBooking.BookingReference)
	if err := s.cacheService.Set(cacheKey, response, cache.WriteThrough); err != nil {
		log.Printf("Failed to cache booking: %v", err)
	}

	log.Printf("Booking created successfully: %s", masterBooking.BookingReference)
	return response, nil
}

// GetBookingByReference retrieves a booking by its reference
func (s *BookingService) GetBookingByReference(ctx context.Context, bookingReference string) (*models.BookingResponse, error) {
	// Check cache first
	cacheKey := cache.GenerateCacheKey("booking", bookingReference)
	if cachedResult, found := s.cacheService.Get(cacheKey); found {
		if booking, ok := cachedResult.(*models.BookingResponse); ok {
			return booking, nil
		}
	}

	// Cache miss - fetch from database
	var masterBooking models.MasterBooking
	if err := s.db.Where("booking_reference = ?", bookingReference).First(&masterBooking).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("booking not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	var flightBooking models.FlightBooking
	if err := s.db.Where("master_booking_id = ?", fmt.Sprintf("%d", masterBooking.ID)).First(&flightBooking).Error; err != nil {
		return nil, fmt.Errorf("flight booking not found: %w", err)
	}

	var contactInfo models.ContactInfo
	if err := s.db.Where("flight_booking_id = ?", fmt.Sprintf("%d", flightBooking.ID)).First(&contactInfo).Error; err != nil {
		return nil, fmt.Errorf("contact info not found: %w", err)
	}

	var travellers []models.Traveller
	if err := s.db.Where("flight_booking_id = ?", fmt.Sprintf("%d", flightBooking.ID)).Find(&travellers).Error; err != nil {
		return nil, fmt.Errorf("travellers not found: %w", err)
	}

	// Build response
	response := &models.BookingResponse{
		BookingReference: masterBooking.BookingReference,
		Status:           string(masterBooking.Status),
		PaymentStatus:    masterBooking.PaymentStatus,
		TotalAmount:      flightBooking.GrossAmount,
		Currency:         "INR", // Default currency
		BookingDetails:   s.buildBookingDetailsFromDB(flightBooking, travellers, contactInfo),
	}

	// Cache the result
	if err := s.cacheService.Set(cacheKey, response, cache.WriteThrough); err != nil {
		log.Printf("Failed to cache booking: %v", err)
	}

	return response, nil
}

// GetBookingsByUser retrieves all bookings for a specific user
func (s *BookingService) GetBookingsByUser(ctx context.Context, userID uint, page, limit int) (*models.BookingListResponse, error) {
	var masterBookings []models.MasterBooking
	var total int64

	// Count total bookings
	if err := s.db.Model(&models.MasterBooking{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count bookings: %w", err)
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Retrieve bookings with pagination
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&masterBookings).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve bookings: %w", err)
	}

	var bookings []models.BookingResponse
	for _, masterBooking := range masterBookings {
		booking, err := s.GetBookingByReference(ctx, masterBooking.BookingReference)
		if err != nil {
			log.Printf("Failed to get booking details for %s: %v", masterBooking.BookingReference, err)
			continue
		}
		bookings = append(bookings, *booking)
	}

	return &models.BookingListResponse{
		Bookings: bookings,
		Total:    int(total),
		Page:     page,
		Limit:    limit,
	}, nil
}

// UpdateBookingStatus updates the status of a booking
func (s *BookingService) UpdateBookingStatus(ctx context.Context, bookingReference string, status models.BookingStatus) error {
	var masterBooking models.MasterBooking
	if err := s.db.Where("booking_reference = ?", bookingReference).First(&masterBooking).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("booking not found")
		}
		return fmt.Errorf("database error: %w", err)
	}

	// Update status
	masterBooking.Status = status
	if err := s.db.Save(&masterBooking).Error; err != nil {
		return fmt.Errorf("failed to update booking status: %w", err)
	}

	// Invalidate cache
	cacheKey := cache.GenerateCacheKey("booking", bookingReference)
	if err := s.cacheService.Delete(cacheKey); err != nil {
		log.Printf("Failed to invalidate booking cache: %v", err)
	}

	log.Printf("Booking status updated: %s -> %s", bookingReference, status)
	return nil
}

// UpdatePaymentStatus updates the payment status of a booking
func (s *BookingService) UpdatePaymentStatus(ctx context.Context, bookingReference, paymentStatus string) error {
	var masterBooking models.MasterBooking
	if err := s.db.Where("booking_reference = ?", bookingReference).First(&masterBooking).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("booking not found")
		}
		return fmt.Errorf("database error: %w", err)
	}

	// Update payment status
	masterBooking.PaymentStatus = paymentStatus
	if paymentStatus == "paid" {
		masterBooking.Status = models.BookingStatusConfirmed
	}

	if err := s.db.Save(&masterBooking).Error; err != nil {
		return fmt.Errorf("failed to update payment status: %w", err)
	}

	// Invalidate cache
	cacheKey := cache.GenerateCacheKey("booking", bookingReference)
	if err := s.cacheService.Delete(cacheKey); err != nil {
		log.Printf("Failed to invalidate booking cache: %v", err)
	}

	log.Printf("Payment status updated: %s -> %s", bookingReference, paymentStatus)
	return nil
}

// Helper functions

// filterTravellersByType filters travellers by passenger type
func (s *BookingService) filterTravellersByType(travellers []models.TravellerRequest, passengerType string) []models.TravellerRequest {
	var filtered []models.TravellerRequest
	for _, traveller := range travellers {
		// Check both new and legacy field names
		ptc := traveller.PTC
		if ptc == "" {
			ptc = traveller.PassengerType
		}
		if ptc == passengerType {
			filtered = append(filtered, traveller)
		}
	}
	return filtered
}

// generateTransactionID generates a unique transaction ID
func (s *BookingService) generateTransactionID() string {
	return fmt.Sprintf("TXN_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// generateBookingReference generates a unique booking reference
func (s *BookingService) generateBookingReference() string {
	return fmt.Sprintf("BK_%d_%d", time.Now().Unix(), time.Now().Nanosecond()%1000000)
}

// buildBookingDetails builds booking details from pricing result
func (s *BookingService) buildBookingDetails(flightBooking models.FlightBooking, travellers []models.Traveller, contactInfo models.ContactInfo, pricingResult *models.PricingResponse) models.BookingDetails {
	var travellerDetails []models.TravellerDetails
	for _, traveller := range travellers {
		travellerDetails = append(travellerDetails, models.TravellerDetails{
			Title:         traveller.Title,
			FirstName:     traveller.FirstName,
			LastName:      traveller.LastName,
			Gender:        traveller.Gender,
			DateOfBirth:   traveller.DateOfBirth.Format("2006-01-02"),
			PassengerType: traveller.PassengerType,
			TicketNumber:  traveller.TicketNumber,
			PNR:           traveller.PNR,
		})
	}

	contactDetails := models.ContactInfoDetails{
		Email:       contactInfo.Email,
		PhoneNumber: contactInfo.PhoneNumber,
		CountryCode: contactInfo.CountryCode,
		FirstName:   contactInfo.FirstName,
		LastName:    contactInfo.LastName,
		Address:     contactInfo.Address,
		City:        contactInfo.City,
		Country:     contactInfo.Country,
	}

	return models.BookingDetails{
		FlightDetails: pricingResult.PricingDetails.Segments,
		Travellers:    travellerDetails,
		ContactInfo:   contactDetails,
	}
}

// buildBookingDetailsFromDB builds booking details from database records
func (s *BookingService) buildBookingDetailsFromDB(flightBooking models.FlightBooking, travellers []models.Traveller, contactInfo models.ContactInfo) models.BookingDetails {
	var travellerDetails []models.TravellerDetails
	for _, traveller := range travellers {
		travellerDetails = append(travellerDetails, models.TravellerDetails{
			Title:         traveller.Title,
			FirstName:     traveller.FirstName,
			LastName:      traveller.LastName,
			Gender:        traveller.Gender,
			DateOfBirth:   traveller.DateOfBirth.Format("2006-01-02"),
			PassengerType: traveller.PassengerType,
			TicketNumber:  traveller.TicketNumber,
			PNR:           traveller.PNR,
		})
	}

	contactDetails := models.ContactInfoDetails{
		Email:       contactInfo.Email,
		PhoneNumber: contactInfo.PhoneNumber,
		CountryCode: contactInfo.CountryCode,
		FirstName:   contactInfo.FirstName,
		LastName:    contactInfo.LastName,
		Address:     contactInfo.Address,
		City:        contactInfo.City,
		Country:     contactInfo.Country,
	}

	return models.BookingDetails{
		FlightDetails: []models.FlightSegment{}, // Would be populated from stored flight data
		Travellers:    travellerDetails,
		ContactInfo:   contactDetails,
	}
}

// Mock methods for testing when database is nil

// createMockBooking creates a mock booking for testing
func (s *BookingService) createMockBooking(ctx context.Context, req models.BookingRequest, userID uint) (*models.BookingResponse, error) {
	// Generate mock booking reference
	bookingRef := fmt.Sprintf("MOCK_%d", time.Now().Unix())

	// Create mock response
	response := &models.BookingResponse{
		BookingReference: bookingRef,
		Status:           "pending",
		PaymentStatus:    "unpaid",
		TotalAmount:      2500.00,
		Currency:         "INR",
		BookingDetails: models.BookingDetails{
			FlightDetails: []models.FlightSegment{},
			Travellers: []models.TravellerDetails{
				{
					Title:         "Mr",
					FirstName:     "Test",
					LastName:      "User",
					Gender:        "male",
					DateOfBirth:   "1990-01-01",
					PassengerType: "ADT",
				},
			},
			ContactInfo: models.ContactInfoDetails{
				Email:       req.ContactInfo.Email,
				PhoneNumber: req.ContactInfo.PhoneNumber,
				CountryCode: req.ContactInfo.CountryCode,
				FirstName:   req.ContactInfo.FirstName,
				LastName:    req.ContactInfo.LastName,
			},
		},
		Extra: req.Extra,
	}

	log.Printf("Mock booking created successfully: %s", bookingRef)
	return response, nil
}

// extractPriceIDFromIndex extracts price ID from flight index (like Python backend)
func (s *BookingService) extractPriceIDFromIndex(index string) string {
	if index == "" {
		return ""
	}

	// Split by '|' and take the first part (like Python: item["Index"].split('|')[0])
	parts := strings.Split(index, "|")
	if len(parts) > 0 {
		return parts[0]
	}

	return ""
}

// extractPriceIDFromJourney extracts price ID from journey data
func (s *BookingService) extractPriceIDFromJourney(journey models.JourneyRequest) string {
	// In a real implementation, this should extract the price ID from the journey
	// For now, we'll look for it in the segments or use a placeholder
	// The price ID should come from the original search results

	// Check if there's any identifier in the segments
	if len(journey.Segments) > 0 {
		// Try to extract from flight FUID or other identifiers
		segment := journey.Segments[0]
		if segment.Flight.FUID != "" {
			return segment.Flight.FUID
		}
	}

	// If no price ID found, this is an error - the frontend should provide valid price IDs
	return ""
}

// getValidPriceIDsFromTUI retrieves valid price IDs from cached search results using TUI
func (s *BookingService) getValidPriceIDsFromTUI(ctx context.Context, tui string) ([]string, error) {
	if tui == "" {
		return nil, fmt.Errorf("TUI is required to get valid price IDs")
	}

	// Try to get cached search results using the flight service
	searchReq := models.SearchListRequest{TUI: tui}
	searchResults, err := s.flightService.GetCachedSearchResults(ctx, searchReq)
	if err != nil {
		log.Printf("Failed to get cached search results for TUI %s: %v", tui, err)
		return nil, fmt.Errorf("search results not found or expired for TUI: %s", tui)
	}

	// Extract price IDs from search results
	var priceIDs []string
	if searchResults != nil && len(searchResults.Trips) > 0 {
		for _, trip := range searchResults.Trips {
			for _, journey := range trip.Journey {
				// Extract price ID from journey Index (remove |0|TJ suffix)
				if journey.Index != "" {
					// Clean the index to get the core price ID
					cleanIndex := s.cleanFlightIndex(journey.Index)
					if cleanIndex != "" {
						priceIDs = append(priceIDs, cleanIndex)
					}
				}
			}
		}
	}

	if len(priceIDs) == 0 {
		log.Printf("No valid price IDs found in search results for TUI: %s", tui)
		return nil, fmt.Errorf("no valid price IDs found in search results")
	}

	log.Printf("Found %d valid price IDs from TUI %s: %v", len(priceIDs), tui, priceIDs)
	return priceIDs, nil
}

// cleanFlightIndex removes suffixes from flight index for TripJack API
func (s *BookingService) cleanFlightIndex(index string) string {
	// Split by | and take the first part (core flight identifier)
	// Example: "4-3805767935_0DELBOMSG135~1022934387950645|0|TJ" -> "4-3805767935_0DELBOMSG135~1022934387950645"
	parts := strings.Split(index, "|")
	if len(parts) > 0 {
		return parts[0]
	}
	return index
}

// convertTravellersToRequest converts CreateBookingRequest travellers to BookingRequest format
func convertTravellersToRequest(travellers []models.TravellerRequest) []models.TravellerRequest {
	// The input is already in the correct format, just return it
	return travellers
}

// convertContactInfoToRequest converts CreateBookingRequest contact info to BookingRequest format
func convertContactInfoToRequest(contactInfo models.ContactInfoRequest) models.ContactInfoRequest {
	// The input is already in the correct format, just return it
	return contactInfo
}
