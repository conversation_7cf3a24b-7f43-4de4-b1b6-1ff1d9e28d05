package services

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/go-resty/resty/v2"
	"gorm.io/gorm"

	"gobackend/internal/database"
	"gobackend/internal/models"
	"gobackend/pkg/cache"
)

// PaymentService handles payment-related business logic
type PaymentService struct {
	db             *gorm.DB
	cacheService   *cache.MultiLayerCache
	bookingService *BookingService
	httpClient     *resty.Client
	gatewayConfig  models.GatewayConfig
}

// NewPaymentService creates a new payment service
func NewPaymentService(cacheService *cache.MultiLayerCache, bookingService *BookingService, gatewayConfig models.GatewayConfig) *PaymentService {
	// Setup HTTP client for gateway communications
	client := resty.New().
		SetTimeout(30 * time.Second).
		SetRetryCount(3).
		SetRetryWaitTime(1 * time.Second)

	return &PaymentService{
		db:             database.DB.BookingDB, // Payments stored in booking database
		cacheService:   cacheService,
		bookingService: bookingService,
		httpClient:     client,
		gatewayConfig:  gatewayConfig,
	}
}

// NewPaymentServiceWithDB creates a new payment service with provided database
func NewPaymentServiceWithDB(db *gorm.DB, cacheService *cache.MultiLayerCache, bookingService *BookingService, gatewayConfig models.GatewayConfig) *PaymentService {
	// Setup HTTP client for gateway communications
	client := resty.New().
		SetTimeout(30 * time.Second).
		SetRetryCount(3).
		SetRetryWaitTime(1 * time.Second)

	return &PaymentService{
		db:             db,
		cacheService:   cacheService,
		bookingService: bookingService,
		httpClient:     client,
		gatewayConfig:  gatewayConfig,
	}
}

// InitiatePayment initiates a new payment
func (s *PaymentService) InitiatePayment(ctx context.Context, req models.PaymentRequest, userID uint) (*models.PaymentResponse, error) {
	// Handle test mode (when db is nil)
	if s.db == nil {
		return s.initiateMockPayment(ctx, req, userID)
	}

	// Validate booking exists and belongs to user
	booking, err := s.bookingService.GetBookingByReference(ctx, req.BookingReference)
	if err != nil {
		return nil, fmt.Errorf("invalid booking reference: %w", err)
	}

	// Check if payment already exists for this booking
	var existingPayment models.Payment
	err = s.db.Where("booking_reference = ? AND status IN ?", req.BookingReference,
		[]string{string(models.PaymentStatusPending), string(models.PaymentStatusCompleted)}).
		First(&existingPayment).Error

	if err == nil {
		return nil, errors.New("payment already exists for this booking")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Validate amount matches booking amount
	if req.Amount != booking.TotalAmount {
		return nil, errors.New("payment amount does not match booking amount")
	}

	// Generate payment ID
	paymentID := s.generatePaymentID()

	// Create payment record
	payment := models.Payment{
		BookingReference: req.BookingReference,
		PaymentID:        paymentID,
		Gateway:          req.Gateway,
		Method:           req.Method,
		Status:           models.PaymentStatusPending,
		Amount:           req.Amount,
		Currency:         req.Currency,
		UserID:           userID,
		NetAmount:        req.Amount, // Will be updated after gateway processing
	}

	if err := s.db.Create(&payment).Error; err != nil {
		return nil, fmt.Errorf("failed to create payment record: %w", err)
	}

	// Process payment with selected gateway
	gatewayResponse, err := s.processWithGateway(ctx, payment, req)
	if err != nil {
		// Update payment status to failed
		payment.Status = models.PaymentStatusFailed
		payment.FailureReason = err.Error()
		s.db.Save(&payment)
		return nil, fmt.Errorf("gateway processing failed: %w", err)
	}

	// Update payment with gateway response
	payment.GatewayPaymentID = gatewayResponse.GatewayPaymentID
	gatewayResponseJSON, _ := json.Marshal(gatewayResponse.GatewayData)
	payment.GatewayResponse = string(gatewayResponseJSON)

	if err := s.db.Save(&payment).Error; err != nil {
		log.Printf("Failed to update payment with gateway response: %v", err)
	}

	// Build response
	response := &models.PaymentResponse{
		PaymentID:   paymentID,
		Status:      string(payment.Status),
		Amount:      payment.Amount,
		Currency:    payment.Currency,
		Gateway:     string(payment.Gateway),
		Method:      string(payment.Method),
		PaymentURL:  gatewayResponse.PaymentURL,
		GatewayData: gatewayResponse.GatewayData,
		ExpiresAt:   gatewayResponse.ExpiresAt,
		CreatedAt:   payment.CreatedAt,
	}

	// Cache payment for quick retrieval
	cacheKey := cache.GenerateCacheKey("payment", paymentID)
	if err := s.cacheService.Set(cacheKey, response, cache.WriteThrough); err != nil {
		log.Printf("Failed to cache payment: %v", err)
	}

	log.Printf("Payment initiated successfully: %s", paymentID)
	return response, nil
}

// processWithGateway processes payment with the selected gateway
func (s *PaymentService) processWithGateway(ctx context.Context, payment models.Payment, req models.PaymentRequest) (*GatewayResponse, error) {
	switch payment.Gateway {
	case models.PaymentGatewayRazorpay:
		return s.processRazorpayPayment(ctx, payment, req)
	case models.PaymentGatewayPayU:
		return s.processPayUPayment(ctx, payment, req)
	case models.PaymentGatewayPaytm:
		return s.processPaytmPayment(ctx, payment, req)
	default:
		return nil, fmt.Errorf("unsupported payment gateway: %s", payment.Gateway)
	}
}

// GatewayResponse represents response from payment gateway
type GatewayResponse struct {
	GatewayPaymentID string                 `json:"gateway_payment_id"`
	PaymentURL       string                 `json:"payment_url"`
	GatewayData      map[string]interface{} `json:"gateway_data"`
	ExpiresAt        *time.Time             `json:"expires_at,omitempty"`
}

// processRazorpayPayment processes payment with Razorpay
func (s *PaymentService) processRazorpayPayment(ctx context.Context, payment models.Payment, req models.PaymentRequest) (*GatewayResponse, error) {
	// Razorpay order creation
	orderData := map[string]interface{}{
		"amount":   int(payment.Amount * 100), // Razorpay expects amount in paise
		"currency": payment.Currency,
		"receipt":  payment.PaymentID,
		"notes": map[string]interface{}{
			"booking_reference": payment.BookingReference,
			"user_id":           payment.UserID,
		},
	}

	var response map[string]interface{}
	resp, err := s.httpClient.R().
		SetContext(ctx).
		SetBasicAuth(s.gatewayConfig.Razorpay.KeyID, s.gatewayConfig.Razorpay.KeySecret).
		SetHeader("Content-Type", "application/json").
		SetBody(orderData).
		SetResult(&response).
		Post(s.gatewayConfig.Razorpay.BaseURL + "/orders")

	if err != nil {
		return nil, fmt.Errorf("razorpay API error: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("razorpay API returned status %d", resp.StatusCode())
	}

	orderID, ok := response["id"].(string)
	if !ok {
		return nil, errors.New("invalid response from Razorpay")
	}

	// Calculate expiry (15 minutes from now)
	expiresAt := time.Now().Add(15 * time.Minute)

	return &GatewayResponse{
		GatewayPaymentID: orderID,
		PaymentURL:       "", // Razorpay uses client-side integration
		GatewayData: map[string]interface{}{
			"order_id":    orderID,
			"key":         s.gatewayConfig.Razorpay.KeyID,
			"amount":      payment.Amount * 100,
			"currency":    payment.Currency,
			"name":        "Fast Travel",
			"description": fmt.Sprintf("Payment for booking %s", payment.BookingReference),
			"prefill": map[string]interface{}{
				"name":    req.CustomerInfo.Name,
				"email":   req.CustomerInfo.Email,
				"contact": req.CustomerInfo.PhoneNumber,
			},
			"theme": map[string]interface{}{
				"color": "#3399cc",
			},
		},
		ExpiresAt: &expiresAt,
	}, nil
}

// processPayUPayment processes payment with PayU
func (s *PaymentService) processPayUPayment(ctx context.Context, payment models.Payment, req models.PaymentRequest) (*GatewayResponse, error) {
	// PayU payment processing (simplified)
	txnID := payment.PaymentID

	// Generate hash for PayU
	hashString := fmt.Sprintf("%s|%s|%.2f|%s|%s|%s|%s|%s|%s|%s|%s",
		s.gatewayConfig.PayU.MerchantKey,
		txnID,
		payment.Amount,
		"Fast Travel Booking",
		req.CustomerInfo.Name,
		req.CustomerInfo.Email,
		"",
		"",
		"",
		"",
		s.gatewayConfig.PayU.Salt,
	)

	hash := s.generateSHA512Hash(hashString)

	paymentURL := fmt.Sprintf("%s/_payment", s.gatewayConfig.PayU.BaseURL)

	return &GatewayResponse{
		GatewayPaymentID: txnID,
		PaymentURL:       paymentURL,
		GatewayData: map[string]interface{}{
			"key":         s.gatewayConfig.PayU.MerchantKey,
			"txnid":       txnID,
			"amount":      payment.Amount,
			"productinfo": "Fast Travel Booking",
			"firstname":   req.CustomerInfo.Name,
			"email":       req.CustomerInfo.Email,
			"phone":       req.CustomerInfo.PhoneNumber,
			"surl":        req.CallbackURL,
			"furl":        req.CancelURL,
			"hash":        hash,
		},
	}, nil
}

// processPaytmPayment processes payment with Paytm
func (s *PaymentService) processPaytmPayment(ctx context.Context, payment models.Payment, req models.PaymentRequest) (*GatewayResponse, error) {
	// Paytm payment processing (simplified)
	// In a real implementation, you would generate checksum and create transaction token

	return &GatewayResponse{
		GatewayPaymentID: payment.PaymentID,
		PaymentURL:       s.gatewayConfig.Paytm.BaseURL + "/theia/processTransaction",
		GatewayData: map[string]interface{}{
			"MID":              s.gatewayConfig.Paytm.MerchantID,
			"ORDER_ID":         payment.PaymentID,
			"TXN_AMOUNT":       payment.Amount,
			"CUST_ID":          fmt.Sprintf("CUST_%d", payment.UserID),
			"INDUSTRY_TYPE_ID": "Retail",
			"WEBSITE":          s.gatewayConfig.Paytm.Website,
			"CHANNEL_ID":       "WEB",
			"CALLBACK_URL":     req.CallbackURL,
		},
	}, nil
}

// HandlePaymentCallback handles payment callbacks from gateways
func (s *PaymentService) HandlePaymentCallback(ctx context.Context, gateway models.PaymentGateway, callbackData map[string]interface{}) error {
	switch gateway {
	case models.PaymentGatewayRazorpay:
		return s.handleRazorpayCallback(ctx, callbackData)
	case models.PaymentGatewayPayU:
		return s.handlePayUCallback(ctx, callbackData)
	case models.PaymentGatewayPaytm:
		return s.handlePaytmCallback(ctx, callbackData)
	default:
		return fmt.Errorf("unsupported gateway for callback: %s", gateway)
	}
}

// handleRazorpayCallback handles Razorpay payment callbacks
func (s *PaymentService) handleRazorpayCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// Extract payment details from callback
	razorpayPaymentID, ok := callbackData["razorpay_payment_id"].(string)
	if !ok {
		return errors.New("missing razorpay_payment_id in callback")
	}

	razorpayOrderID, ok := callbackData["razorpay_order_id"].(string)
	if !ok {
		return errors.New("missing razorpay_order_id in callback")
	}

	// Verify signature
	signature, ok := callbackData["razorpay_signature"].(string)
	if !ok {
		return errors.New("missing razorpay_signature in callback")
	}

	if !s.verifyRazorpaySignature(razorpayOrderID, razorpayPaymentID, signature) {
		return errors.New("invalid signature")
	}

	// Find payment by gateway payment ID
	var payment models.Payment
	if err := s.db.Where("gateway_payment_id = ?", razorpayOrderID).First(&payment).Error; err != nil {
		return fmt.Errorf("payment not found: %w", err)
	}

	// Update payment status
	payment.Status = models.PaymentStatusCompleted
	payment.GatewayPaymentID = razorpayPaymentID
	now := time.Now()
	payment.ProcessedAt = &now

	if err := s.db.Save(&payment).Error; err != nil {
		return fmt.Errorf("failed to update payment: %w", err)
	}

	// Update booking payment status
	if err := s.bookingService.UpdatePaymentStatus(ctx, payment.BookingReference, "paid"); err != nil {
		log.Printf("Failed to update booking payment status: %v", err)
	}

	// Invalidate cache
	cacheKey := cache.GenerateCacheKey("payment", payment.PaymentID)
	s.cacheService.Delete(cacheKey)

	log.Printf("Payment completed successfully: %s", payment.PaymentID)
	return nil
}

// handlePayUCallback handles PayU payment callbacks
func (s *PaymentService) handlePayUCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// Extract payment details from PayU callback
	txnID, ok := callbackData["txnid"].(string)
	if !ok {
		return errors.New("missing txnid in callback")
	}

	status, ok := callbackData["status"].(string)
	if !ok {
		return errors.New("missing status in callback")
	}

	// Find payment
	var payment models.Payment
	if err := s.db.Where("payment_id = ?", txnID).First(&payment).Error; err != nil {
		return fmt.Errorf("payment not found: %w", err)
	}

	// Update payment based on status
	now := time.Now()
	payment.ProcessedAt = &now

	switch status {
	case "success":
		payment.Status = models.PaymentStatusCompleted
		// Update booking payment status
		if err := s.bookingService.UpdatePaymentStatus(ctx, payment.BookingReference, "paid"); err != nil {
			log.Printf("Failed to update booking payment status: %v", err)
		}
	case "failure":
		payment.Status = models.PaymentStatusFailed
		if reason, ok := callbackData["error_Message"].(string); ok {
			payment.FailureReason = reason
		}
	default:
		payment.Status = models.PaymentStatusPending
	}

	if err := s.db.Save(&payment).Error; err != nil {
		return fmt.Errorf("failed to update payment: %w", err)
	}

	// Invalidate cache
	cacheKey := cache.GenerateCacheKey("payment", payment.PaymentID)
	s.cacheService.Delete(cacheKey)

	log.Printf("PayU payment callback processed: %s -> %s", payment.PaymentID, payment.Status)
	return nil
}

// handlePaytmCallback handles Paytm payment callbacks
func (s *PaymentService) handlePaytmCallback(ctx context.Context, callbackData map[string]interface{}) error {
	// Similar implementation for Paytm
	orderID, ok := callbackData["ORDERID"].(string)
	if !ok {
		return errors.New("missing ORDERID in callback")
	}

	status, ok := callbackData["STATUS"].(string)
	if !ok {
		return errors.New("missing STATUS in callback")
	}

	// Find payment
	var payment models.Payment
	if err := s.db.Where("payment_id = ?", orderID).First(&payment).Error; err != nil {
		return fmt.Errorf("payment not found: %w", err)
	}

	// Update payment status
	now := time.Now()
	payment.ProcessedAt = &now

	switch status {
	case "TXN_SUCCESS":
		payment.Status = models.PaymentStatusCompleted
		if err := s.bookingService.UpdatePaymentStatus(ctx, payment.BookingReference, "paid"); err != nil {
			log.Printf("Failed to update booking payment status: %v", err)
		}
	case "TXN_FAILURE":
		payment.Status = models.PaymentStatusFailed
		if reason, ok := callbackData["RESPMSG"].(string); ok {
			payment.FailureReason = reason
		}
	default:
		payment.Status = models.PaymentStatusPending
	}

	if err := s.db.Save(&payment).Error; err != nil {
		return fmt.Errorf("failed to update payment: %w", err)
	}

	// Invalidate cache
	cacheKey := cache.GenerateCacheKey("payment", payment.PaymentID)
	s.cacheService.Delete(cacheKey)

	log.Printf("Paytm payment callback processed: %s -> %s", payment.PaymentID, payment.Status)
	return nil
}

// Helper functions

// generatePaymentID generates a unique payment ID
func (s *PaymentService) generatePaymentID() string {
	return fmt.Sprintf("PAY_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// verifyRazorpaySignature verifies Razorpay webhook signature
func (s *PaymentService) verifyRazorpaySignature(orderID, paymentID, signature string) bool {
	data := orderID + "|" + paymentID
	expectedSignature := s.generateHMACSHA256(data, s.gatewayConfig.Razorpay.KeySecret)
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

// generateHMACSHA256 generates HMAC SHA256 hash
func (s *PaymentService) generateHMACSHA256(data, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// generateSHA512Hash generates SHA512 hash
func (s *PaymentService) generateSHA512Hash(data string) string {
	h := sha256.New()
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// Mock methods for testing when database is nil

// initiateMockPayment creates a mock payment for testing
func (s *PaymentService) initiateMockPayment(ctx context.Context, req models.PaymentRequest, userID uint) (*models.PaymentResponse, error) {
	// Generate mock payment ID
	paymentID := fmt.Sprintf("MOCK_PAY_%d", time.Now().Unix())

	// Create mock response
	response := &models.PaymentResponse{
		PaymentID:  paymentID,
		Status:     "pending",
		Amount:     req.Amount,
		Currency:   req.Currency,
		Gateway:    string(req.Gateway),
		Method:     string(req.Method),
		PaymentURL: "https://mock-gateway.com/pay/" + paymentID,
		GatewayData: map[string]interface{}{
			"mock_order_id": paymentID,
			"test_mode":     true,
		},
		CreatedAt: time.Now(),
	}

	log.Printf("Mock payment initiated successfully: %s", paymentID)
	return response, nil
}
