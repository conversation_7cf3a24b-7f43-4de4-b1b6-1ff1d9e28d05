package handlers

import (
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"gobackend/internal/models"
	"gobackend/internal/services"
)

// FlightHandler handles flight-related HTTP requests
type <PERSON>Handler struct {
	flightService *services.FlightService
	validator     *validator.Validate
}

// NewFlightHandler creates a new flight handler
func NewFlightHandler(flightService *services.FlightService) *FlightHandler {
	return &FlightHandler{
		flightService: flightService,
		validator:     validator.New(),
	}
}

// SearchFlights handles flight search requests
func (h *FlightHandler) SearchFlights(c *gin.Context) {
	var req models.FlightSearchRequest

	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Perform flight search
	result, err := h.flightService.SearchFlights(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Flight search failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Set cache status header
	if result.CacheHit {
		c.Header("X-Cache-Status", "HIT")
	} else {
		c.Header("X-Cache-Status", "MISS")
	}

	// Set response time header
	c.Header("X-Response-Time", strconv.FormatFloat(result.ResponseTimeMS, 'f', 2, 64))

	c.JSON(http.StatusOK, result)
}

// GetFlightPricing handles flight pricing requests
func (h *FlightHandler) GetFlightPricing(c *gin.Context) {
	var req models.PricingRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get flight pricing
	result, err := h.flightService.GetFlightPricing(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Flight pricing failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Set response time header
	c.Header("X-Response-Time", strconv.FormatFloat(result.ResponseTimeMS, 'f', 2, 64))

	c.JSON(http.StatusOK, result)
}

// GetFlightDetails handles flight details requests
func (h *FlightHandler) GetFlightDetails(c *gin.Context) {
	var req models.DetailsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Convert to pricing request structure for the service call
	pricingReq := models.PricingRequest{
		Trips:    req.Trips,
		ClientID: req.ClientID,
		Mode:     req.Mode,
		Options:  req.Options,
		Source:   req.Source,
		TripType: req.TripType,
	}

	// Get flight details (using pricing endpoint for now)
	result, err := h.flightService.GetFlightPricing(c.Request.Context(), pricingReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Flight details retrieval failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetCachedSearchResults handles cached search results retrieval
func (h *FlightHandler) GetCachedSearchResults(c *gin.Context) {
	var req models.SearchListRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get cached search results
	result, err := h.flightService.GetCachedSearchResults(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "Cached results not found",
			Message: err.Error(),
			Code:    http.StatusNotFound,
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SearchAirports handles airport search requests (both GET and POST)
func (h *FlightHandler) SearchAirports(c *gin.Context) {
	var query string
	var limit int = 10 // Default limit

	// Handle both GET and POST requests
	if c.Request.Method == "GET" {
		// GET request with query parameters
		query = c.Query("query")
		if limitStr := c.Query("limit"); limitStr != "" {
			if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
				limit = parsedLimit
			}
		}
	} else {
		// POST request with JSON body
		var req map[string]interface{}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Error:   "Invalid request format",
				Message: err.Error(),
				Code:    http.StatusBadRequest,
			})
			return
		}

		// Extract query from request body
		if q, ok := req["query"].(string); ok {
			query = q
		}
		if l, ok := req["limit"].(float64); ok {
			limit = int(l)
		}
	}

	// Validate query
	if query == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Query required",
			Message: "Query parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Create airport search request
	searchReq := models.AirportSearchRequest{
		Query: query,
		Limit: limit,
	}

	// Search airports
	result, err := h.flightService.SearchAirports(c.Request.Context(), searchReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Airport search failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetFareRules handles fare rules requests
func (h *FlightHandler) GetFareRules(c *gin.Context) {
	var req models.RulesRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Extract TUI and FareID from the first trip (following Python backend pattern)
	var tui, fareID string
	if len(req.Trips) > 0 {
		tui = req.Trips[0].TUI
		fareID = req.Trips[0].Index
	}

	// Call TripJack API to get actual fare rules
	result, err := h.flightService.GetFareRules(c.Request.Context(), fareID)
	if err != nil {
		// If TripJack API fails, return sample fare rules as fallback
		log.Printf("[Fare Rules Handler] TripJack API failed, returning sample data: %v", err)

		fareRules := []models.FareRule{
			{
				RuleType:    "Cancellation",
				Description: "Cancellation charges apply as per airline policy",
				Penalty:     "INR 3000 + Airline charges",
			},
			{
				RuleType:    "Date Change",
				Description: "Date change allowed with charges",
				Penalty:     "INR 2500 + Fare difference",
			},
			{
				RuleType:    "Refund",
				Description: "Refund allowed with cancellation charges",
				Penalty:     "As per cancellation policy",
			},
		}

		response := map[string]interface{}{
			"TUI":       tui,
			"FareID":    fareID,
			"Status":    "success",
			"FareRules": fareRules,
		}
		c.JSON(http.StatusOK, response)
		return
	}

	// Return actual TripJack fare rules
	response := map[string]interface{}{
		"TUI":       tui,
		"FareID":    fareID,
		"Status":    result.Status,
		"FareRules": result.FareRules,
	}

	c.JSON(http.StatusOK, response)
}

// GetSSRServices handles SSR (Special Service Requests) requests
func (h *FlightHandler) GetSSRServices(c *gin.Context) {
	var req models.ServiceRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Extract TUI and FareID from the first trip (following Python backend pattern)
	var tui, fareID string
	if len(req.Trips) > 0 {
		tui = req.Trips[0].TUI
		fareID = req.Trips[0].Index
	}

	// For now, return sample SSR services
	// In a real implementation, this would fetch from TripJack API
	ssrServices := map[string]interface{}{
		"MealServices": []map[string]interface{}{
			{"Code": "VGML", "Description": "Vegetarian Meal", "Price": 500},
			{"Code": "NVML", "Description": "Non-Vegetarian Meal", "Price": 600},
			{"Code": "JNML", "Description": "Jain Meal", "Price": 500},
		},
		"SeatServices": []map[string]interface{}{
			{"Code": "SEAT", "Description": "Preferred Seat Selection", "Price": 800},
			{"Code": "XSEAT", "Description": "Extra Legroom Seat", "Price": 1500},
		},
		"BaggageServices": []map[string]interface{}{
			{"Code": "XBAG", "Description": "Extra Baggage 15kg", "Price": 2000},
			{"Code": "XBAG25", "Description": "Extra Baggage 25kg", "Price": 3500},
		},
	}

	response := map[string]interface{}{
		"TUI":         tui,
		"FareID":      fareID,
		"Status":      "success",
		"SSRServices": ssrServices,
	}

	c.JSON(http.StatusOK, response)
}

// GetPricingList handles pricing list requests (similar to GetFlightPricing)
func (h *FlightHandler) GetPricingList(c *gin.Context) {
	// Reuse the same logic as GetFlightPricing
	h.GetFlightPricing(c)
}

// GetWebSettings handles web settings requests
func (h *FlightHandler) GetWebSettings(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Return sample web settings
	webSettings := map[string]interface{}{
		"ClientID": req["ClientID"],
		"TUI":      req["TUI"],
		"Status":   "success",
		"Settings": map[string]interface{}{
			"Currency":          "INR",
			"Language":          "en",
			"TimeZone":          "Asia/Kolkata",
			"PaymentMethods":    []string{"Credit Card", "Debit Card", "Net Banking", "UPI"},
			"SupportedAirlines": []string{"6E", "AI", "SG", "UK", "G8"},
			"MaxSearchResults":  100,
			"CacheTimeout":      300,
		},
	}

	c.JSON(http.StatusOK, webSettings)
}

// RetrieveBooking handles booking retrieval requests
func (h *FlightHandler) RetrieveBooking(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, this would retrieve booking from external system
	response := map[string]interface{}{
		"Status":  "success",
		"Message": "Booking retrieval functionality not implemented yet",
		"Data":    req,
	}

	c.JSON(http.StatusOK, response)
}

// GetTravelChecklist handles travel checklist requests
func (h *FlightHandler) GetTravelChecklist(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Return sample travel checklist
	checklist := map[string]interface{}{
		"Status": "success",
		"Checklist": []map[string]interface{}{
			{"Item": "Valid ID Proof", "Required": true, "Description": "Passport/Aadhaar/Driving License"},
			{"Item": "Boarding Pass", "Required": true, "Description": "Print or mobile boarding pass"},
			{"Item": "Travel Insurance", "Required": false, "Description": "Recommended for international travel"},
			{"Item": "Vaccination Certificate", "Required": false, "Description": "If required by destination"},
		},
	}

	c.JSON(http.StatusOK, checklist)
}

// GetFlightSeat handles flight seat selection requests
func (h *FlightHandler) GetFlightSeat(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Return sample seat map
	seatMap := map[string]interface{}{
		"Status": "success",
		"SeatMap": map[string]interface{}{
			"Aircraft":    "Boeing 737-800",
			"Rows":        30,
			"SeatsPerRow": 6,
			"AvailableSeats": []map[string]interface{}{
				{"Row": 1, "Seat": "A", "Type": "Business", "Price": 2000, "Available": true},
				{"Row": 1, "Seat": "B", "Type": "Business", "Price": 2000, "Available": false},
				{"Row": 7, "Seat": "A", "Type": "Economy", "Price": 800, "Available": true},
				{"Row": 7, "Seat": "F", "Type": "Economy", "Price": 800, "Available": true},
			},
		},
	}

	c.JSON(http.StatusOK, seatMap)
}

// GetAirlineSSR handles airline-specific SSR requests
func (h *FlightHandler) GetAirlineSSR(c *gin.Context) {
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Return airline-specific SSR services
	airlineSSR := map[string]interface{}{
		"Status":      "success",
		"AirlineCode": req["AirlineCode"],
		"SSRServices": map[string]interface{}{
			"SpecialMeals": []map[string]interface{}{
				{"Code": "AVML", "Description": "Asian Vegetarian Meal", "Price": 0},
				{"Code": "BBML", "Description": "Baby Meal", "Price": 0},
				{"Code": "CHML", "Description": "Child Meal", "Price": 0},
			},
			"SpecialAssistance": []map[string]interface{}{
				{"Code": "WCHR", "Description": "Wheelchair - Ramp", "Price": 0},
				{"Code": "WCHC", "Description": "Wheelchair - Cabin Seat", "Price": 0},
				{"Code": "BLND", "Description": "Blind Passenger", "Price": 0},
			},
		},
	}

	c.JSON(http.StatusOK, airlineSSR)
}
