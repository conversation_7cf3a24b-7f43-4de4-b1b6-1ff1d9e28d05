package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"gobackend/internal/models"
	"gobackend/internal/services"
	"gobackend/pkg/auth"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authService *services.AuthService
	jwtManager  *auth.JWTManager
	validator   *validator.Validate
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(authService *services.AuthService, jwtManager *auth.JWTManager) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		jwtManager:  jwtManager,
		validator:   validator.New(),
	}
}

// RegisterUser handles user registration
func (h *AuthHandler) RegisterUser(c *gin.Context) {
	var req models.UserCreateRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Map frontend role values to backend enum values
	switch string(req.Role) {
	case "user", "customer":
		req.Role = models.UserRoleCustomer
	case "admin":
		req.Role = models.UserRoleAdmin
	default:
		// If no role specified or invalid role, default to customer
		req.Role = models.UserRoleCustomer
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Create user
	user, err := h.authService.CreateUser(req)
	if err != nil {
		// Check if it's a user already exists error
		if err.Error() == "user with this email already exists" {
			c.JSON(http.StatusConflict, models.ErrorResponse{
				Error:   "User already exists",
				Message: err.Error(),
				Code:    http.StatusConflict,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to create user",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse{
		Success: true,
		Message: "User created successfully. OTP sent for verification.",
		Data:    user.ToResponse(),
	})
}

// LoginUser handles user login
func (h *AuthHandler) LoginUser(c *gin.Context) {
	var req models.UserLoginRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Authenticate user
	user, err := h.authService.AuthenticateUser(req.Email, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "Authentication failed",
			Message: err.Error(),
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Generate JWT token
	token, err := h.jwtManager.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to generate token",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	tokenResponse := h.jwtManager.CreateTokenResponse(token)

	c.JSON(http.StatusOK, models.AuthTokenResponse{
		AccessToken: tokenResponse.AccessToken,
		TokenType:   tokenResponse.TokenType,
		ExpiresIn:   tokenResponse.ExpiresIn,
		User:        user.ToResponse(),
	})
}

// VerifyOTP handles OTP verification
func (h *AuthHandler) VerifyOTP(c *gin.Context) {
	var req models.UserOTPVerificationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Verify OTP
	user, err := h.authService.VerifyOTP(req.Email, req.OTP)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "OTP verification failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Generate JWT token after successful verification
	token, err := h.jwtManager.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to generate token",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	tokenResponse := h.jwtManager.CreateTokenResponse(token)

	c.JSON(http.StatusOK, models.AuthTokenResponse{
		AccessToken: tokenResponse.AccessToken,
		TokenType:   tokenResponse.TokenType,
		ExpiresIn:   tokenResponse.ExpiresIn,
		User:        user.ToResponse(),
	})
}

// GetUserProfile handles getting user profile
func (h *AuthHandler) GetUserProfile(c *gin.Context) {
	// Get user from context (set by auth middleware)
	userInterface, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "User not found in context",
			Message: "Authentication required",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	claims, ok := userInterface.(*auth.Claims)
	if !ok {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Invalid user data",
			Message: "Failed to parse user information",
			Code:    http.StatusInternalServerError,
		})
		return
	}

	// Get user details from database
	user, err := h.authService.GetUserByID(claims.UserID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "User not found",
			Message: err.Error(),
			Code:    http.StatusNotFound,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "User profile retrieved successfully",
		Data:    user.ToResponse(),
	})
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// Get current token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "Authorization header missing",
			Message: "Token required for refresh",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Extract token (remove "Bearer " prefix)
	tokenString := authHeader[7:]

	// Refresh token
	newToken, err := h.jwtManager.RefreshToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "Token refresh failed",
			Message: err.Error(),
			Code:    http.StatusUnauthorized,
		})
		return
	}

	tokenResponse := h.jwtManager.CreateTokenResponse(newToken)

	c.JSON(http.StatusOK, tokenResponse)
}

// ResendOTP handles OTP resend
func (h *AuthHandler) ResendOTP(c *gin.Context) {
	email := c.Query("email")
	if email == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Email required",
			Message: "Email parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Resend OTP
	err := h.authService.ResendOTP(email)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Failed to resend OTP",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "OTP resent successfully",
	})
}

// GetUserByID handles getting user by ID (admin only)
func (h *AuthHandler) GetUserByID(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid number",
			Code:    http.StatusBadRequest,
		})
		return
	}

	user, err := h.authService.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "User not found",
			Message: err.Error(),
			Code:    http.StatusNotFound,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "User retrieved successfully",
		Data:    user.ToResponse(),
	})
}

// GetLoginOTP handles getting login OTP for existing user
func (h *AuthHandler) GetLoginOTP(c *gin.Context) {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid number",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get user by ID
	user, err := h.authService.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error:   "User not found",
			Message: err.Error(),
			Code:    http.StatusNotFound,
		})
		return
	}

	// Generate and send OTP
	err = h.authService.ResendOTP(user.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to send OTP",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":    user.ID,
		"email": user.Email,
	})
}

// LoginWithOTP handles login with email and OTP via query parameters
func (h *AuthHandler) LoginWithOTP(c *gin.Context) {
	email := c.Query("email")
	otp := c.Query("otp")

	if email == "" || otp == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Missing parameters",
			Message: "Email and OTP are required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Verify OTP
	user, err := h.authService.VerifyOTP(email, otp)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "OTP verification failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Generate JWT token after successful verification
	token, err := h.jwtManager.GenerateToken(user.ID, user.Email, string(user.Role))
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Failed to generate token",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":      "Login successful",
		"verified":     true,
		"access_token": token,
	})
}
