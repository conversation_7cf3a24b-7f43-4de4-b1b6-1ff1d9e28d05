package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"gobackend/internal/middleware"
	"gobackend/internal/models"
	"gobackend/internal/services"
)

// PaymentHandler handles payment-related HTTP requests
type PaymentHandler struct {
	paymentService *services.PaymentService
	validator      *validator.Validate
}

// NewPaymentHandler creates a new payment handler
func NewPaymentHandler(paymentService *services.PaymentService) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		validator:      validator.New(),
	}
}

// InitiatePayment handles payment initiation requests
func (h *PaymentHandler) InitiatePayment(c *gin.Context) {
	var req models.PaymentRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get user ID from context
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error:   "User not authenticated",
			Message: "Authentication required",
			Code:    http.StatusUnauthorized,
		})
		return
	}

	// Initiate payment
	result, err := h.paymentService.InitiatePayment(c.Request.Context(), req, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Payment initiation failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusCreated, models.SuccessResponse{
		Success: true,
		Message: "Payment initiated successfully",
		Data:    result,
	})
}

// HandleRazorpayCallback handles Razorpay payment callbacks
func (h *PaymentHandler) HandleRazorpayCallback(c *gin.Context) {
	var callbackData map[string]interface{}

	if err := c.ShouldBindJSON(&callbackData); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid callback data",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Process callback
	err := h.paymentService.HandlePaymentCallback(c.Request.Context(), models.PaymentGatewayRazorpay, callbackData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Callback processing failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Callback processed successfully",
	})
}

// HandlePayUCallback handles PayU payment callbacks
func (h *PaymentHandler) HandlePayUCallback(c *gin.Context) {
	// PayU sends form data, not JSON
	callbackData := make(map[string]interface{})
	
	// Extract form values
	for key, values := range c.Request.Form {
		if len(values) > 0 {
			callbackData[key] = values[0]
		}
	}

	// Process callback
	err := h.paymentService.HandlePaymentCallback(c.Request.Context(), models.PaymentGatewayPayU, callbackData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Callback processing failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Callback processed successfully",
	})
}

// HandlePaytmCallback handles Paytm payment callbacks
func (h *PaymentHandler) HandlePaytmCallback(c *gin.Context) {
	// Paytm sends form data
	callbackData := make(map[string]interface{})
	
	// Extract form values
	for key, values := range c.Request.Form {
		if len(values) > 0 {
			callbackData[key] = values[0]
		}
	}

	// Process callback
	err := h.paymentService.HandlePaymentCallback(c.Request.Context(), models.PaymentGatewayPaytm, callbackData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Callback processing failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Callback processed successfully",
	})
}

// GetPaymentStatus handles payment status queries
func (h *PaymentHandler) GetPaymentStatus(c *gin.Context) {
	paymentID := c.Param("payment_id")
	if paymentID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Payment ID required",
			Message: "Payment ID parameter is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, you would query the database
	response := models.PaymentStatusResponse{
		PaymentID:        paymentID,
		BookingReference: "BOOK_123456",
		Status:           "completed",
		Amount:           2500.00,
		Currency:         "INR",
		Gateway:          "razorpay",
		Method:           "card",
		TransactionFee:   50.00,
		NetAmount:        2450.00,
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Payment status retrieved successfully",
		Data:    response,
	})
}

// ProcessRefund handles payment refund requests
func (h *PaymentHandler) ProcessRefund(c *gin.Context) {
	var req models.RefundRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, you would process the refund
	response := models.RefundResponse{
		RefundID:         "REF_" + req.PaymentID,
		PaymentID:        req.PaymentID,
		BookingReference: "BOOK_123456",
		Amount:           req.Amount,
		Currency:         "INR",
		Status:           "processed",
		Reason:           req.Reason,
		EstimatedArrival: "5-7 business days",
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Refund processed successfully",
		Data:    response,
	})
}

// GetPaymentAnalytics handles payment analytics requests (admin only)
func (h *PaymentHandler) GetPaymentAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "month")

	// For now, return sample analytics data
	// In a real implementation, you would query the database
	analytics := models.PaymentAnalytics{
		TotalPayments:      1250,
		TotalAmount:        3125000.00,
		SuccessfulPayments: 1180,
		FailedPayments:     45,
		PendingPayments:    25,
		RefundedPayments:   15,
		SuccessRate:        94.4,
		AverageAmount:      2500.00,
		Currency:           "INR",
		Period:             period,
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Payment analytics retrieved successfully",
		Data:    analytics,
	})
}

// GetPaymentMethodStats handles payment method statistics (admin only)
func (h *PaymentHandler) GetPaymentMethodStats(c *gin.Context) {
	// Sample payment method statistics
	stats := []models.PaymentMethodStats{
		{
			Method:      "card",
			Count:       750,
			Amount:      1875000.00,
			SuccessRate: 96.2,
		},
		{
			Method:      "upi",
			Count:       350,
			Amount:      875000.00,
			SuccessRate: 94.8,
		},
		{
			Method:      "wallet",
			Count:       100,
			Amount:      250000.00,
			SuccessRate: 92.1,
		},
		{
			Method:      "netbanking",
			Count:       50,
			Amount:      125000.00,
			SuccessRate: 89.5,
		},
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Payment method statistics retrieved successfully",
		Data:    stats,
	})
}

// GetGatewayStats handles payment gateway statistics (admin only)
func (h *PaymentHandler) GetGatewayStats(c *gin.Context) {
	// Sample gateway statistics
	stats := []models.GatewayStats{
		{
			Gateway:         "razorpay",
			Count:           800,
			Amount:          2000000.00,
			SuccessRate:     95.8,
			AvgResponseTime: 1250.5,
		},
		{
			Gateway:         "payu",
			Count:           300,
			Amount:          750000.00,
			SuccessRate:     93.2,
			AvgResponseTime: 1850.2,
		},
		{
			Gateway:         "paytm",
			Count:           150,
			Amount:          375000.00,
			SuccessRate:     91.5,
			AvgResponseTime: 2100.8,
		},
	}

	c.JSON(http.StatusOK, models.SuccessResponse{
		Success: true,
		Message: "Gateway statistics retrieved successfully",
		Data:    stats,
	})
}

// WebhookHandler handles generic webhook events
func (h *PaymentHandler) WebhookHandler(c *gin.Context) {
	gateway := c.Param("gateway")
	
	var webhookData map[string]interface{}
	if err := c.ShouldBindJSON(&webhookData); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Invalid webhook data",
			Message: err.Error(),
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Process webhook based on gateway
	var gatewayType models.PaymentGateway
	switch gateway {
	case "razorpay":
		gatewayType = models.PaymentGatewayRazorpay
	case "payu":
		gatewayType = models.PaymentGatewayPayU
	case "paytm":
		gatewayType = models.PaymentGatewayPaytm
	default:
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error:   "Unsupported gateway",
			Message: "Gateway not supported for webhooks",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Process webhook
	err := h.paymentService.HandlePaymentCallback(c.Request.Context(), gatewayType, webhookData)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error:   "Webhook processing failed",
			Message: err.Error(),
			Code:    http.StatusInternalServerError,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"status": "success"})
}
