package models

import (
	"encoding/json"
	"fmt"
	"time"
)

// BookingStatus represents the status of a booking
type BookingStatus string

const (
	BookingStatusPending   BookingStatus = "pending"
	BookingStatusConfirmed BookingStatus = "confirmed"
	BookingStatusCancelled BookingStatus = "cancelled"
	BookingStatusCompleted BookingStatus = "completed"
	BookingStatusFailed    BookingStatus = "failed"
)

// ServiceType represents the type of service being booked
type ServiceType string

const (
	ServiceTypeFlight ServiceType = "flight"
	ServiceTypeHotel  ServiceType = "hotel"
	ServiceTypeCar    ServiceType = "car"
)

// MasterBooking represents the master_bookings table
type MasterBooking struct {
	BaseModel
	BookingReference string        `gorm:"type:varchar(100);unique;not null;index" json:"booking_reference"`
	ServiceType      ServiceType   `gorm:"type:enum('flight','hotel','car');not null" json:"service_type"`
	Status           BookingStatus `gorm:"type:enum('pending','confirmed','cancelled','completed');default:'pending';not null" json:"status"`
	PaymentStatus    string        `gorm:"type:varchar(10);default:'unpaid';not null" json:"payment_status"`
	UserID           uint          `gorm:"index" json:"user_id"`
}

// TableName specifies the table name for the MasterBooking model
func (MasterBooking) TableName() string {
	return "master_bookings"
}

// FlightBooking represents the flight_bookings table
type FlightBooking struct {
	BaseModel
	MasterBookingID   string          `gorm:"type:varchar(36);not null;index" json:"master_booking_id"`
	ProviderInfo      json.RawMessage `gorm:"type:json;not null" json:"provider_info"`
	TUI               string          `gorm:"type:varchar(255);not null;index" json:"TUI"`
	Mode              string          `gorm:"type:varchar(50)" json:"Mode"`
	TransactionID     string          `gorm:"type:varchar(36);not null;index" json:"TransactionID"`
	ADT               int             `gorm:"not null" json:"ADT"`
	CHD               int             `gorm:"not null" json:"CHD"`
	INF               int             `gorm:"not null" json:"INF"`
	NetAmount         float64         `gorm:"not null" json:"NetAmount"`
	AirlineNetFare    float64         `gorm:"not null" json:"AirlineNetFare"`
	SSRAmount         float64         `gorm:"not null" json:"SSRAmount"`
	GrossAmount       float64         `gorm:"not null" json:"GrossAmount"`
	Hold              bool            `gorm:"not null" json:"Hold"`
	ActualHoldTime    int             `gorm:"not null" json:"ActualHoldTime"`
	ActualDisplayTime int             `gorm:"not null" json:"ActualDisplayTime"`
	ProviderBookingID string          `gorm:"type:varchar(100)" json:"provider_booking_id"`
	PNR               string          `gorm:"type:varchar(20)" json:"pnr"`
}

// TableName specifies the table name for the FlightBooking model
func (FlightBooking) TableName() string {
	return "flight_bookings"
}

// ContactInfo represents the contact_info table
type ContactInfo struct {
	BaseModel
	FlightBookingID string `gorm:"type:varchar(36);not null;index" json:"flight_booking_id"`
	Email           string `gorm:"type:varchar(100);not null" json:"email"`
	PhoneNumber     string `gorm:"type:varchar(20);not null" json:"phone_number"`
	CountryCode     string `gorm:"type:varchar(5);not null" json:"country_code"`
	Title           string `gorm:"type:varchar(10)" json:"title"`
	FirstName       string `gorm:"type:varchar(50);not null" json:"first_name"`
	LastName        string `gorm:"type:varchar(50);not null" json:"last_name"`
	Address         string `gorm:"type:text" json:"address"`
	City            string `gorm:"type:varchar(50)" json:"city"`
	Country         string `gorm:"type:varchar(50)" json:"country"`
	PostalCode      string `gorm:"type:varchar(20)" json:"postal_code"`
}

// TableName specifies the table name for the ContactInfo model
func (ContactInfo) TableName() string {
	return "contact_info"
}

// Traveller represents the travellers table
type Traveller struct {
	BaseModel
	FlightBookingID string     `gorm:"type:varchar(36);not null;index" json:"flight_booking_id"`
	Title           string     `gorm:"type:varchar(10);not null" json:"title"`
	FirstName       string     `gorm:"type:varchar(50);not null" json:"first_name"`
	LastName        string     `gorm:"type:varchar(50);not null" json:"last_name"`
	Gender          string     `gorm:"type:varchar(10);not null" json:"gender"`
	DateOfBirth     time.Time  `gorm:"not null" json:"date_of_birth"`
	PassportNumber  string     `gorm:"type:varchar(20)" json:"passport_number"`
	PassportExpiry  *time.Time `json:"passport_expiry"`
	Nationality     string     `gorm:"type:varchar(50)" json:"nationality"`
	PassengerType   string     `gorm:"type:varchar(10);not null" json:"passenger_type"`
	TicketNumber    string     `gorm:"type:varchar(50)" json:"ticket_number"`
	PNR             string     `gorm:"type:varchar(20)" json:"pnr"`
}

// TableName specifies the table name for the Traveller model
func (Traveller) TableName() string {
	return "travellers"
}

// BookingRequest represents the request payload for creating a booking (YAML compliant)
type BookingRequest struct {
	FlightBooking FlightBookingRequest `json:"flight_booking,omitempty"`
	Code          string               `json:"Code,omitempty"`
	Msg           []string             `json:"Msg,omitempty"`
	Travellers    []TravellerRequest   `json:"Travellers" validate:"required,min=1"`
	ContactInfo   ContactInfoRequest   `json:"ContactInfo,omitempty"`

	// Legacy fields for backward compatibility
	FareID string         `json:"FareId,omitempty"`
	TUI    string         `json:"TUI,omitempty"`
	Trips  []TripInfo     `json:"Trips,omitempty"`
	Extra  map[string]any `json:"extra,omitempty"`
}

// Validate performs custom validation for BookingRequest
func (br *BookingRequest) Validate() error {
	// Check if either new structure or legacy structure is provided
	hasNewStructure := br.FlightBooking.TUI != ""
	hasLegacyStructure := br.FareID != "" || br.TUI != ""

	if !hasNewStructure && !hasLegacyStructure {
		return fmt.Errorf("either flight_booking structure or legacy fields (FareId, TUI) must be provided")
	}

	// If using new structure, validate required fields
	if hasNewStructure {
		if br.FlightBooking.ProviderInfo.Code == "" {
			return fmt.Errorf("provider_info.code is required when using flight_booking structure")
		}
		if br.FlightBooking.TUI == "" {
			return fmt.Errorf("TUI is required in flight_booking structure")
		}
		if br.FlightBooking.NetAmount <= 0 {
			return fmt.Errorf("NetAmount must be greater than 0")
		}
		if br.FlightBooking.GrossAmount <= 0 {
			return fmt.Errorf("GrossAmount must be greater than 0")
		}
	}

	// Validate travellers
	if len(br.Travellers) == 0 {
		return fmt.Errorf("at least one traveller is required")
	}

	return nil
}

// FlightBookingRequest represents the flight booking details in the request
type FlightBookingRequest struct {
	ProviderInfo      ProviderInfoRequest `json:"provider_info" validate:"required"`
	TUI               string              `json:"TUI" validate:"required"`
	Mode              *string             `json:"Mode"`
	TransactionID     int64               `json:"TransactionID,omitempty"`
	ADT               int                 `json:"ADT" validate:"required"`
	CHD               int                 `json:"CHD"`
	INF               int                 `json:"INF"`
	NetAmount         float64             `json:"NetAmount" validate:"required"`
	AirlineNetFare    float64             `json:"AirlineNetFare" validate:"required"`
	SSRAmount         float64             `json:"SSRAmount"`
	CrossSellAmount   float64             `json:"CrossSellAmount"`
	GrossAmount       float64             `json:"GrossAmount" validate:"required"`
	Trips             []TripRequest       `json:"Trips" validate:"required"`
	Rules             []FareRuleRequest   `json:"Rules,omitempty"`
	SSR               []SSRRequest        `json:"SSR,omitempty"`
	CrossSell         []CrossSellRequest  `json:"CrossSell,omitempty"`
	Auxiliaries       []AuxiliaryRequest  `json:"Auxiliaries,omitempty"`
	Hold              bool                `json:"Hold"`
	ActualHoldTime    int                 `json:"ActualHoldTime"`
	ActualDisplayTime int                 `json:"ActualDisplayTime"`
	CeilingInfo       string              `json:"CeilingInfo,omitempty"`
}

// ProviderInfoRequest represents provider information in the request
type ProviderInfoRequest struct {
	Code string `json:"code" validate:"required"`
}

// TripRequest represents trip information in the request
type TripRequest struct {
	Journey []JourneyRequest `json:"Journey" validate:"required"`
}

// JourneyRequest represents journey information in the request
type JourneyRequest struct {
	Provider  string           `json:"Provider" validate:"required"`
	Stops     int              `json:"Stops"`
	Offer     string           `json:"Offer"`
	OrderID   int              `json:"OrderID"`
	Index     string           `json:"Index"`
	GrossFare float64          `json:"GrossFare"`
	NetFare   float64          `json:"NetFare"`
	CSBalance float64          `json:"CSBalance"`
	Promo     *string          `json:"Promo"`
	Segments  []SegmentRequest `json:"Segments" validate:"required"`
	Notices   *string          `json:"Notices"`
}

// SegmentRequest represents segment information in the request
type SegmentRequest struct {
	Flight         FlightRequest `json:"Flight" validate:"required"`
	Fares          FareRequest   `json:"Fares" validate:"required"`
	MulticityRefID *string       `json:"MulticityRefID"`
}

// FlightRequest represents flight information in the request
type FlightRequest struct {
	FUID              string  `json:"FUID" validate:"required"`
	VAC               string  `json:"VAC" validate:"required"`
	MAC               string  `json:"MAC" validate:"required"`
	OAC               string  `json:"OAC" validate:"required"`
	FareBasisCode     string  `json:"FareBasisCode"`
	Airline           string  `json:"Airline" validate:"required"`
	Aircraft          string  `json:"Aircraft"`
	FlightNo          string  `json:"FlightNo" validate:"required"`
	ArrivalTime       string  `json:"ArrivalTime"`
	DepartureTime     string  `json:"DepartureTime"`
	ArrivalCode       string  `json:"ArrivalCode"`
	DepartureCode     string  `json:"DepartureCode"`
	ArrAirportName    string  `json:"ArrAirportName" validate:"required"`
	DepAirportName    string  `json:"DepAirportName" validate:"required"`
	ArrivalTerminal   string  `json:"ArrivalTerminal"`
	DepartureTerminal string  `json:"DepartureTerminal"`
	EquipmentType     string  `json:"EquipmentType"`
	RBD               string  `json:"RBD"`
	Cabin             string  `json:"Cabin" validate:"required"`
	Refundable        string  `json:"Refundable"`
	Amenities         *string `json:"Amenities"`
	Duration          string  `json:"Duration" validate:"required"`
	PaxCategory       string  `json:"PaxCategory"`
	Hops              *string `json:"Hops"`
	VACAirlineLogo    string  `json:"VACAirlineLogo"`
	MACAirlineLogo    string  `json:"MACAirlineLogo"`
	OACAirlineLogo    string  `json:"OACAirlineLogo"`
}

// FareRequest represents fare information in the request
type FareRequest struct {
	PTCFare                  []PTCFareRequest `json:"PTCFare" validate:"required"`
	GrossFare                float64          `json:"GrossFare" validate:"required"`
	NetFare                  float64          `json:"NetFare" validate:"required"`
	TotalServiceTax          float64          `json:"TotalServiceTax"`
	TotalBaseFare            float64          `json:"TotalBaseFare"`
	TotalTax                 float64          `json:"TotalTax"`
	TotalCommission          float64          `json:"TotalCommission"`
	TotalVATonServiceCharge  float64          `json:"TotalVATonServiceCharge"`
	TotalVATonTransactionFee float64          `json:"TotalVATonTransactionFee"`
	TotalAgentMarkup         float64          `json:"TotalAgentMarkup"`
	TotalMarkup              float64          `json:"TotalMarkup"`
	TotalAtoCharge           float64          `json:"TotalAtoCharge"`
	TotalReissueCharge       float64          `json:"TotalReissueCharge"`
	DealKey                  *string          `json:"DealKey"`
}

// PTCFareRequest represents passenger type fare information in the request
type PTCFareRequest struct {
	PTC                 string  `json:"PTC" validate:"required"`
	Fare                float64 `json:"Fare" validate:"required"`
	YQ                  float64 `json:"YQ"`
	PSF                 float64 `json:"PSF"`
	YR                  float64 `json:"YR"`
	UD                  float64 `json:"UD"`
	K3                  float64 `json:"K3"`
	API                 float64 `json:"API"`
	OTT                 string  `json:"OTT"`
	OT                  string  `json:"OT"`
	Tax                 float64 `json:"Tax"`
	GrossFare           float64 `json:"GrossFare" validate:"required"`
	NetFare             float64 `json:"NetFare" validate:"required"`
	ST                  float64 `json:"ST"`
	CMS                 float64 `json:"CMS"`
	VATonServiceCharge  float64 `json:"VATonServiceCharge"`
	VATonTransactionFee float64 `json:"VATonTransactionFee"`
	AgentMarkup         float64 `json:"AgentMarkup"`
	Markup              float64 `json:"Markup"`
	AtoCharge           float64 `json:"AtoCharge"`
	ReissueCharge       float64 `json:"ReissueCharge"`
}

// FareRuleRequest represents fare rule information in the request
type FareRuleRequest struct {
	OrginDestination    string            `json:"OrginDestination" validate:"required"`
	FUID                string            `json:"FUID" validate:"required"`
	Provider            string            `json:"Provider" validate:"required"`
	FareRuleText        *string           `json:"FareRuleText"`
	Rule                []RuleInfoRequest `json:"Rule" validate:"required"`
	SpecialInformations string            `json:"SpecialInformations"`
}

// RuleInfoRequest represents rule information in the request
type RuleInfoRequest struct {
	Info []RuleDetailRequest `json:"Info" validate:"required"`
	Head string              `json:"Head" validate:"required"`
}

// RuleDetailRequest represents rule detail information in the request
type RuleDetailRequest struct {
	AdultAmount  string `json:"AdultAmount" validate:"required"`
	ChildAmount  string `json:"ChildAmount" validate:"required"`
	InfantAmount string `json:"InfantAmount" validate:"required"`
	Description  string `json:"Description" validate:"required"`
	RuleText     string `json:"RuleText"`
}

// SSRRequest represents SSR information in the request
type SSRRequest struct {
	PTC              string  `json:"PTC" validate:"required"`
	PaxId            string  `json:"PaxId" validate:"required"`
	FUID             string  `json:"FUID" validate:"required"`
	Code             string  `json:"Code" validate:"required"`
	Description      string  `json:"Description" validate:"required"`
	PieceDescription string  `json:"PieceDescription"`
	SSRCategory      string  `json:"SSRCategory"`
	Charge           float64 `json:"Charge"`
	Type             string  `json:"Type" validate:"required"`
	SSRUrl           *string `json:"SSRUrl"`
}

// CrossSellRequest represents cross-sell information in the request
type CrossSellRequest struct {
	Code          string `json:"Code" validate:"required"`
	TransactionID int64  `json:"TransactionID" validate:"required"`
}

// AuxiliaryRequest represents auxiliary information in the request
type AuxiliaryRequest struct {
	Code       *string `json:"Code"`
	EmployeeID *string `json:"EmployeeID"`
	Amount     float64 `json:"Amount"`
}

// TravellerRequest represents the request payload for traveller information (YAML compliant)
type TravellerRequest struct {
	ID            int                 `json:"ID,omitempty"`
	PaxID         int                 `json:"PaxID,omitempty"`
	Operation     string              `json:"Operation,omitempty"`
	Title         string              `json:"Title" validate:"required"`
	FName         string              `json:"FName" validate:"required"`
	LName         string              `json:"LName" validate:"required"`
	Age           int                 `json:"Age,omitempty"`
	DOB           string              `json:"DOB" validate:"required"`
	Country       string              `json:"Country,omitempty"`
	Gender        string              `json:"Gender" validate:"required,oneof=M F"`
	PTC           string              `json:"PTC" validate:"required,oneof=ADT CHD INF"`
	Nationality   string              `json:"Nationality,omitempty"`
	PassportNo    string              `json:"PassportNo,omitempty"`
	PLI           string              `json:"PLI,omitempty"`
	PDOE          string              `json:"PDOE,omitempty"`
	VisaType      string              `json:"VisaType,omitempty"`
	DOBDay        string              `json:"DOBDay,omitempty"`
	DOBMonth      string              `json:"DOBMonth,omitempty"`
	DOBYear       string              `json:"DOBYear,omitempty"`
	PDOEDay       string              `json:"PDOEDay,omitempty"`
	PDOEMonth     string              `json:"PDOEMonth,omitempty"`
	PDOEBYear     string              `json:"PDOEBYear,omitempty"`
	DefenceID     string              `json:"DefenceID,omitempty"`
	PaxCategoryID string              `json:"PaxCategoryID,omitempty"`
	DocType       string              `json:"DocType,omitempty"`
	ContactInfo   *ContactInfoRequest `json:"contactinfo,omitempty"`
	RoomReference string              `json:"room_reference,omitempty"`
	SSRInfo       []SSRInfoRequest    `json:"ssr_info,omitempty"`
	Address       string              `json:"address,omitempty"`
	HusbandName   *string             `json:"husband_name,omitempty"`
	HusbandNameAr *string             `json:"husband_name_ar,omitempty"`
	FatherName    string              `json:"father_name,omitempty"`
	FatherNameAr  *string             `json:"father_name_ar,omitempty"`
	MotherName    string              `json:"mother_name,omitempty"`
	MotherNameAr  *string             `json:"mother_name_ar,omitempty"`
	DateOfIssue   string              `json:"date_of_issue,omitempty"`
	BirthPlace    string              `json:"birth_place,omitempty"`
	BirthPlaceAr  *string             `json:"birth_place_ar,omitempty"`
	City          string              `json:"city,omitempty"`
	PassportType  string              `json:"passport_type,omitempty"`

	// Legacy fields for backward compatibility
	FirstName      string `json:"first_name,omitempty"`
	LastName       string `json:"last_name,omitempty"`
	DateOfBirth    string `json:"date_of_birth,omitempty"`
	PassportNumber string `json:"passport_number,omitempty"`
	PassportExpiry string `json:"passport_expiry,omitempty"`
	PassengerType  string `json:"passenger_type,omitempty"`
}

// SSRInfoRequest represents SSR information for travellers
type SSRInfoRequest struct {
	FUID   int    `json:"FUID" validate:"required"`
	SSID   int    `json:"SSID" validate:"required"`
	Charge string `json:"charge,omitempty"`
}

// ContactInfoRequest represents the request payload for contact information (YAML compliant)
type ContactInfoRequest struct {
	Title          string `json:"title,omitempty"`
	FirstName      string `json:"first_name" validate:"required"`
	LastName       string `json:"last_name" validate:"required"`
	PhoneNumber    string `json:"phone_number" validate:"required"`
	PhnCountryCode string `json:"phn_country_code" validate:"required"`
	Email          string `json:"email" validate:"required,email"`

	// Legacy fields for backward compatibility
	Mobile            string `json:"Mobile,omitempty"` // Frontend uses this field
	CountryCode       string `json:"country_code,omitempty"`
	MobileCountryCode string `json:"MobileCountryCode,omitempty"` // Frontend uses this field
	Address           string `json:"address,omitempty"`
	City              string `json:"city,omitempty"`
	Country           string `json:"country,omitempty"`
	PostalCode        string `json:"postal_code,omitempty"`
	State             string `json:"State,omitempty"`   // Frontend uses this field
	FName             string `json:"FName,omitempty"`   // Frontend uses this field
	LName             string `json:"LName,omitempty"`   // Frontend uses this field
	IsGuest           bool   `json:"IsGuest,omitempty"` // Frontend uses this field
}

// BookingResponse represents the response payload for booking operations
type BookingResponse struct {
	BookingReference  string         `json:"booking_reference"`
	Status            string         `json:"status"`
	PaymentStatus     string         `json:"payment_status"`
	TotalAmount       float64        `json:"total_amount"`
	Currency          string         `json:"currency"`
	BookingDetails    BookingDetails `json:"booking_details"`
	ProviderBookingID string         `json:"provider_booking_id,omitempty"`
	PNR               string         `json:"pnr,omitempty"`
	Extra             map[string]any `json:"extra,omitempty"`
}

// BookingDetails represents detailed booking information
type BookingDetails struct {
	FlightDetails []FlightSegment    `json:"flight_details"`
	Travellers    []TravellerDetails `json:"travellers"`
	ContactInfo   ContactInfoDetails `json:"contact_info"`
	PNR           string             `json:"pnr,omitempty"`
	TicketNumbers []string           `json:"ticket_numbers,omitempty"`
}

// TravellerDetails represents detailed traveller information in response
type TravellerDetails struct {
	Title         string `json:"title"`
	FirstName     string `json:"first_name"`
	LastName      string `json:"last_name"`
	Gender        string `json:"gender"`
	DateOfBirth   string `json:"date_of_birth"`
	PassengerType string `json:"passenger_type"`
	TicketNumber  string `json:"ticket_number,omitempty"`
	PNR           string `json:"pnr,omitempty"`
}

// ContactInfoDetails represents detailed contact information in response
type ContactInfoDetails struct {
	Email       string `json:"email"`
	PhoneNumber string `json:"phone_number"`
	CountryCode string `json:"country_code"`
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	Address     string `json:"address,omitempty"`
	City        string `json:"city,omitempty"`
	Country     string `json:"country,omitempty"`
}

// BookingListResponse represents the response for listing bookings
type BookingListResponse struct {
	Bookings []BookingResponse `json:"bookings"`
	Total    int               `json:"total"`
	Page     int               `json:"page"`
	Limit    int               `json:"limit"`
}
