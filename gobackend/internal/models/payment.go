package models

import (
	"time"
)

// PaymentStatus represents the status of a payment
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusRefunded  PaymentStatus = "refunded"
	PaymentStatusCancelled PaymentStatus = "cancelled"
)

// PaymentMethod represents different payment methods
type PaymentMethod string

const (
	PaymentMethodCard   PaymentMethod = "card"
	PaymentMethodUPI    PaymentMethod = "upi"
	PaymentMethodWallet PaymentMethod = "wallet"
	PaymentMethodNetBanking PaymentMethod = "netbanking"
)

// PaymentGateway represents different payment gateways
type PaymentGateway string

const (
	PaymentGatewayRazorpay PaymentGateway = "razorpay"
	PaymentGatewayPayU     PaymentGateway = "payu"
	PaymentGatewayPaytm    PaymentGateway = "paytm"
	PaymentGatewayCCAvenue PaymentGateway = "ccavenue"
)

// Payment represents the payments table
type Payment struct {
	BaseModel
	BookingReference  string         `gorm:"type:varchar(100);not null;index" json:"booking_reference"`
	PaymentID         string         `gorm:"type:varchar(100);unique;not null;index" json:"payment_id"`
	GatewayPaymentID  string         `gorm:"type:varchar(100);index" json:"gateway_payment_id"`
	Gateway           PaymentGateway `gorm:"type:varchar(50);not null" json:"gateway"`
	Method            PaymentMethod  `gorm:"type:varchar(50);not null" json:"method"`
	Status            PaymentStatus  `gorm:"type:varchar(50);not null;default:'pending'" json:"status"`
	Amount            float64        `gorm:"not null" json:"amount"`
	Currency          string         `gorm:"type:varchar(10);not null;default:'INR'" json:"currency"`
	UserID            uint           `gorm:"index" json:"user_id"`
	TransactionFee    float64        `gorm:"default:0" json:"transaction_fee"`
	NetAmount         float64        `gorm:"not null" json:"net_amount"`
	GatewayResponse   string         `gorm:"type:text" json:"gateway_response,omitempty"`
	FailureReason     string         `gorm:"type:text" json:"failure_reason,omitempty"`
	ProcessedAt       *time.Time     `json:"processed_at,omitempty"`
	RefundedAt        *time.Time     `json:"refunded_at,omitempty"`
	RefundAmount      float64        `gorm:"default:0" json:"refund_amount"`
}

// TableName specifies the table name for the Payment model
func (Payment) TableName() string {
	return "payments"
}

// PaymentRequest represents the request payload for initiating a payment
type PaymentRequest struct {
	BookingReference string        `json:"booking_reference" validate:"required"`
	Amount           float64       `json:"amount" validate:"required,gt=0"`
	Currency         string        `json:"currency" validate:"required"`
	Method           PaymentMethod `json:"method" validate:"required"`
	Gateway          PaymentGateway `json:"gateway" validate:"required"`
	CallbackURL      string        `json:"callback_url" validate:"required,url"`
	CancelURL        string        `json:"cancel_url" validate:"required,url"`
	CustomerInfo     CustomerInfo  `json:"customer_info" validate:"required"`
}

// CustomerInfo represents customer information for payment
type CustomerInfo struct {
	Name        string `json:"name" validate:"required"`
	Email       string `json:"email" validate:"required,email"`
	PhoneNumber string `json:"phone_number" validate:"required"`
	Address     string `json:"address,omitempty"`
}

// PaymentResponse represents the response payload for payment initiation
type PaymentResponse struct {
	PaymentID       string                 `json:"payment_id"`
	Status          string                 `json:"status"`
	Amount          float64                `json:"amount"`
	Currency        string                 `json:"currency"`
	Gateway         string                 `json:"gateway"`
	Method          string                 `json:"method"`
	PaymentURL      string                 `json:"payment_url,omitempty"`
	GatewayData     map[string]interface{} `json:"gateway_data,omitempty"`
	ExpiresAt       *time.Time             `json:"expires_at,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
}

// PaymentCallbackRequest represents the callback request from payment gateway
type PaymentCallbackRequest struct {
	PaymentID        string                 `json:"payment_id"`
	GatewayPaymentID string                 `json:"gateway_payment_id"`
	Status           string                 `json:"status"`
	Amount           float64                `json:"amount"`
	Currency         string                 `json:"currency"`
	Signature        string                 `json:"signature"`
	GatewayData      map[string]interface{} `json:"gateway_data"`
}

// PaymentStatusResponse represents the response for payment status queries
type PaymentStatusResponse struct {
	PaymentID        string                 `json:"payment_id"`
	BookingReference string                 `json:"booking_reference"`
	Status           string                 `json:"status"`
	Amount           float64                `json:"amount"`
	Currency         string                 `json:"currency"`
	Gateway          string                 `json:"gateway"`
	Method           string                 `json:"method"`
	TransactionFee   float64                `json:"transaction_fee"`
	NetAmount        float64                `json:"net_amount"`
	ProcessedAt      *time.Time             `json:"processed_at,omitempty"`
	FailureReason    string                 `json:"failure_reason,omitempty"`
	GatewayData      map[string]interface{} `json:"gateway_data,omitempty"`
	CreatedAt        time.Time              `json:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// RefundRequest represents the request payload for payment refund
type RefundRequest struct {
	PaymentID     string  `json:"payment_id" validate:"required"`
	Amount        float64 `json:"amount" validate:"required,gt=0"`
	Reason        string  `json:"reason" validate:"required"`
	RefundType    string  `json:"refund_type" validate:"required,oneof=full partial"`
	Notes         string  `json:"notes,omitempty"`
}

// RefundResponse represents the response payload for refund operations
type RefundResponse struct {
	RefundID         string    `json:"refund_id"`
	PaymentID        string    `json:"payment_id"`
	BookingReference string    `json:"booking_reference"`
	Amount           float64   `json:"amount"`
	Currency         string    `json:"currency"`
	Status           string    `json:"status"`
	Reason           string    `json:"reason"`
	ProcessedAt      time.Time `json:"processed_at"`
	EstimatedArrival string    `json:"estimated_arrival"`
}

// PaymentListResponse represents the response for listing payments
type PaymentListResponse struct {
	Payments []PaymentStatusResponse `json:"payments"`
	Total    int                     `json:"total"`
	Page     int                     `json:"page"`
	Limit    int                     `json:"limit"`
}

// GatewayConfig represents configuration for different payment gateways
type GatewayConfig struct {
	Razorpay RazorpayConfig `json:"razorpay"`
	PayU     PayUConfig     `json:"payu"`
	Paytm    PaytmConfig    `json:"paytm"`
}

// RazorpayConfig represents Razorpay gateway configuration
type RazorpayConfig struct {
	KeyID     string `json:"key_id"`
	KeySecret string `json:"key_secret"`
	Webhook   string `json:"webhook_secret"`
	BaseURL   string `json:"base_url"`
}

// PayUConfig represents PayU gateway configuration
type PayUConfig struct {
	MerchantKey string `json:"merchant_key"`
	Salt        string `json:"salt"`
	BaseURL     string `json:"base_url"`
}

// PaytmConfig represents Paytm gateway configuration
type PaytmConfig struct {
	MerchantID  string `json:"merchant_id"`
	MerchantKey string `json:"merchant_key"`
	Website     string `json:"website"`
	BaseURL     string `json:"base_url"`
}

// PaymentAnalytics represents payment analytics data
type PaymentAnalytics struct {
	TotalPayments     int64   `json:"total_payments"`
	TotalAmount       float64 `json:"total_amount"`
	SuccessfulPayments int64  `json:"successful_payments"`
	FailedPayments    int64   `json:"failed_payments"`
	PendingPayments   int64   `json:"pending_payments"`
	RefundedPayments  int64   `json:"refunded_payments"`
	SuccessRate       float64 `json:"success_rate"`
	AverageAmount     float64 `json:"average_amount"`
	Currency          string  `json:"currency"`
	Period            string  `json:"period"`
}

// PaymentMethodStats represents statistics by payment method
type PaymentMethodStats struct {
	Method      string  `json:"method"`
	Count       int64   `json:"count"`
	Amount      float64 `json:"amount"`
	SuccessRate float64 `json:"success_rate"`
}

// GatewayStats represents statistics by payment gateway
type GatewayStats struct {
	Gateway     string  `json:"gateway"`
	Count       int64   `json:"count"`
	Amount      float64 `json:"amount"`
	SuccessRate float64 `json:"success_rate"`
	AvgResponseTime float64 `json:"avg_response_time_ms"`
}

// PaymentWebhookEvent represents webhook events from payment gateways
type PaymentWebhookEvent struct {
	ID               string                 `json:"id"`
	Event            string                 `json:"event"`
	PaymentID        string                 `json:"payment_id"`
	GatewayPaymentID string                 `json:"gateway_payment_id"`
	Gateway          string                 `json:"gateway"`
	Status           string                 `json:"status"`
	Amount           float64                `json:"amount"`
	Currency         string                 `json:"currency"`
	Timestamp        time.Time              `json:"timestamp"`
	Data             map[string]interface{} `json:"data"`
	Signature        string                 `json:"signature"`
	Verified         bool                   `json:"verified"`
}
