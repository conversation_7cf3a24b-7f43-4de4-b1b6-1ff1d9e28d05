package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"gobackend/internal/models"
	"gobackend/pkg/auth"
)

// AuthMiddleware provides JWT authentication middleware
type AuthMiddleware struct {
	jwtManager *auth.JWTManager
}

// NewAuthMiddleware creates a new authentication middleware
func NewAuthMiddleware(jwtManager *auth.JWTManager) *AuthMiddleware {
	return &AuthMiddleware{
		jwtManager: jwtManager,
	}
}

// RequireAuth middleware that requires valid JWT token
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "Authorization header missing",
				Message: "Authentication required",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.<PERSON>(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "Invalid authorization header format",
				Message: "Authorization header must start with 'Bearer '",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Extract token
		tokenString := authHeader[7:] // Remove "Bearer " prefix

		// Validate token
		claims, err := m.jwtManager.ValidateToken(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, models.ErrorResponse{
				Error:   "Invalid or expired token",
				Message: err.Error(),
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user", claims)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)

		c.Next()
	}
}

// RequireRole middleware that requires specific user role
func (m *AuthMiddleware) RequireRole(requiredRole models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		// First check authentication
		m.RequireAuth()(c)
		if c.IsAborted() {
			return
		}

		// Get user role from context
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error:   "User role not found in context",
				Message: "Authentication error",
				Code:    http.StatusInternalServerError,
			})
			c.Abort()
			return
		}

		// Check if user has required role
		if userRole != string(requiredRole) {
			c.JSON(http.StatusForbidden, models.ErrorResponse{
				Error:   "Insufficient permissions",
				Message: "Access denied: required role not met",
				Code:    http.StatusForbidden,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin middleware that requires admin role
func (m *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return m.RequireRole(models.UserRoleAdmin)
}

// OptionalAuth middleware that optionally validates JWT token
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			// Invalid format, continue without authentication
			c.Next()
			return
		}

		// Extract token
		tokenString := authHeader[7:] // Remove "Bearer " prefix

		// Validate token
		claims, err := m.jwtManager.ValidateToken(tokenString)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Set user information in context if token is valid
		c.Set("user", claims)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("authenticated", true)

		c.Next()
	}
}

// GetCurrentUser helper function to get current user from context
func GetCurrentUser(c *gin.Context) (*auth.Claims, bool) {
	userInterface, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	claims, ok := userInterface.(*auth.Claims)
	if !ok {
		return nil, false
	}

	return claims, true
}

// GetCurrentUserID helper function to get current user ID from context
func GetCurrentUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}

	id, ok := userID.(uint)
	if !ok {
		return 0, false
	}

	return id, true
}

// IsAuthenticated helper function to check if user is authenticated
func IsAuthenticated(c *gin.Context) bool {
	authenticated, exists := c.Get("authenticated")
	if !exists {
		return false
	}

	isAuth, ok := authenticated.(bool)
	return ok && isAuth
}

// HasRole helper function to check if user has specific role
func HasRole(c *gin.Context, role models.UserRole) bool {
	userRole, exists := c.Get("user_role")
	if !exists {
		return false
	}

	roleStr, ok := userRole.(string)
	if !ok {
		return false
	}

	return roleStr == string(role)
}

// IsAdmin helper function to check if user is admin
func IsAdmin(c *gin.Context) bool {
	return HasRole(c, models.UserRoleAdmin)
}

// IsCustomer helper function to check if user is customer
func IsCustomer(c *gin.Context) bool {
	return HasRole(c, models.UserRoleCustomer)
}
