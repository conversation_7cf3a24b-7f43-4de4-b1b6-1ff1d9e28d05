package main

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	"gobackend/internal/database"
	"gobackend/internal/handlers"
	"gobackend/internal/middleware"
	"gobackend/internal/models"
	"gobackend/internal/services"
	"gobackend/pkg/auth"
	"gobackend/pkg/cache"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize database connections
	if err := initializeDatabase(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.DB.Close()

	// Initialize cache
	cacheService := initializeCache()

	// Initialize JWT manager
	jwtManager := initializeJWT()

	// Initialize services
	authService := services.NewAuthService()

	// Initialize flight service with TripJack configuration
	tripjackConfig := services.TripJackConfig{
		BaseURL:  getEnv("TRIPJACK_BASE_URL", "https://api.tripjack.com"),
		APIKey:   getEnv("TRIPJACK_API_KEY", ""),
		Username: getEnv("TRIPJACK_USERNAME", ""),
		Password: getEnv("TRIPJACK_PASSWORD", ""),
		Timeout:  time.Duration(getEnvAsInt("EXTERNAL_API_TIMEOUT_SECONDS", 30)) * time.Second,
	}

	flightCacheService := cache.NewFlightCacheService(cacheService)
	flightService := services.NewFlightService(flightCacheService, tripjackConfig)

	// Initialize booking service
	bookingService := services.NewBookingService(cacheService, flightService)

	// Initialize payment service with gateway configuration
	gatewayConfig := models.GatewayConfig{
		Razorpay: models.RazorpayConfig{
			KeyID:     getEnv("RAZORPAY_KEY_ID", ""),
			KeySecret: getEnv("RAZORPAY_KEY_SECRET", ""),
			Webhook:   getEnv("RAZORPAY_WEBHOOK_SECRET", ""),
			BaseURL:   getEnv("RAZORPAY_BASE_URL", "https://api.razorpay.com/v1"),
		},
		PayU: models.PayUConfig{
			MerchantKey: getEnv("PAYU_MERCHANT_KEY", ""),
			Salt:        getEnv("PAYU_SALT", ""),
			BaseURL:     getEnv("PAYU_BASE_URL", "https://secure.payu.in"),
		},
		Paytm: models.PaytmConfig{
			MerchantID:  getEnv("PAYTM_MERCHANT_ID", ""),
			MerchantKey: getEnv("PAYTM_MERCHANT_KEY", ""),
			Website:     getEnv("PAYTM_WEBSITE", "WEBSTAGING"),
			BaseURL:     getEnv("PAYTM_BASE_URL", "https://securegw-stage.paytm.in"),
		},
	}

	paymentService := services.NewPaymentService(cacheService, bookingService, gatewayConfig)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, jwtManager)
	flightHandler := handlers.NewFlightHandler(flightService)
	bookingHandler := handlers.NewBookingHandler(bookingService)
	paymentHandler := handlers.NewPaymentHandler(paymentService)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(jwtManager)

	// Setup router
	router := setupRouter(authHandler, flightHandler, bookingHandler, paymentHandler, authMiddleware)

	// Auto-migrate database tables
	if err := autoMigrate(); err != nil {
		log.Fatalf("Failed to auto-migrate database: %v", err)
	}

	// Start server
	port := getEnv("PORT", "8080")
	log.Printf("Starting server on port %s", port)
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func initializeDatabase() error {
	configs := map[string]database.DatabaseConfig{
		"auth": {
			Host:     getEnv("DATABASE_HOST", "localhost"),
			Port:     getEnvAsInt("DATABASE_PORT", 3306),
			User:     getEnv("DATABASE_USER", "root"),
			Password: getEnv("DATABASE_PASSWORD", ""),
			Database: getEnv("AUTH_DATABASE_NAME", "tdb_auth"),
			Charset:  "utf8mb4",
		},
		"flight": {
			Host:     getEnv("DATABASE_HOST", "localhost"),
			Port:     getEnvAsInt("DATABASE_PORT", 3306),
			User:     getEnv("DATABASE_USER", "root"),
			Password: getEnv("DATABASE_PASSWORD", ""),
			Database: getEnv("FLIGHT_DATABASE_NAME", "tdb_flight"),
			Charset:  "utf8mb4",
		},
		"booking": {
			Host:     getEnv("DATABASE_HOST", "localhost"),
			Port:     getEnvAsInt("DATABASE_PORT", 3306),
			User:     getEnv("DATABASE_USER", "root"),
			Password: getEnv("DATABASE_PASSWORD", ""),
			Database: getEnv("BOOKING_DATABASE_NAME", "tdb_booking"),
			Charset:  "utf8mb4",
		},
	}

	redisURL := getEnv("REDIS_URL", "redis://localhost:6379/0")
	return database.InitializeConnections(configs, redisURL)
}

func initializeCache() *cache.MultiLayerCache {
	cacheConfig := cache.CacheConfig{
		RedisClient:     database.DB.Redis,
		MemoryCacheTTL:  time.Minute * 15, // 15 minutes for L1 cache
		RedisCacheTTL:   time.Hour * 1,    // 1 hour for L2 cache
		CleanupInterval: time.Minute * 5,  // Cleanup every 5 minutes
	}

	return cache.NewMultiLayerCache(cacheConfig)
}

func initializeJWT() *auth.JWTManager {
	jwtConfig := auth.JWTConfig{
		SecretKey:       getEnv("JWT_SECRET_KEY", "your_jwt_secret_key"),
		ExpirationHours: getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
		Issuer:          getEnv("JWT_ISSUER", "fast-travel-backend"),
	}

	return auth.NewJWTManager(jwtConfig)
}

func setupRouter(authHandler *handlers.AuthHandler, flightHandler *handlers.FlightHandler, bookingHandler *handlers.BookingHandler, paymentHandler *handlers.PaymentHandler, authMiddleware *middleware.AuthMiddleware) *gin.Engine {
	// Set Gin mode
	if getEnv("GIN_MODE", "debug") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.Default()

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		// Get CORS configuration from environment variables
		allowedOriginsStr := getEnv("CORS_ALLOWED_ORIGINS", "*")
		allowedMethods := getEnv("CORS_ALLOWED_METHODS", "GET, POST, PUT, DELETE, OPTIONS")
		allowedHeaders := getEnv("CORS_ALLOWED_HEADERS", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		// Always set CORS headers
		origin := c.Request.Header.Get("Origin")

		if allowedOriginsStr == "*" {
			c.Header("Access-Control-Allow-Origin", "*")
		} else {
			// Split allowed origins and check if request origin is allowed
			allowedOrigins := strings.Split(allowedOriginsStr, ",")
			originAllowed := false
			for _, allowedOrigin := range allowedOrigins {
				if strings.TrimSpace(allowedOrigin) == origin {
					c.Header("Access-Control-Allow-Origin", origin)
					originAllowed = true
					break
				}
			}
			// If origin not found in allowed list, still set a default for development
			if !originAllowed && origin != "" {
				log.Printf("Origin not in allowed list: %s", origin)
				// For development, allow localhost origins
				if strings.Contains(origin, "localhost") || strings.Contains(origin, "127.0.0.1") {
					c.Header("Access-Control-Allow-Origin", origin)
				}
			}
		}

		c.Header("Access-Control-Allow-Methods", allowedMethods)
		c.Header("Access-Control-Allow-Headers", allowedHeaders)
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		health := database.DB.HealthCheck()
		c.JSON(200, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Format(time.RFC3339),
			"server":    "Fast Travel Backend Go",
			"version":   "1.0.0",
			"database":  health,
		})
	})

	// API routes (matching frontend flight-api-service.tsx exactly)
	api := router.Group("/apis")
	{
		// Auth routes (matching frontend expectations)
		auth := api.Group("/auth")
		{
			// Registration and login routes
			auth.POST("/register", authHandler.RegisterUser)
			auth.POST("/register/", authHandler.RegisterUser)
			auth.GET("/get_login_otp/:userId", authHandler.GetLoginOTP)
			auth.POST("/otp_verify", authHandler.VerifyOTP)
			auth.POST("/login", authHandler.LoginWithOTP) // Query params: email, otp

			// Legacy routes for compatibility
			auth.POST("/verify-otp", authHandler.VerifyOTP)
			auth.POST("/resend-otp", authHandler.ResendOTP)
			auth.POST("/refresh", authHandler.RefreshToken)

			// Protected auth routes
			protected := auth.Group("")
			protected.Use(authMiddleware.RequireAuth())
			{
				protected.GET("/profile", authHandler.GetUserProfile)
			}

			// Admin only routes
			admin := auth.Group("/admin")
			admin.Use(authMiddleware.RequireAdmin())
			{
				admin.GET("/users/:id", authHandler.GetUserByID)
			}
		}

		// Flight search and info routes (matching frontend exactly)
		api.POST("/airports", flightHandler.SearchAirports)             // Airport search
		api.POST("/airports/", flightHandler.SearchAirports)            // With trailing slash
		api.POST("/search", flightHandler.SearchFlights)                // Flight search
		api.POST("/search/", flightHandler.SearchFlights)               // With trailing slash
		api.POST("/search_list", flightHandler.GetCachedSearchResults)  // Cached search results
		api.POST("/search_list/", flightHandler.GetCachedSearchResults) // With trailing slash
		api.POST("/details", flightHandler.GetFlightDetails)            // Flight details
		api.POST("/details/", flightHandler.GetFlightDetails)           // With trailing slash
		api.POST("/service_req", flightHandler.GetSSRServices)          // SSR services
		api.POST("/service_req/", flightHandler.GetSSRServices)         // With trailing slash
		api.POST("/rules", flightHandler.GetFareRules)                  // Fare rules
		api.POST("/rules/", flightHandler.GetFareRules)                 // With trailing slash
		api.POST("/pricing", flightHandler.GetFlightPricing)            // Smart pricer
		api.POST("/pricing/", flightHandler.GetFlightPricing)           // With trailing slash
		api.POST("/pricing_list", flightHandler.GetPricingList)         // Pricing list
		api.POST("/pricing_list/", flightHandler.GetPricingList)        // With trailing slash

		// Additional flight services
		api.POST("/setup", flightHandler.GetWebSettings)             // Web settings
		api.POST("/setup/", flightHandler.GetWebSettings)            // With trailing slash
		api.POST("/rb", flightHandler.RetrieveBooking)               // Retrieve booking
		api.POST("/rb/", flightHandler.RetrieveBooking)              // With trailing slash
		api.POST("/fetchservice", flightHandler.GetTravelChecklist)  // Travel checklist
		api.POST("/fetchservice/", flightHandler.GetTravelChecklist) // With trailing slash
		api.POST("/seat", flightHandler.GetFlightSeat)               // Flight seat selection
		api.POST("/seat/", flightHandler.GetFlightSeat)              // With trailing slash

		// Airline specific routes
		airline := api.Group("/airline")
		{
			airline.POST("/SSR", flightHandler.GetAirlineSSR)  // Airline SSR
			airline.POST("/SSR/", flightHandler.GetAirlineSSR) // With trailing slash
		}

		// Booking routes (matching frontend exactly)
		api.POST("/create-booking", authMiddleware.RequireAuth(), bookingHandler.CreateBooking)
		api.POST("/create-booking/", authMiddleware.RequireAuth(), bookingHandler.CreateBooking)
		api.GET("/user-bookings", authMiddleware.RequireAuth(), bookingHandler.GetUserBookings)
		api.GET("/user-bookings/", authMiddleware.RequireAuth(), bookingHandler.GetUserBookings)
		api.GET("/get-booking/:reference", authMiddleware.RequireAuth(), bookingHandler.GetBookingByReference)

		// Legacy booking routes for compatibility
		api.GET("/get-all-bookings", authMiddleware.RequireAuth(), bookingHandler.GetUserBookings)
		api.GET("/get-all-bookings/", authMiddleware.RequireAuth(), bookingHandler.GetUserBookings)

		// Dashboard routes (admin access)
		dashboard := api.Group("/dashboard")
		dashboard.Use(authMiddleware.RequireAdmin())
		{
			dashboard.GET("/bookings/:id", bookingHandler.GetBookingByReference)
			dashboard.GET("/all-bookings", bookingHandler.GetUserBookings)
			dashboard.GET("/all-bookings/", bookingHandler.GetUserBookings)
		}

		// Standard booking routes (for compatibility)
		booking := api.Group("/booking")
		booking.Use(authMiddleware.RequireAuth())
		{
			booking.POST("/create", bookingHandler.CreateBooking)
			booking.GET("/list", bookingHandler.GetUserBookings)
			booking.GET("/:reference", bookingHandler.GetBookingByReference)
			booking.PUT("/:reference/cancel", bookingHandler.CancelBooking)

			// Admin only routes
			admin := booking.Group("/admin")
			admin.Use(authMiddleware.RequireAdmin())
			{
				admin.PUT("/:reference/status", bookingHandler.UpdateBookingStatus)
				admin.PUT("/:reference/payment-status", bookingHandler.UpdatePaymentStatus)
				admin.GET("/stats", bookingHandler.GetBookingStats)
			}
		}

		// Payment routes
		payment := api.Group("/payment")
		{
			// Public routes
			payment.POST("/initiate", authMiddleware.RequireAuth(), paymentHandler.InitiatePayment)
			payment.GET("/:payment_id/status", paymentHandler.GetPaymentStatus)

			// Gateway callbacks
			payment.POST("/callback/razorpay", paymentHandler.HandleRazorpayCallback)
			payment.POST("/callback/payu", paymentHandler.HandlePayUCallback)
			payment.POST("/callback/paytm", paymentHandler.HandlePaytmCallback)

			// Webhook endpoints
			payment.POST("/webhook/:gateway", paymentHandler.WebhookHandler)

			// Admin only routes
			admin := payment.Group("/admin")
			admin.Use(authMiddleware.RequireAdmin())
			{
				admin.POST("/refund", paymentHandler.ProcessRefund)
				admin.GET("/analytics", paymentHandler.GetPaymentAnalytics)
				admin.GET("/method-stats", paymentHandler.GetPaymentMethodStats)
				admin.GET("/gateway-stats", paymentHandler.GetGatewayStats)
			}
		}
	}

	return router
}

func autoMigrate() error {
	// Migrate auth database
	if err := database.DB.AuthDB.AutoMigrate(&models.User{}); err != nil {
		return err
	}

	// Migrate booking database
	if err := database.DB.BookingDB.AutoMigrate(
		&models.MasterBooking{},
		&models.FlightBooking{},
		&models.ContactInfo{},
		&models.Traveller{},
		&models.Payment{},
	); err != nil {
		return err
	}

	log.Println("Database migration completed successfully")
	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
