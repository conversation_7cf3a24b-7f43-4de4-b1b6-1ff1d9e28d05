package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type PricingRequest struct {
	Trips    []PricingTrip `json:"Trips"`
	ClientID string        `json:"ClientID"`
	Mode     string        `json:"Mode"`
	Options  string        `json:"Options"`
	Source   string        `json:"Source"`
	TripType string        `json:"TripType"`
}

type PricingTrip struct {
	Amount      float64 `json:"Amount"`
	Index       string  `json:"Index"`
	ChannelCode *string `json:"ChannelCode"`
	OrderID     int     `json:"OrderID"`
	TUI         string  `json:"TUI"`
}

func main() {
	fmt.Println("🧪 Testing New Pricing Response Structure")
	fmt.Println("============================================================")

	baseURL := "http://localhost:8080"

	// Test with a sample pricing request
	pricingReq := PricingRequest{
		Trips: []PricingTrip{
			{
				Amount:      4222.0,
				Index:       "4-3805767935_0DELBOMSG135~1022934387950645|0|TJ",
				ChannelCode: nil,
				OrderID:     1,
				TUI:         "flight_search_DEL_BOM_2025-05-28_1_0_0_E",
			},
		},
		ClientID: "",
		Mode:     "SS",
		Options:  "A",
		Source:   "SF",
		TripType: "O",
	}

	jsonData, err := json.Marshal(pricingReq)
	if err != nil {
		fmt.Printf("❌ Failed to marshal request: %v\n", err)
		return
	}

	fmt.Printf("📤 Request: %s\n", string(jsonData))
	fmt.Println("------------------------------------------------------------")

	resp, err := http.Post(baseURL+"/apis/pricing/", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ Request failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Failed to read response: %v\n", err)
		return
	}

	fmt.Printf("📥 Response Status: %d\n", resp.StatusCode)
	fmt.Printf("📥 Response Body: %s\n", string(body))

	if resp.StatusCode == 200 {
		// Parse the response to check structure
		var response map[string]interface{}
		if err := json.Unmarshal(body, &response); err != nil {
			fmt.Printf("❌ Failed to parse response: %v\n", err)
			return
		}

		fmt.Println("\n🔍 Response Structure Analysis:")
		fmt.Printf("   - TUI: %v\n", response["TUI"])
		fmt.Printf("   - ADT: %v\n", response["ADT"])
		fmt.Printf("   - CHD: %v\n", response["CHD"])
		fmt.Printf("   - INF: %v\n", response["INF"])
		fmt.Printf("   - NetAmount: %v\n", response["NetAmount"])
		fmt.Printf("   - GrossAmount: %v\n", response["GrossAmount"])

		if trips, ok := response["Trips"].([]interface{}); ok {
			fmt.Printf("   - Trips: Found %d trips ✅\n", len(trips))
			if len(trips) > 0 {
				if trip, ok := trips[0].(map[string]interface{}); ok {
					if journey, ok := trip["Journey"].([]interface{}); ok {
						fmt.Printf("   - Journey: Found %d journeys ✅\n", len(journey))
					}
				}
			}
		} else {
			fmt.Printf("   - Trips: Missing or invalid ❌\n")
		}

		// Check backward compatibility
		if pricingDetails, ok := response["PricingDetails"].(map[string]interface{}); ok {
			fmt.Printf("   - PricingDetails: Present for backward compatibility ✅\n")
			fmt.Printf("     - FareId: %v\n", pricingDetails["FareId"])
			fmt.Printf("     - TotalFare: %v\n", pricingDetails["TotalFare"])
		} else {
			fmt.Printf("   - PricingDetails: Missing ❌\n")
		}

		fmt.Println("\n✅ SUCCESS! New response structure is working!")
	} else {
		fmt.Printf("❌ Failed with status %d\n", resp.StatusCode)
	}
}
