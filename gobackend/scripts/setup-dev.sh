#!/bin/bash

# Fast Travel Backend Go - Development Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Fast Travel Backend Go Development Environment"
echo "============================================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Make is installed
if ! command -v make &> /dev/null; then
    echo "❌ Make is not installed. Please install Make first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Copy development environment file
echo "📝 Setting up environment configuration..."
if [ ! -f .env ]; then
    cp .env.development .env
    echo "✅ Copied .env.development to .env"
else
    echo "⚠️  .env file already exists, skipping copy"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p uploads logs tmp database redis monitoring
echo "✅ Directories created"

# Check if Go is installed (optional)
if command -v go &> /dev/null; then
    echo "✅ Go is installed: $(go version)"

    # Install development tools if Go is available
    echo "🔧 Installing Go development tools..."
    go install github.com/air-verse/air@latest
    go install github.com/go-delve/delve/cmd/dlv@latest
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
    go install github.com/swaggo/swag/cmd/swag@latest
    echo "✅ Go development tools installed"
else
    echo "⚠️  Go is not installed locally (Docker will be used for development)"
fi

# Build development environment
echo "🐳 Building development Docker environment..."
docker-compose -f docker-compose.dev.yml build

# Start development environment
echo "🚀 Starting development environment..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check MySQL
if docker-compose -f docker-compose.dev.yml exec mysql-dev mysqladmin ping -h localhost -u root -pVishnu123 --silent; then
    echo "✅ MySQL is ready"
else
    echo "❌ MySQL is not ready"
fi

# Check Redis
if docker-compose -f docker-compose.dev.yml exec redis-dev redis-cli ping | grep -q PONG; then
    echo "✅ Redis is ready"
else
    echo "❌ Redis is not ready"
fi

# Check Go backend
if curl -f http://localhost:8080/health &> /dev/null; then
    echo "✅ Go backend is ready"
else
    echo "⚠️  Go backend is starting up (may take a few more seconds)"
fi

echo ""
echo "🎉 Development environment setup complete!"
echo "============================================"
echo ""
echo "📋 Available Services:"
echo "  🔗 Go Backend:        http://localhost:8080"
echo "  🔗 Health Check:      http://localhost:8080/health"
echo "  🔗 API Documentation: http://localhost:8080/swagger/ (when available)"
echo ""
echo "🛠️  Development Tools (run 'make dev-tools-up' to start):"
echo "  🔗 phpMyAdmin:        http://localhost:8082"
echo "  🔗 Redis Commander:   http://localhost:8081"
echo "  🔗 Mailhog:           http://localhost:8025"
echo ""
echo "📊 Monitoring (run 'make dev-monitoring-up' to start):"
echo "  🔗 Prometheus:        http://localhost:9090"
echo "  🔗 Grafana:           http://localhost:3000 (admin/admin)"
echo ""
echo "🔧 Useful Commands:"
echo "  make dev-logs         # View application logs"
echo "  make dev-shell        # Access container shell"
echo "  make dev-restart      # Restart services"
echo "  make dev-db-reset     # Reset database with seed data"
echo "  make dev-cache-clear  # Clear Redis cache"
echo "  make test             # Run tests"
echo "  make help             # Show all available commands"
echo ""
echo "📚 Documentation:"
echo "  - Development Guide: ./DEVELOPMENT.md"
echo "  - API Documentation: ./README.md"
echo "  - Deployment Guide:  ./DEPLOYMENT_GUIDE.md"
echo ""
echo "🎯 Test the setup:"
echo "  curl http://localhost:8080/health"
echo ""
echo "Happy coding! 🚀"
