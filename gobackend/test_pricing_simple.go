package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

type PricingRequest struct {
	Trips    []PricingTrip `json:"Trips"`
	ClientID string        `json:"ClientID"`
	Mode     string        `json:"Mode"`
	Options  string        `json:"Options"`
	Source   string        `json:"Source"`
	TripType string        `json:"TripType"`
}

type PricingTrip struct {
	Amount      float64 `json:"Amount"`
	Index       string  `json:"Index"`
	ChannelCode *string `json:"ChannelCode"`
	OrderID     int     `json:"OrderID"`
	TUI         string  `json:"TUI"`
}

func main() {
	fmt.Println("🧪 Testing Pricing with Modified Flight Index")
	fmt.Println("============================================================")
	
	// Original index from your search result
	originalIndex := "4-3805767935_0DELBOMSG135~1022934387950645|0|TJ"
	
	// Extract core index (remove |0|TJ suffix)
	coreIndex := strings.Split(originalIndex, "|")[0]
	
	fmt.Printf("Original Index: %s\n", originalIndex)
	fmt.Printf("Core Index: %s\n", coreIndex)
	
	// Test both versions
	fmt.Println("\n🔍 Test 1: Using Original Index (with |0|TJ)")
	testPricing("http://localhost:8080", originalIndex, "Original")
	
	fmt.Println("\n🔍 Test 2: Using Core Index (without |0|TJ)")
	testPricing("http://localhost:8080", coreIndex, "Core")
	
	// Test with different variations
	fmt.Println("\n🔍 Test 3: Using Index with |44|TJ (common pattern)")
	modifiedIndex := coreIndex + "|44|TJ"
	testPricing("http://localhost:8080", modifiedIndex, "Modified")
	
	fmt.Println("\n💡 Analysis:")
	fmt.Println("   • Testing different index formats to find the correct one")
	fmt.Println("   • TripJack may expect specific suffixes")
	fmt.Println("   • The |0|TJ or |44|TJ suffix might be important")
}

func testPricing(baseURL, flightIndex, testName string) {
	pricingReq := PricingRequest{
		Trips: []PricingTrip{
			{
				Amount:      4222.0,
				Index:       flightIndex,
				ChannelCode: nil,
				OrderID:     1,
				TUI:         "flight_search_DEL_BOM_2025-05-28_1_0_0_E",
			},
		},
		ClientID: "",
		Mode:     "SS",
		Options:  "A",
		Source:   "SF",
		TripType: "O",
	}
	
	jsonData, err := json.Marshal(pricingReq)
	if err != nil {
		fmt.Printf("   ❌ %s: Failed to marshal request: %v\n", testName, err)
		return
	}
	
	fmt.Printf("   📤 %s Request: %s\n", testName, string(jsonData))
	
	resp, err := http.Post(baseURL+"/apis/pricing/", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("   ❌ %s: Request failed: %v\n", testName, err)
		return
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("   ❌ %s: Failed to read response: %v\n", testName, err)
		return
	}
	
	fmt.Printf("   📥 %s Response Status: %d\n", testName, resp.StatusCode)
	fmt.Printf("   📥 %s Response Body: %s\n", testName, string(body))
	
	if resp.StatusCode == 200 {
		fmt.Printf("   ✅ %s: SUCCESS!\n", testName)
	} else {
		fmt.Printf("   ❌ %s: Failed with status %d\n", testName, resp.StatusCode)
		
		// Try to extract error details
		var errorResp map[string]interface{}
		if json.Unmarshal(body, &errorResp) == nil {
			if message, ok := errorResp["message"].(string); ok {
				if strings.Contains(message, "not longer available") {
					fmt.Printf("   💡 %s: Flight expired (normal for test environment)\n", testName)
				} else if strings.Contains(message, "Invalid") {
					fmt.Printf("   💡 %s: Invalid format - try different index format\n", testName)
				}
			}
		}
	}
}
