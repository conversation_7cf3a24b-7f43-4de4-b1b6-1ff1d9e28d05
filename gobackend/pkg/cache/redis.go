package cache

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/redis/go-redis/v9"
)

// CacheLevel represents different cache levels
type CacheLevel int

const (
	L1Memory CacheLevel = iota
	L2Redis
	L3Persistent
)

// CacheStrategy represents different caching strategies
type CacheStrategy int

const (
	WriteThrough CacheStrategy = iota
	WriteBehind
	WriteAround
)

// MultiLayerCache implements a multi-layer caching system
type MultiLayerCache struct {
	memoryCache *cache.Cache
	redisClient *redis.Client
	ctx         context.Context

	// Cache TTL settings
	cacheTTL map[CacheLevel]time.Duration

	// Statistics
	stats CacheStats
}

// CacheStats holds cache performance statistics
type CacheStats struct {
	Hits          int64 `json:"hits"`
	Misses        int64 `json:"misses"`
	L1Hits        int64 `json:"l1_hits"`
	L2Hits        int64 `json:"l2_hits"`
	Writes        int64 `json:"writes"`
	Evictions     int64 `json:"evictions"`
	TotalRequests int64 `json:"total_requests"`
}

// CacheConfig holds cache configuration
type CacheConfig struct {
	RedisClient     *redis.Client
	MemoryCacheTTL  time.Duration
	RedisCacheTTL   time.Duration
	CleanupInterval time.Duration
}

// NewMultiLayerCache creates a new multi-layer cache instance
func NewMultiLayerCache(config CacheConfig) *MultiLayerCache {
	memCache := cache.New(config.MemoryCacheTTL, config.CleanupInterval)

	cacheTTL := map[CacheLevel]time.Duration{
		L1Memory:     config.MemoryCacheTTL,
		L2Redis:      config.RedisCacheTTL,
		L3Persistent: 24 * time.Hour, // 24 hours for persistent cache
	}

	return &MultiLayerCache{
		memoryCache: memCache,
		redisClient: config.RedisClient,
		ctx:         context.Background(),
		cacheTTL:    cacheTTL,
		stats:       CacheStats{},
	}
}

// GenerateCacheKey generates a consistent cache key from request data
func GenerateCacheKey(prefix string, data interface{}, excludeKeys ...string) string {
	// Convert data to map for consistent key generation
	dataBytes, _ := json.Marshal(data)
	var dataMap map[string]interface{}
	json.Unmarshal(dataBytes, &dataMap)

	// Remove excluded keys
	for _, key := range excludeKeys {
		delete(dataMap, key)
	}

	// Sort keys for consistency
	keys := make([]string, 0, len(dataMap))
	for k := range dataMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Build sorted key-value string
	var parts []string
	for _, k := range keys {
		value, _ := json.Marshal(dataMap[k])
		parts = append(parts, fmt.Sprintf("%s:%s", k, string(value)))
	}

	// Generate hash
	hash := sha256.Sum256([]byte(strings.Join(parts, "|")))
	return fmt.Sprintf("%s:%x", prefix, hash)
}

// Get retrieves data from cache with multi-layer lookup
func (c *MultiLayerCache) Get(key string) (interface{}, bool) {
	c.stats.TotalRequests++

	// L1 Cache: Check memory cache first
	if value, found := c.memoryCache.Get(key); found {
		c.stats.Hits++
		c.stats.L1Hits++
		return value, true
	}

	// L2 Cache: Check Redis cache (only if Redis client is available)
	if c.redisClient != nil {
		value, err := c.redisClient.Get(c.ctx, key).Result()
		if err == nil {
			// Deserialize and populate L1 cache
			var data interface{}
			if err := json.Unmarshal([]byte(value), &data); err == nil {
				c.memoryCache.Set(key, data, c.cacheTTL[L1Memory])
				c.stats.Hits++
				c.stats.L2Hits++
				return data, true
			}
		}
	}

	// Cache miss
	c.stats.Misses++
	return nil, false
}

// Set stores data in cache using specified strategy
func (c *MultiLayerCache) Set(key string, value interface{}, strategy CacheStrategy) error {
	c.stats.Writes++

	switch strategy {
	case WriteThrough:
		return c.writeThrough(key, value)
	case WriteBehind:
		return c.writeBehind(key, value)
	case WriteAround:
		return c.writeAround(key, value)
	default:
		return c.writeThrough(key, value)
	}
}

// writeThrough writes to both L1 and L2 simultaneously
func (c *MultiLayerCache) writeThrough(key string, value interface{}) error {
	// Write to L1 (memory cache)
	c.memoryCache.Set(key, value, c.cacheTTL[L1Memory])

	// Write to L2 (Redis cache) only if Redis client is available
	if c.redisClient != nil {
		data, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal data: %w", err)
		}

		return c.redisClient.Set(c.ctx, key, data, c.cacheTTL[L2Redis]).Err()
	}

	return nil
}

// writeBehind writes to L1 immediately, L2 asynchronously
func (c *MultiLayerCache) writeBehind(key string, value interface{}) error {
	// Write to L1 immediately
	c.memoryCache.Set(key, value, c.cacheTTL[L1Memory])

	// Write to L2 asynchronously only if Redis client is available
	if c.redisClient != nil {
		go func() {
			data, err := json.Marshal(value)
			if err != nil {
				return
			}
			c.redisClient.Set(c.ctx, key, data, c.cacheTTL[L2Redis])
		}()
	}

	return nil
}

// writeAround writes only to L2, bypassing L1
func (c *MultiLayerCache) writeAround(key string, value interface{}) error {
	// Only write to L2 if Redis client is available
	if c.redisClient != nil {
		data, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal data: %w", err)
		}

		return c.redisClient.Set(c.ctx, key, data, c.cacheTTL[L2Redis]).Err()
	}

	return nil
}

// Delete removes data from all cache layers
func (c *MultiLayerCache) Delete(key string) error {
	// Remove from L1
	c.memoryCache.Delete(key)

	// Remove from L2 only if Redis client is available
	if c.redisClient != nil {
		return c.redisClient.Del(c.ctx, key).Err()
	}

	return nil
}

// Clear clears all cache layers
func (c *MultiLayerCache) Clear() error {
	// Clear L1
	c.memoryCache.Flush()

	// Clear L2 (Redis) only if Redis client is available - be careful with this in production
	if c.redisClient != nil {
		return c.redisClient.FlushDB(c.ctx).Err()
	}

	return nil
}

// GetStats returns cache performance statistics
func (c *MultiLayerCache) GetStats() CacheStats {
	return c.stats
}

// GetHitRatio calculates cache hit ratio
func (c *MultiLayerCache) GetHitRatio() float64 {
	if c.stats.TotalRequests == 0 {
		return 0.0
	}
	return float64(c.stats.Hits) / float64(c.stats.TotalRequests)
}

// Exists checks if a key exists in any cache layer
func (c *MultiLayerCache) Exists(key string) bool {
	// Check L1
	if _, found := c.memoryCache.Get(key); found {
		return true
	}

	// Check L2 only if Redis client is available
	if c.redisClient != nil {
		exists := c.redisClient.Exists(c.ctx, key).Val()
		return exists > 0
	}

	return false
}

// GetTTL returns the TTL for a key in Redis
func (c *MultiLayerCache) GetTTL(key string) time.Duration {
	if c.redisClient != nil {
		ttl := c.redisClient.TTL(c.ctx, key).Val()
		return ttl
	}
	return 0
}

// SetTTL updates the TTL for a key in Redis
func (c *MultiLayerCache) SetTTL(key string, ttl time.Duration) error {
	if c.redisClient != nil {
		return c.redisClient.Expire(c.ctx, key, ttl).Err()
	}
	return nil
}

// GetKeysByPattern returns keys matching a pattern from Redis
func (c *MultiLayerCache) GetKeysByPattern(pattern string) ([]string, error) {
	if c.redisClient != nil {
		return c.redisClient.Keys(c.ctx, pattern).Result()
	}
	return []string{}, nil
}

// FlightCacheService provides flight-specific caching operations
type FlightCacheService struct {
	cache *MultiLayerCache
}

// NewFlightCacheService creates a new flight cache service
func NewFlightCacheService(cache *MultiLayerCache) *FlightCacheService {
	return &FlightCacheService{
		cache: cache,
	}
}

// GetFlightSearchResults retrieves flight search results from cache
func (f *FlightCacheService) GetFlightSearchResults(requestData interface{}) (interface{}, bool) {
	key := GenerateCacheKey("flight_search", requestData)
	return f.cache.Get(key)
}

// SetFlightSearchResults stores flight search results in cache
func (f *FlightCacheService) SetFlightSearchResults(requestData, results interface{}, strategy CacheStrategy) error {
	key := GenerateCacheKey("flight_search", requestData)
	return f.cache.Set(key, results, strategy)
}

// GetFlightDetails retrieves flight details from cache
func (f *FlightCacheService) GetFlightDetails(fareID string, requestData interface{}) (interface{}, bool) {
	key := GenerateCacheKey("flight_details", map[string]interface{}{
		"fare_id": fareID,
		"request": requestData,
	})
	return f.cache.Get(key)
}

// SetFlightDetails stores flight details in cache with shorter TTL
func (f *FlightCacheService) SetFlightDetails(fareID string, requestData, details interface{}) error {
	key := GenerateCacheKey("flight_details", map[string]interface{}{
		"fare_id": fareID,
		"request": requestData,
	})
	return f.cache.Set(key, details, WriteThrough)
}

// GetFlightSearchResultsByTUI retrieves flight search results using TUI as key (matches Python search_list)
func (f *FlightCacheService) GetFlightSearchResultsByTUI(tui string) (interface{}, bool) {
	// In Python, TUI is used directly as the cache key for search results
	return f.cache.Get(tui)
}

// SetFlightSearchResultsByTUI stores flight search results using TUI as key (matches Python caching)
func (f *FlightCacheService) SetFlightSearchResultsByTUI(tui string, results interface{}) error {
	// In Python, TUI is used directly as the cache key for search results
	return f.cache.Set(tui, results, WriteThrough)
}
