package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/hibiken/asynq"
)

// TaskType represents different types of background tasks
type TaskType string

const (
	TaskTypeFlightSearch      TaskType = "flight:search"
	TaskTypeFlightPricing     TaskType = "flight:pricing"
	TaskTypeCacheWarm         TaskType = "cache:warm"
	TaskTypeEmailNotification TaskType = "email:notification"
	TaskTypeSMSNotification   TaskType = "sms:notification"
	TaskTypeDataCleanup       TaskType = "data:cleanup"
)

// JobManager manages background job processing
type JobManager struct {
	client *asynq.Client
	server *asynq.Server
	mux    *asynq.ServeMux
	config JobConfig
}

// JobConfig holds job processing configuration
type JobConfig struct {
	RedisAddr     string
	Concurrency   int
	QueueName     string
	RetryAttempts int
	Timeout       time.Duration
}

// TaskPayload represents the payload for background tasks
type TaskPayload struct {
	Type      TaskType               `json:"type"`
	Data      map[string]interface{} `json:"data"`
	RequestID string                 `json:"request_id"`
	UserID    uint                   `json:"user_id,omitempty"`
	Priority  int                    `json:"priority"`
	CreatedAt time.Time              `json:"created_at"`
}

// NewJobManager creates a new job manager
func NewJobManager(config JobConfig) *JobManager {
	redisOpt := asynq.RedisClientOpt{
		Addr: config.RedisAddr,
	}

	client := asynq.NewClient(redisOpt)

	server := asynq.NewServer(redisOpt, asynq.Config{
		Concurrency: config.Concurrency,
		Queues: map[string]int{
			"critical": 6,
			"default":  3,
			"low":      1,
		},
		RetryDelayFunc: func(n int, e error, t *asynq.Task) time.Duration {
			return time.Duration(n) * time.Second
		},
		ErrorHandler: asynq.ErrorHandlerFunc(func(ctx context.Context, task *asynq.Task, err error) {
			log.Printf("Task %s failed: %v", task.Type(), err)
		}),
	})

	mux := asynq.NewServeMux()

	return &JobManager{
		client: client,
		server: server,
		mux:    mux,
		config: config,
	}
}

// RegisterHandlers registers task handlers
func (jm *JobManager) RegisterHandlers() {
	jm.mux.HandleFunc(string(TaskTypeFlightSearch), jm.handleFlightSearchTask)
	jm.mux.HandleFunc(string(TaskTypeFlightPricing), jm.handleFlightPricingTask)
	jm.mux.HandleFunc(string(TaskTypeCacheWarm), jm.handleCacheWarmTask)
	jm.mux.HandleFunc(string(TaskTypeEmailNotification), jm.handleEmailNotificationTask)
	jm.mux.HandleFunc(string(TaskTypeSMSNotification), jm.handleSMSNotificationTask)
	jm.mux.HandleFunc(string(TaskTypeDataCleanup), jm.handleDataCleanupTask)
}

// Start starts the job processing server
func (jm *JobManager) Start() error {
	jm.RegisterHandlers()
	log.Printf("Starting job manager with %d workers", jm.config.Concurrency)
	return jm.server.Run(jm.mux)
}

// Stop stops the job processing server
func (jm *JobManager) Stop() {
	jm.server.Shutdown()
	jm.client.Close()
}

// EnqueueTask enqueues a new background task
func (jm *JobManager) EnqueueTask(taskType TaskType, payload map[string]interface{}, opts ...TaskOption) error {
	taskPayload := TaskPayload{
		Type:      taskType,
		Data:      payload,
		RequestID: generateRequestID(),
		Priority:  1,
		CreatedAt: time.Now(),
	}

	// Apply options
	for _, opt := range opts {
		opt(&taskPayload)
	}

	payloadBytes, err := json.Marshal(taskPayload)
	if err != nil {
		return fmt.Errorf("failed to marshal task payload: %w", err)
	}

	task := asynq.NewTask(string(taskType), payloadBytes)

	// Set task options
	taskOpts := []asynq.Option{
		asynq.MaxRetry(jm.config.RetryAttempts),
		asynq.Timeout(jm.config.Timeout),
	}

	// Set queue based on priority
	switch taskPayload.Priority {
	case 3:
		taskOpts = append(taskOpts, asynq.Queue("critical"))
	case 2:
		taskOpts = append(taskOpts, asynq.Queue("default"))
	default:
		taskOpts = append(taskOpts, asynq.Queue("low"))
	}

	_, err = jm.client.Enqueue(task, taskOpts...)
	if err != nil {
		return fmt.Errorf("failed to enqueue task: %w", err)
	}

	log.Printf("Enqueued task %s with request ID %s", taskType, taskPayload.RequestID)
	return nil
}

// TaskOption represents options for configuring tasks
type TaskOption func(*TaskPayload)

// WithUserID sets the user ID for the task
func WithUserID(userID uint) TaskOption {
	return func(tp *TaskPayload) {
		tp.UserID = userID
	}
}

// WithPriority sets the priority for the task (1=low, 2=normal, 3=high)
func WithPriority(priority int) TaskOption {
	return func(tp *TaskPayload) {
		tp.Priority = priority
	}
}

// WithRequestID sets a custom request ID for the task
func WithRequestID(requestID string) TaskOption {
	return func(tp *TaskPayload) {
		tp.RequestID = requestID
	}
}

// Task Handlers

// handleFlightSearchTask handles flight search background tasks
func (jm *JobManager) handleFlightSearchTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing flight search task: %s", payload.RequestID)

	// Extract search parameters
	_, ok := payload.Data["search_params"]
	if !ok {
		return fmt.Errorf("search_params not found in payload")
	}

	// Here you would call the flight service to perform the search
	// For now, simulate processing
	time.Sleep(2 * time.Second)

	log.Printf("Completed flight search task: %s", payload.RequestID)
	return nil
}

// handleFlightPricingTask handles flight pricing background tasks
func (jm *JobManager) handleFlightPricingTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing flight pricing task: %s", payload.RequestID)

	// Extract pricing parameters
	fareID, ok := payload.Data["fare_id"].(string)
	if !ok {
		return fmt.Errorf("fare_id not found in payload")
	}

	// Here you would call the flight service to get pricing
	// For now, simulate processing
	time.Sleep(1 * time.Second)

	log.Printf("Completed flight pricing task for fare %s: %s", fareID, payload.RequestID)
	return nil
}

// handleCacheWarmTask handles cache warming background tasks
func (jm *JobManager) handleCacheWarmTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing cache warm task: %s", payload.RequestID)

	// Extract cache warming parameters
	cacheKeys, ok := payload.Data["cache_keys"].([]interface{})
	if !ok {
		return fmt.Errorf("cache_keys not found in payload")
	}

	// Here you would warm the specified cache keys
	// For now, simulate processing
	for _, key := range cacheKeys {
		log.Printf("Warming cache key: %v", key)
		time.Sleep(100 * time.Millisecond)
	}

	log.Printf("Completed cache warm task: %s", payload.RequestID)
	return nil
}

// handleEmailNotificationTask handles email notification tasks
func (jm *JobManager) handleEmailNotificationTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing email notification task: %s", payload.RequestID)

	// Extract email parameters
	email, ok := payload.Data["email"].(string)
	if !ok {
		return fmt.Errorf("email not found in payload")
	}

	subject, ok := payload.Data["subject"].(string)
	if !ok {
		return fmt.Errorf("subject not found in payload")
	}

	// Here you would send the actual email
	// For now, simulate sending
	log.Printf("Sending email to %s with subject: %s", email, subject)
	time.Sleep(500 * time.Millisecond)

	log.Printf("Completed email notification task: %s", payload.RequestID)
	return nil
}

// handleSMSNotificationTask handles SMS notification tasks
func (jm *JobManager) handleSMSNotificationTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing SMS notification task: %s", payload.RequestID)

	// Extract SMS parameters
	phoneNumber, ok := payload.Data["phone_number"].(string)
	if !ok {
		return fmt.Errorf("phone_number not found in payload")
	}

	message, ok := payload.Data["message"].(string)
	if !ok {
		return fmt.Errorf("message not found in payload")
	}

	// Here you would send the actual SMS
	// For now, simulate sending
	log.Printf("Sending SMS to %s: %s", phoneNumber, message)
	time.Sleep(300 * time.Millisecond)

	log.Printf("Completed SMS notification task: %s", payload.RequestID)
	return nil
}

// handleDataCleanupTask handles data cleanup tasks
func (jm *JobManager) handleDataCleanupTask(ctx context.Context, t *asynq.Task) error {
	var payload TaskPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("failed to unmarshal payload: %w", err)
	}

	log.Printf("Processing data cleanup task: %s", payload.RequestID)

	// Extract cleanup parameters
	dataType, ok := payload.Data["data_type"].(string)
	if !ok {
		return fmt.Errorf("data_type not found in payload")
	}

	// Here you would perform the actual cleanup
	// For now, simulate cleanup
	log.Printf("Cleaning up data type: %s", dataType)
	time.Sleep(5 * time.Second)

	log.Printf("Completed data cleanup task: %s", payload.RequestID)
	return nil
}

// Helper functions

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// GetQueueStats returns statistics about job queues
func (jm *JobManager) GetQueueStats() (map[string]interface{}, error) {
	inspector := asynq.NewInspector(asynq.RedisClientOpt{
		Addr: jm.config.RedisAddr,
	})
	defer inspector.Close()

	stats := make(map[string]interface{})

	// Get queue info for each queue
	queues := []string{"critical", "default", "low"}
	for _, queue := range queues {
		queueInfo, err := inspector.GetQueueInfo(queue)
		if err != nil {
			log.Printf("Failed to get queue info for %s: %v", queue, err)
			continue
		}

		stats[queue] = map[string]interface{}{
			"active":    queueInfo.Active,
			"pending":   queueInfo.Pending,
			"scheduled": queueInfo.Scheduled,
			"retry":     queueInfo.Retry,
			"archived":  queueInfo.Archived,
			"completed": queueInfo.Completed,
			"processed": queueInfo.Processed,
			"failed":    queueInfo.Failed,
		}
	}

	return stats, nil
}
