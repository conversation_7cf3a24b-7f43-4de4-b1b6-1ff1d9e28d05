#!/bin/bash

# Complete Booking Flow Test Script
# Tests the full TripJack Hold & Confirm booking flow with real APIs

set -e  # Exit on any error

BASE_URL="http://localhost:8080"
AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************.b_JicXY7bJnk2qP07sJjzrt54uNasyZ-ju7DFJVa6Fg"

echo "🚀 Starting Complete Booking Flow Test"
echo "======================================"

# Step 1: Flight Search (BOM to DEL on June 1st, 2025)
echo ""
echo "📍 Step 1: Searching flights BOM → DEL on 2025-06-01"
echo "---------------------------------------------------"

SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/apis/search/" \
  -H "Content-Type: application/json" \
  -d '{
    "FareType": "Regular",
    "ADT": 1,
    "CHD": 0,
    "INF": 0,
    "Cabin": "E",
    "Trips": [{
      "From": "BOM",
      "To": "DEL", 
      "OnwardDate": "2025-06-01"
    }]
  }')

echo "Search Response:"
echo "$SEARCH_RESPONSE" | jq '.'

# Extract TUI and first flight details from search response
TUI=$(echo "$SEARCH_RESPONSE" | jq -r '.TUI // empty')
FIRST_FLIGHT_INDEX=$(echo "$SEARCH_RESPONSE" | jq -r '.Trips[0].Journey[0].Index // empty')
FIRST_FLIGHT_FARE=$(echo "$SEARCH_RESPONSE" | jq -r '.Trips[0].Journey[0].Fares.GrossFare // 0')
AIRLINE_CODE=$(echo "$SEARCH_RESPONSE" | jq -r '.Trips[0].Journey[0].Segments[0].Flight.VAC // "1G"')
FLIGHT_NO=$(echo "$SEARCH_RESPONSE" | jq -r '.Trips[0].Journey[0].Segments[0].Flight.FlightNo // "252"')
AIRLINE_NAME=$(echo "$SEARCH_RESPONSE" | jq -r '.Trips[0].Journey[0].Segments[0].Flight.Airline // "SpiceJet"')

echo ""
echo "✅ Search Results:"
echo "   TUI: $TUI"
echo "   First Flight Index: $FIRST_FLIGHT_INDEX"
echo "   Fare: ₹$FIRST_FLIGHT_FARE"
echo "   Airline: $AIRLINE_NAME ($AIRLINE_CODE) $FLIGHT_NO"

if [ -z "$TUI" ] || [ -z "$FIRST_FLIGHT_INDEX" ]; then
    echo "❌ Error: Search failed or returned invalid data"
    exit 1
fi

# Step 2: Create Booking with Hold & Confirm Flow
echo ""
echo "📝 Step 2: Creating booking with Hold & Confirm flow"
echo "---------------------------------------------------"

BOOKING_RESPONSE=$(curl -s -X POST "$BASE_URL/apis/create-booking/" \
  -H "Accept: application/json, text/plain, */*" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"flight_booking\": {
      \"provider_info\": {\"code\": \"$AIRLINE_CODE\"},
      \"TUI\": \"$TUI\",
      \"ADT\": 1,
      \"CHD\": 0,
      \"INF\": 0,
      \"NetAmount\": $FIRST_FLIGHT_FARE,
      \"Trips\": [{
        \"Journey\": [{
          \"Provider\": \"$AIRLINE_CODE\",
          \"Stops\": 0,
          \"Index\": \"$FIRST_FLIGHT_INDEX\",
          \"Segments\": [{
            \"Flight\": {
              \"FUID\": \"1\",
              \"VAC\": \"$AIRLINE_CODE\",
              \"MAC\": \"$AIRLINE_CODE\",
              \"OAC\": \"$AIRLINE_CODE\",
              \"Airline\": \"$AIRLINE_NAME\",
              \"FlightNo\": \"$FLIGHT_NO\",
              \"ArrivalTime\": \"\",
              \"DepartureTime\": \"\",
              \"ArrivalCode\": \"DEL\",
              \"DepartureCode\": \"BOM\",
              \"Duration\": \"2h 30m\",
              \"FareBasisCode\": \"\",
              \"ArrAirportName\": \"Delhi Indira Gandhi Intl\",
              \"DepAirportName\": \"Chhatrapati Shivaji\",
              \"RBD\": \"\",
              \"Cabin\": \"ECONOMY\",
              \"Refundable\": \"N\"
            },
            \"Fares\": {\"GrossFare\": $FIRST_FLIGHT_FARE, \"NetFare\": $FIRST_FLIGHT_FARE}
          }]
        }]
      }],
      \"AirlineNetFare\": $FIRST_FLIGHT_FARE,
      \"SSRAmount\": 0,
      \"CrossSellAmount\": 0,
      \"GrossAmount\": $FIRST_FLIGHT_FARE,
      \"Hold\": true,
      \"ActualHoldTime\": 0,
      \"ActualDisplayTime\": 0
    },
    \"Travellers\": [{
      \"ID\": 1,
      \"PaxID\": 1,
      \"Title\": \"Mr\",
      \"FName\": \"ALI\",
      \"LName\": \"K\",
      \"Age\": 25,
      \"DOB\": \"1989-05-15\",
      \"Gender\": \"M\",
      \"PTC\": \"adult\",
      \"PLI\": \"New York\",
      \"PDOE\": \"2030-12-31\",
      \"Nationality\": \"US\",
      \"PassportNo\": \"A123456789\",
      \"VisaType\": null,
      \"DocType\": \"Other\"
    }],
    \"ContactInfo\": {
      \"Title\": \"Mr\",
      \"FName\": \"ALI\",
      \"LName\": \"K\",
      \"Mobile\": \"09321116965\",
      \"Phone\": null,
      \"Email\": \"<EMAIL>\",
      \"Address\": \"123 Elm street, New York , NY ,USA\",
      \"CountryCode\": \"+91\",
      \"MobileCountryCode\": \"+91\",
      \"State\": \"NY\",
      \"City\": \"New York\",
      \"PIN\": null,
      \"GSTAddress\": null,
      \"GSTCompanyName\": null,
      \"GSTTIN\": null,
      \"UpdateProfile\": false,
      \"IsGuest\": true,
      \"SaveGST\": false,
      \"Language\": null
    }
  }")

echo "Booking Response:"
echo "$BOOKING_RESPONSE" | jq '.'

# Extract booking details
BOOKING_REFERENCE=$(echo "$BOOKING_RESPONSE" | jq -r '.booking_reference // empty')
BOOKING_STATUS=$(echo "$BOOKING_RESPONSE" | jq -r '.status // empty')
PROVIDER_BOOKING_ID=$(echo "$BOOKING_RESPONSE" | jq -r '.provider_booking_id // empty')
PNR=$(echo "$BOOKING_RESPONSE" | jq -r '.pnr // empty')

echo ""
echo "✅ Booking Results:"
echo "   Booking Reference: $BOOKING_REFERENCE"
echo "   Status: $BOOKING_STATUS"
echo "   Provider Booking ID: $PROVIDER_BOOKING_ID"
echo "   PNR: $PNR"

if [ -z "$BOOKING_REFERENCE" ]; then
    echo "❌ Error: Booking failed"
    echo "Response: $BOOKING_RESPONSE"
    exit 1
fi

echo ""
echo "🎉 Complete Booking Flow Test Completed Successfully!"
echo "===================================================="
echo "✅ Flight Search: SUCCESS"
echo "✅ Booking Creation (Hold): SUCCESS"
echo ""
echo "📋 Summary:"
echo "   Route: BOM → DEL (2025-06-01)"
echo "   Airline: $AIRLINE_NAME ($AIRLINE_CODE) $FLIGHT_NO"
echo "   Fare: ₹$FIRST_FLIGHT_FARE"
echo "   Booking Reference: $BOOKING_REFERENCE"
echo "   Status: $BOOKING_STATUS (HELD)"
echo ""
echo "🔄 Next Steps for Complete Flow:"
echo "   1. ✅ Search flights (COMPLETED)"
echo "   2. ✅ Hold booking (COMPLETED)"
echo "   3. 💳 Process payment (MANUAL)"
echo "   4. ✅ Confirm booking (READY)"
echo "   5. 🎫 Issue tickets (READY)"
