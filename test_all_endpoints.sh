#!/bin/bash

# Comprehensive API Endpoint Testing Script
# Tests all flight API endpoints and monitors TripJack responses

BASE_URL="http://localhost:8080/apis"
CONTENT_TYPE="Content-Type: application/json"

echo "🚀 Starting comprehensive API endpoint testing..."
echo "Base URL: $BASE_URL"
echo "=================================================="

# Test data
SEARCH_BODY='{
  "FareType": "REGULAR",
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "Trips": [
    {
      "From": "DEL",
      "To": "BOM",
      "OnwardDate": "2025-06-12"
    }
  ]
}'

AIRPORT_SEARCH_BODY='{
  "query": "Delhi"
}'

WEB_SETTINGS_BODY='{
  "ClientID": "",
  "TUI": ""
}'

# Function to test endpoint
test_endpoint() {
    local endpoint=$1
    local method=$2
    local body=$3
    local description=$4

    echo ""
    echo "🔍 Testing: $description"
    echo "Endpoint: $method $endpoint"
    echo "----------------------------------------"

    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}" \
            -X POST "$BASE_URL$endpoint" \
            -H "$CONTENT_TYPE" \
            -d "$body")
    else
        response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\nTIME:%{time_total}" \
            -X GET "$BASE_URL$endpoint")
    fi

    # Extract HTTP status and time
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    time_total=$(echo "$response" | grep "TIME:" | cut -d: -f2)

    # Remove status and time from response body
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d' | sed '/TIME:/d')

    echo "Status: $http_status"
    echo "Time: ${time_total}s"

    if [ "$http_status" = "200" ]; then
        echo "✅ SUCCESS"
        # Show first 200 chars of response
        echo "Response preview: $(echo "$response_body" | head -c 200)..."
    else
        echo "❌ FAILED"
        echo "Response: $response_body"
    fi
}

# 1. Flight Search Endpoints (TripJack API calls)
echo ""
echo "🛫 FLIGHT SEARCH ENDPOINTS (TripJack API)"
echo "=================================================="

test_endpoint "/search" "POST" "$SEARCH_BODY" "Flight Search"
test_endpoint "/search/" "POST" "$SEARCH_BODY" "Flight Search (with trailing slash)"

# Get TUI from search response for subsequent tests
echo ""
echo "🔄 Getting TUI for subsequent tests..."
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search" -H "$CONTENT_TYPE" -d "$SEARCH_BODY")
TUI=$(echo "$SEARCH_RESPONSE" | grep -o '"TUI":"[^"]*"' | cut -d'"' -f4)
echo "Extracted TUI: $TUI"

if [ -n "$TUI" ]; then
    SEARCH_LIST_BODY="{\"TUI\": \"$TUI\"}"
    test_endpoint "/search_list" "POST" "$SEARCH_LIST_BODY" "Cached Search Results"
    test_endpoint "/search_list/" "POST" "$SEARCH_LIST_BODY" "Cached Search Results (with trailing slash)"
fi

# 2. Airport Search
echo ""
echo "🏢 AIRPORT SEARCH ENDPOINTS"
echo "=================================================="

test_endpoint "/airports" "POST" "$AIRPORT_SEARCH_BODY" "Airport Search"
test_endpoint "/airports/" "POST" "$AIRPORT_SEARCH_BODY" "Airport Search (with trailing slash)"

# 3. Flight Details and Pricing (TripJack API calls)
echo ""
echo "💰 FLIGHT PRICING ENDPOINTS (TripJack API)"
echo "=================================================="

# Extract a fare ID from search results for pricing tests
if [ -n "$SEARCH_RESPONSE" ]; then
    FARE_ID=$(echo "$SEARCH_RESPONSE" | grep -o '"Index":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "Extracted Fare ID: $FARE_ID"

    if [ -n "$FARE_ID" ]; then
        PRICING_BODY="{
          \"FareId\": \"$FARE_ID\",
          \"TUI\": \"$TUI\",
          \"ADT\": 1,
          \"CHD\": 0,
          \"INF\": 0
        }"

        test_endpoint "/pricing" "POST" "$PRICING_BODY" "Flight Pricing"
        test_endpoint "/pricing/" "POST" "$PRICING_BODY" "Flight Pricing (with trailing slash)"
        test_endpoint "/pricing_list" "POST" "$PRICING_BODY" "Pricing List"
        test_endpoint "/pricing_list/" "POST" "$PRICING_BODY" "Pricing List (with trailing slash)"

        DETAILS_BODY="{
          \"FareId\": \"$FARE_ID\",
          \"TUI\": \"$TUI\"
        }"

        test_endpoint "/details" "POST" "$DETAILS_BODY" "Flight Details"
        test_endpoint "/details/" "POST" "$DETAILS_BODY" "Flight Details (with trailing slash)"
    fi
fi

# 4. Service Requests and Rules
echo ""
echo "🛎️ SERVICE AND RULES ENDPOINTS"
echo "=================================================="

if [ -n "$FARE_ID" ] && [ -n "$TUI" ]; then
    SERVICE_BODY="{
      \"FareId\": \"$FARE_ID\",
      \"TUI\": \"$TUI\"
    }"

    test_endpoint "/service_req" "POST" "$SERVICE_BODY" "SSR Services"
    test_endpoint "/service_req/" "POST" "$SERVICE_BODY" "SSR Services (with trailing slash)"
    test_endpoint "/rules" "POST" "$SERVICE_BODY" "Fare Rules"
    test_endpoint "/rules/" "POST" "$SERVICE_BODY" "Fare Rules (with trailing slash)"
fi

# 5. Web Settings and Configuration
echo ""
echo "⚙️ CONFIGURATION ENDPOINTS"
echo "=================================================="

test_endpoint "/setup" "POST" "$WEB_SETTINGS_BODY" "Web Settings"
test_endpoint "/setup/" "POST" "$WEB_SETTINGS_BODY" "Web Settings (with trailing slash)"

# 6. Additional Services
echo ""
echo "🔧 ADDITIONAL SERVICE ENDPOINTS"
echo "=================================================="

RETRIEVE_BOOKING_BODY="{\"BookingReference\": \"TEST123\"}"
test_endpoint "/rb" "POST" "$RETRIEVE_BOOKING_BODY" "Retrieve Booking"
test_endpoint "/rb/" "POST" "$RETRIEVE_BOOKING_BODY" "Retrieve Booking (with trailing slash)"

TRAVEL_CHECKLIST_BODY="{\"destination\": \"BOM\"}"
test_endpoint "/fetchservice" "POST" "$TRAVEL_CHECKLIST_BODY" "Travel Checklist"
test_endpoint "/fetchservice/" "POST" "$TRAVEL_CHECKLIST_BODY" "Travel Checklist (with trailing slash)"

SEAT_BODY="{\"FlightID\": \"TEST123\"}"
test_endpoint "/seat" "POST" "$SEAT_BODY" "Flight Seat Selection"
test_endpoint "/seat/" "POST" "$SEAT_BODY" "Flight Seat Selection (with trailing slash)"

# 7. Airline Specific Services
echo ""
echo "✈️ AIRLINE SPECIFIC ENDPOINTS"
echo "=================================================="

AIRLINE_SSR_BODY="{\"AirlineCode\": \"6E\"}"
test_endpoint "/airline/SSR" "POST" "$AIRLINE_SSR_BODY" "Airline SSR"
test_endpoint "/airline/SSR/" "POST" "$AIRLINE_SSR_BODY" "Airline SSR (with trailing slash)"

echo ""
echo "🏁 Testing completed!"
echo "=================================================="
echo "Check the Go backend logs for detailed TripJack API call information."
