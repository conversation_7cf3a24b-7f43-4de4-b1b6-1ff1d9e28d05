-- =============================================================================
-- FAST TRAVEL BACKEND GO - DEVELOPMENT SEED DATA
-- =============================================================================

-- Use auth database for user data
USE tdb_auth;

-- Insert test users
INSERT INTO users (email, name, phone_number, phone_country_code, role, is_verified, otp, created_at, updated_at) VALUES
('<EMAIL>', 'Admin User', '9876543210', '+91', 'admin', true, '', NOW(), NOW()),
('<EMAIL>', 'Test Customer', '**********', '+91', 'customer', true, '', NOW(), NOW()),
('<EMAIL>', '<PERSON>', '**********', '+91', 'customer', true, '', NOW(), NOW()),
('<EMAIL>', 'Jane Smith', '9876543213', '+91', 'customer', false, '123456', NOW(), NOW()),
('<EMAIL>', 'Test User', '**********', '+91', 'customer', true, '', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Use booking database for booking and payment data
USE tdb_booking;

-- Insert test master bookings
INSERT INTO master_bookings (id, booking_reference, service_type, status, payment_status, user_id, created_at, updated_at) VALUES
(1, 'BOOK_DEV_001', 'flight', 'confirmed', 'paid', 2, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, 'BOOK_DEV_002', 'flight', 'pending', 'unpaid', 3, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, 'BOOK_DEV_003', 'flight', 'confirmed', 'paid', 5, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(4, 'BOOK_DEV_004', 'flight', 'cancelled', 'refunded', 2, DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(5, 'BOOK_DEV_005', 'flight', 'pending', 'unpaid', 3, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Insert test flight bookings
INSERT INTO flight_bookings (id, master_booking_id, provider_info, TUI, Mode, TransactionID, ADT, CHD, INF, NetAmount, AirlineNetFare, SSRAmount, GrossAmount, Hold, ActualHoldTime, ActualDisplayTime, created_at, updated_at) VALUES
(1, '1', '{"provider": "tripjack", "fare_id": "TEST_FARE_001"}', 'TUI_DEV_001', 'Online', 'TXN_DEV_001', 2, 0, 0, 4500.00, 4500.00, 200.00, 4700.00, false, 0, 30, DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, '2', '{"provider": "tripjack", "fare_id": "TEST_FARE_002"}', 'TUI_DEV_002', 'Online', 'TXN_DEV_002', 1, 1, 0, 3200.00, 3200.00, 150.00, 3350.00, false, 0, 30, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, '3', '{"provider": "tripjack", "fare_id": "TEST_FARE_003"}', 'TUI_DEV_003', 'Online', 'TXN_DEV_003', 1, 0, 0, 2800.00, 2800.00, 100.00, 2900.00, false, 0, 30, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(4, '4', '{"provider": "tripjack", "fare_id": "TEST_FARE_004"}', 'TUI_DEV_004', 'Online', 'TXN_DEV_004', 3, 0, 1, 8500.00, 8500.00, 300.00, 8800.00, false, 0, 30, DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(5, '5', '{"provider": "tripjack", "fare_id": "TEST_FARE_005"}', 'TUI_DEV_005', 'Online', 'TXN_DEV_005', 2, 0, 0, 5200.00, 5200.00, 250.00, 5450.00, true, 900, 30, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Insert test contact information
INSERT INTO contact_info (id, flight_booking_id, email, phone_number, country_code, title, first_name, last_name, address, city, country, postal_code, created_at, updated_at) VALUES
(1, '1', '<EMAIL>', '**********', '+91', 'Mr', 'Test', 'Customer', '123 Test Street', 'Mumbai', 'India', '400001', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, '2', '<EMAIL>', '**********', '+91', 'Mr', 'John', 'Doe', '456 Sample Road', 'Delhi', 'India', '110001', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, '3', '<EMAIL>', '**********', '+91', 'Ms', 'Test', 'User', '789 Demo Lane', 'Bangalore', 'India', '560001', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(4, '4', '<EMAIL>', '**********', '+91', 'Mr', 'Test', 'Customer', '123 Test Street', 'Mumbai', 'India', '400001', DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(5, '5', '<EMAIL>', '**********', '+91', 'Mr', 'John', 'Doe', '456 Sample Road', 'Delhi', 'India', '110001', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Insert test travellers
INSERT INTO travellers (id, flight_booking_id, title, first_name, last_name, gender, date_of_birth, passport_number, passport_expiry, nationality, passenger_type, ticket_number, pnr, created_at, updated_at) VALUES
(1, '1', 'Mr', 'Test', 'Customer', 'male', '1985-06-15', 'A1234567', '2030-06-15', 'Indian', 'ADT', 'TKT001', 'PNR001', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, '1', 'Mrs', 'Test', 'Spouse', 'female', '1987-08-20', 'A1234568', '2030-08-20', 'Indian', 'ADT', 'TKT002', 'PNR001', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(3, '2', 'Mr', 'John', 'Doe', 'male', '1990-03-10', 'B2345678', '2029-03-10', 'Indian', 'ADT', 'TKT003', 'PNR002', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(4, '2', 'Master', 'John', 'Junior', 'male', '2015-12-05', '', NULL, 'Indian', 'CHD', 'TKT004', 'PNR002', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(5, '3', 'Ms', 'Test', 'User', 'female', '1992-11-25', 'C3456789', '2031-11-25', 'Indian', 'ADT', 'TKT005', 'PNR003', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(6, '4', 'Mr', 'Test', 'Customer', 'male', '1985-06-15', 'A1234567', '2030-06-15', 'Indian', 'ADT', 'TKT006', 'PNR004', DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(7, '4', 'Mrs', 'Test', 'Spouse', 'female', '1987-08-20', 'A1234568', '2030-08-20', 'Indian', 'ADT', 'TKT007', 'PNR004', DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(8, '4', 'Master', 'Test', 'Child', 'male', '2018-04-12', '', NULL, 'Indian', 'CHD', 'TKT008', 'PNR004', DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY)),
(9, '5', 'Mr', 'John', 'Doe', 'male', '1990-03-10', 'B2345678', '2029-03-10', 'Indian', 'ADT', '', '', NOW(), NOW()),
(10, '5', 'Mrs', 'Jane', 'Doe', 'female', '1992-07-18', 'B2345679', '2029-07-18', 'Indian', 'ADT', '', '', NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- Insert test payments
INSERT INTO payments (id, booking_reference, payment_id, gateway_payment_id, gateway, method, status, amount, currency, user_id, transaction_fee, net_amount, gateway_response, processed_at, created_at, updated_at) VALUES
(1, 'BOOK_DEV_001', 'PAY_DEV_001', 'rzp_test_001', 'razorpay', 'card', 'completed', 4700.00, 'INR', 2, 94.00, 4606.00, '{"razorpay_payment_id": "rzp_test_001", "status": "captured"}', DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY), DATE_SUB(NOW(), INTERVAL 7 DAY)),
(2, 'BOOK_DEV_002', 'PAY_DEV_002', '', 'razorpay', 'upi', 'pending', 3350.00, 'INR', 3, 67.00, 3283.00, '{}', NULL, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
(3, 'BOOK_DEV_003', 'PAY_DEV_003', 'rzp_test_003', 'razorpay', 'card', 'completed', 2900.00, 'INR', 5, 58.00, 2842.00, '{"razorpay_payment_id": "rzp_test_003", "status": "captured"}', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(4, 'BOOK_DEV_004', 'PAY_DEV_004', 'rzp_test_004', 'razorpay', 'netbanking', 'refunded', 8800.00, 'INR', 2, 176.00, 8624.00, '{"razorpay_payment_id": "rzp_test_004", "status": "refunded"}', DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 14 DAY), DATE_SUB(NOW(), INTERVAL 13 DAY))
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- =============================================================================
-- DEVELOPMENT HELPER VIEWS
-- =============================================================================

-- Create view for easy booking overview
CREATE OR REPLACE VIEW booking_overview AS
SELECT 
    mb.booking_reference,
    mb.service_type,
    mb.status as booking_status,
    mb.payment_status,
    u.email as customer_email,
    u.name as customer_name,
    fb.GrossAmount as total_amount,
    fb.TUI,
    ci.phone_number,
    COUNT(t.id) as traveller_count,
    mb.created_at as booking_date
FROM master_bookings mb
LEFT JOIN tdb_auth.users u ON mb.user_id = u.id
LEFT JOIN flight_bookings fb ON mb.id = fb.master_booking_id
LEFT JOIN contact_info ci ON fb.id = ci.flight_booking_id
LEFT JOIN travellers t ON fb.id = t.flight_booking_id
GROUP BY mb.id;

-- Create view for payment summary
CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    p.payment_id,
    p.booking_reference,
    p.gateway,
    p.method,
    p.status,
    p.amount,
    p.transaction_fee,
    p.net_amount,
    p.processed_at,
    mb.status as booking_status,
    u.email as customer_email
FROM payments p
LEFT JOIN master_bookings mb ON p.booking_reference = mb.booking_reference
LEFT JOIN tdb_auth.users u ON p.user_id = u.id;

-- =============================================================================
-- DEVELOPMENT STATISTICS
-- =============================================================================

-- Show development data summary
SELECT 'Development Seed Data Summary' as info;
SELECT 'Users' as table_name, COUNT(*) as count FROM tdb_auth.users;
SELECT 'Master Bookings' as table_name, COUNT(*) as count FROM master_bookings;
SELECT 'Flight Bookings' as table_name, COUNT(*) as count FROM flight_bookings;
SELECT 'Contact Info' as table_name, COUNT(*) as count FROM contact_info;
SELECT 'Travellers' as table_name, COUNT(*) as count FROM travellers;
SELECT 'Payments' as table_name, COUNT(*) as count FROM payments;
