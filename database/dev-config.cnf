# MySQL Development Configuration
# Optimized for development environment with better logging and performance

[mysqld]
# Basic Settings
default-authentication-plugin=mysql_native_password
skip-host-cache
skip-name-resolve

# Character Set
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Memory Settings (Optimized for development)
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_log_buffer_size=16M
key_buffer_size=32M
table_open_cache=400
sort_buffer_size=2M
read_buffer_size=2M
read_rnd_buffer_size=8M
myisam_sort_buffer_size=64M
thread_cache_size=8
query_cache_size=32M
query_cache_limit=2M

# Connection Settings
max_connections=100
max_connect_errors=100000
max_allowed_packet=64M
interactive_timeout=28800
wait_timeout=28800

# Logging (Enhanced for development)
general_log=1
general_log_file=/var/log/mysql/general.log
log_error=/var/log/mysql/error.log
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=1
log_queries_not_using_indexes=1
log_slow_admin_statements=1

# Binary Logging (Disabled for development)
skip-log-bin

# InnoDB Settings
innodb_file_per_table=1
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT
innodb_lock_wait_timeout=120

# SQL Mode (Relaxed for development)
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Performance Schema (Enabled for monitoring)
performance_schema=ON
performance_schema_max_table_instances=400
performance_schema_max_table_handles=4000

# Time Zone
default-time-zone='+05:30'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
