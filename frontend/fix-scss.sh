#!/bin/bash

# <PERSON><PERSON>t to fix SCSS deprecation warnings
# This script will:
# 1. Replace @import with @use
# 2. Add sass:color import where needed
# 3. Replace deprecated color functions

echo "Starting SCSS fixes..."

# Find all SCSS files
find . -name "*.scss" -type f | while read file; do
    echo "Processing: $file"
    
    # Check if file contains @import
    if grep -q "@import" "$file"; then
        # Create backup
        cp "$file" "$file.bak"
        
        # Check if it imports variable.scss
        if grep -q "@import.*variable\.scss" "$file"; then
            # Replace @import with @use for variable.scss
            sed -i '' 's/@import.*variable\.scss.*/@use sass:color;\
@use '\''..\/..\/..\/..\/..\/..\/styles\/variable.scss'\'' as *;/g' "$file"
            
            # Fix relative paths based on file location
            if [[ "$file" == *"/Components/"* ]]; then
                # For deeply nested component files
                sed -i '' 's/@use '\''..\/..\/..\/..\/..\/..\/styles\/variable.scss'\'' as \*;/@use '\''..\/..\/..\/..\/..\/..\/styles\/variable.scss'\'' as *;/g' "$file"
            elif [[ "$file" == *"/app/flight/"* ]]; then
                # For flight app files
                sed -i '' 's/@use '\''..\/..\/..\/..\/..\/..\/styles\/variable.scss'\'' as \*;/@use '\''..\/..\/..\/styles\/variable.scss'\'' as *;/g' "$file"
            elif [[ "$file" == *"/components/"* ]]; then
                # For components files
                sed -i '' 's/@use '\''..\/..\/..\/..\/..\/..\/styles\/variable.scss'\'' as \*;/@use '\''..\/..\/styles\/variable.scss'\'' as *;/g' "$file"
            fi
        fi
        
        # Replace other @import statements with @use
        sed -i '' 's/@import \(.*\);/@use \1;/g' "$file"
        
        # Replace deprecated color functions
        sed -i '' 's/darken(\$\([^,]*\), \([^)]*\))/color.scale($\1, $lightness: -\2)/g' "$file"
        sed -i '' 's/lighten(\$\([^,]*\), \([^)]*\))/color.scale($\1, $lightness: \2)/g' "$file"
        
        echo "Fixed: $file"
    fi
done

echo "SCSS fixes completed!"
