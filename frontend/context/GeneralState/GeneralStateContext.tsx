import React, { createContext, useContext} from 'react';

// Define the types for the state variables
interface GeneralStateContextType {
  isUnder950 : boolean;
  setIsUnder950: React.Dispatch<React.SetStateAction<boolean>>;
  globalRateFlight: number;
  setGlobalRateFlight: React.Dispatch<React.SetStateAction<number>>;
  globalRate: number;
  setGlobalRate: React.Dispatch<React.SetStateAction<number>>;
  globalDecimel: number;
  setGlobalDecimel: React.Dispatch<React.SetStateAction<number>>;
  globalCurrency: string;
  setGlobalCurrency: React.Dispatch<React.SetStateAction<string>>;
  globalCurrencySymbol: string;
  setGlobalCurrencySymbol: React.Dispatch<React.SetStateAction<string>>;
  countryCode: string;
  setCountryCode: React.Dispatch<React.SetStateAction<string>>;
  globelFlightRate: number;
  setGlobelFlightRate: React.Dispatch<React.SetStateAction<number>>;
  globelFlightDecimel: number;
  setGlobelFlightDecimel: React.Dispatch<React.SetStateAction<number>>;
  isUserLoggedIn: boolean;
  setIsUserLoggedIn: React.Dispatch<React.SetStateAction<boolean>>;
  userName: string;
  setUserName: React.Dispatch<React.SetStateAction<string>>;
  userMail: string;
  setUserMail: React.Dispatch<React.SetStateAction<string>>;
  userPhoneCountryCode: string;
  setUserPhoneCountryCode: React.Dispatch<React.SetStateAction<string>>;
  userPhonePhoneNumber: string;
  setUserPhonePhoneNumber: React.Dispatch<React.SetStateAction<string>>;
  isMobileSideNavOpened: boolean;
  setIsMobileSideNavOpened: React.Dispatch<React.SetStateAction<boolean>>;
  isTearmsRailActive: boolean;
  setIsTearmsRailActive: React.Dispatch<React.SetStateAction<boolean>>;
  shortIsoCode: string;
  setShortIsoCode: React.Dispatch<React.SetStateAction<string>>;
  invalidEmail: boolean;
  setInvalidEmail: React.Dispatch<React.SetStateAction<boolean>>;
  currentRoute: number;
  setCurrentRoute: React.Dispatch<React.SetStateAction<number>>;
  flightAdultCount: number;
  setFlightAdultCount: React.Dispatch<React.SetStateAction<number>>;
  flightChildCount: number;
  setFlightChildCount: React.Dispatch<React.SetStateAction<number>>;
  flightInfantCount: number;
  setFlightInfantCount: React.Dispatch<React.SetStateAction<number>>;
  userLocation: string;
  setUserLocation: React.Dispatch<React.SetStateAction<string>>;
  maxLimit: number;
  setMaxLimit: React.Dispatch<React.SetStateAction<number>>;
  childCount: number;
  setChildCount: React.Dispatch<React.SetStateAction<number>>;
  railAdultCount: number;
  setRailAdultCount: React.Dispatch<React.SetStateAction<number>>;
  railChildCount: number;
  setRailChildCount: React.Dispatch<React.SetStateAction<number>>;
  adultCount: number;
  setAdultCount: React.Dispatch<React.SetStateAction<number>>;
  infantCount: number;
  setInfantCount: React.Dispatch<React.SetStateAction<number>>;
  toIata: string;
  setToIata: React.Dispatch<React.SetStateAction<string>>;
  fromIata: string;
  setFromIata: React.Dispatch<React.SetStateAction<string>>;
  isOpenLogin: boolean;
  setIsOpenLogin: React.Dispatch<React.SetStateAction<boolean>>;
  serviceType: string;
  setServiceType: React.Dispatch<React.SetStateAction<string>>;
  agentMarkup: number;
  setAgentMarkup: React.Dispatch<React.SetStateAction<number>>;
  markupType: string;
  setMarkupType: React.Dispatch<React.SetStateAction<string>>;
  tui: string[];
  setTui: React.Dispatch<React.SetStateAction<string[]>>;
  clientId: string;
  setClientId: React.Dispatch<React.SetStateAction<string>>;
  agentGetProfileResponse: any;
  setAgentGetProfileResponse: React.Dispatch<React.SetStateAction<any>>;
  hotelStatic: any[];
  setHotelStatic: React.Dispatch<React.SetStateAction<any[]>>;
  setCurrency: (data: { currency: string }) => void;
}

// Create context
export const GeneralStateContext = createContext<GeneralStateContextType | undefined>(undefined);

// Custom hook to use the GeneralStateContext
export const useGeneralState = (): GeneralStateContextType => {
  const context = useContext(GeneralStateContext);
  if (!context) {
    throw new Error('useGeneralState must be used within a GeneralStateProvider');
  }
  return context;
};
