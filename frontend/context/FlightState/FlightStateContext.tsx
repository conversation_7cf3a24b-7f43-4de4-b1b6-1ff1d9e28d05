import { scheduleSave, Journey } from 'models/flight-list-response-model';
import React, { createContext, useContext } from 'react';

// Define flight selection data structure
export interface FlightSelectionData {
  selectedFlight: Journey | null;
  pricingBody: any | null;
  searchTui: string | null;
  sectType: string | null;
}

// Define pricing data structure
export interface FlightPricingData {
  priceData: any | null;
  priceTuis: string[];
  fareSummary: any | null;
  isLoading: boolean;
  error: string | null;
}

// Define the context type
interface FlightStateContextType {
  isFlightDetailsPopup: boolean;
  setIsFlightDetailsPopup: React.Dispatch<React.SetStateAction<boolean>>;
  flightDetailsBodyData: any;
  setFlightDetailsBodyData: React.Dispatch<React.SetStateAction<any>>;
  flightSchedule: scheduleSave[];
  setFlightSchedule: React.Dispatch<React.SetStateAction<scheduleSave[]>>;

  // New flight selection and pricing state
  flightSelection: FlightSelectionData;
  setFlightSelection: React.Dispatch<React.SetStateAction<FlightSelectionData>>;
  flightPricing: FlightPricingData;
  setFlightPricing: React.Dispatch<React.SetStateAction<FlightPricingData>>;

  // Actions
  selectFlight: (flight: Journey, tui: string, sectType: string) => void;
  clearFlightSelection: () => void;
}

// Create the context
export const FlightStateContext = createContext<FlightStateContextType | undefined>(undefined);

// Custom hook to use FlightStateContext
export const useFlightState = (): FlightStateContextType => {
  const context = useContext(FlightStateContext);
  if (!context) {
    throw new Error('useFlightState must be used within a FlightStateProvider');
  }
  return context;
};
