import React, { useState, ReactNode, useCallback } from 'react';
import { FlightStateContext, FlightSelectionData, FlightPricingData } from './FlightStateContext';
import { scheduleSave, Journey } from 'models/flight-list-response-model';

// Define the Props for the Provider
interface FlightStateProviderProps {
    children: ReactNode;
  }

  // FlightStateProvider component
  export const FlightStateProvider: React.FC<FlightStateProviderProps> = ({ children }) => {
    const [isFlightDetailsPopup, setIsFlightDetailsPopup] = useState<boolean>(false);
    const [flightDetailsBodyData, setFlightDetailsBodyData] = useState<any>(null);
    const [flightSchedule, setFlightSchedule] = useState<scheduleSave[]>([]);

    // New flight selection and pricing state
    const [flightSelection, setFlightSelection] = useState<FlightSelectionData>({
      selectedFlight: null,
      pricingBody: null,
      searchTui: null,
      sectType: null,
    });

    const [flightPricing, setFlightPricing] = useState<FlightPricingData>({
      priceData: null,
      priceTuis: [],
      fareSummary: null,
      isLoading: false,
      error: null,
    });

    // Action to select a flight
    const selectFlight = useCallback((flight: Journey, tui: string, sectType: string) => {
      const pricingBody = [{
        "Trips": [
          {
            "Amount": flight.GrossFare,
            "Index": flight.Index,
            "ChannelCode": flight.ChannelCode ? flight.ChannelCode : null,
            "OrderID": 1,
            "TUI": tui
          }
        ],
        "ClientID": "",
        "Mode": "SS",
        "Options": "A",
        "Source": "SF",
        "TripType": "ON"
      }];

      setFlightSelection({
        selectedFlight: flight,
        pricingBody,
        searchTui: tui,
        sectType,
      });

      // Reset pricing data when new flight is selected
      setFlightPricing({
        priceData: null,
        priceTuis: [],
        fareSummary: null,
        isLoading: false,
        error: null,
      });
    }, []);

    // Action to clear flight selection
    const clearFlightSelection = useCallback(() => {
      setFlightSelection({
        selectedFlight: null,
        pricingBody: null,
        searchTui: null,
        sectType: null,
      });
      setFlightPricing({
        priceData: null,
        priceTuis: [],
        fareSummary: null,
        isLoading: false,
        error: null,
      });
    }, []);

    // Memoized context value
    const value = {
      isFlightDetailsPopup,
      setIsFlightDetailsPopup,
      flightDetailsBodyData,
      setFlightDetailsBodyData,
      flightSchedule,
      setFlightSchedule,
      flightSelection,
      setFlightSelection,
      flightPricing,
      setFlightPricing,
      selectFlight,
      clearFlightSelection,
    };

    return (
      <FlightStateContext.Provider value={value}>
        {children}
      </FlightStateContext.Provider>
    );
  };
