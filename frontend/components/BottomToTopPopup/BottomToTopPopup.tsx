import { CloseOutlined } from '@mui/icons-material';
import React, { useEffect } from 'react';
import styles from './BottomToTopPopup.module.scss';

interface props{
    children: React.ReactNode;
    isOpen: boolean;
    onClose:  () => void;
    heading?: string;
    type:string;
}

const  BottomToTopPopup: React.FC<props> = ({children,isOpen , heading = '' , onClose , type=''}) => {
    // const [classToAdd, setClassToAdd] = useState('')
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = 'auto';
        }
        return () => {
            document.body.style.overflow = 'auto';
        };
    }, [isOpen]);
    // useEffect(() => {
    //     if (Mobile(navigator.userAgent)) {
    //         setClassToAdd('remove-scroll-mobile');
    //     } else {
    //         setClassToAdd('remove-scroll');
    //     }
    // }, []); 

    // useEffect(() => {
    //     if (isOpen) {
    //         document.body.classList.add(classToAdd);
    //     } else if (classToAdd) {
    //         document.body.classList.remove(classToAdd);
    //     }
    // }, [isOpen, classToAdd]);


    // useEffect(() => {
    //     return () => {
    //         if (classToAdd) {
    //             document.body.classList.remove(classToAdd);
    //         }
    //     };
    // }, [classToAdd]); // Cleanup when component unmounts



    const closePanel = () => {
        if (isOpen) onClose();
    };
    return(
        <div className={`${styles['bottom-to-top-popup-container']} ${isOpen ? styles.visible : ''}`}>
            <div className={`${styles['dy-bootom-up-popup-overlay']}`} onClick={closePanel}></div>
            <div className={`${styles["dy-bootom-up-popup-div"]} ${isOpen ? styles.show : styles.hide} ${type === 'search' ? 'full-height' : ''}`}>
                {(() => {
                    if (type !== 'search' && type !== 'filter') {
                        return <div className={`${styles["header-section"]} flex flex-row`} >
                                    <div className={`${styles["left"]} flex flex-row justify-start items-center`} >
                                        <span className={styles["fa fa-chevron-left icon ar-rotate"]} onClick={closePanel}></span>
                                        <h5>{heading}</h5>
                                    </div>
                                </div>
                        }
                    })()}

                {type === 'filter' && (
                    <div className={`${styles['close-bttn-container']}`}>
                        <div className={`${styles['close-bttn']}`} onClick={closePanel}>
                            <CloseOutlined className={`${styles['close-icon']}`}/>
                        </div>
                    </div>
                ) }
                {children}
            </div>
        </div>
    );
}

export default BottomToTopPopup;

export function Mobile(userAgent: string) {
    var isMobile = {
        Android: function () {
            return userAgent.match(/Android/i);
        },
        BlackBerry: function () {
            return userAgent.match(/BlackBerry/i);
        },
        IOS: function () {
            return userAgent.match(/iPhone|iPad|iPod/i);
        },
        Opera: function () {
            return userAgent.match(/Opera Mini/i);
        },
        Windows: function () {
            return userAgent.match(/IEMobile/i) || userAgent.match(/WPDesktop/i);
        },
        any: function () {
            return (
                isMobile.Android() ||
                isMobile.BlackBerry() ||
                isMobile.IOS() ||
                isMobile.Opera() ||
                isMobile.Windows()
            );
        },
    };

    return isMobile.any();
}
