.payment-data-div{
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 99;

    .payment-loader {
        width: 150px;
      }
      
      .payment-loader .pad {
        width: 75px;
        height: 50px;
        border-radius: 8px;
        border: 2px solid #efbc00;
        padding: 6px;
        margin: 0 auto;
      }
      
      .payment-loader .chip {
        width: 12px;
        height: 8px;
        background: #efbc00;
        border-radius: 3px;
        margin-top: 4px;
        margin-left: 3px;
      }
      
      .payment-loader .line {
        width: 52px;
        margin-top: 6px;
        margin-left: 3px;
        height: 4px;
        background: #efbc00;
        border-radius: 100px;
        opacity: 0;
        animation: writeline 3s infinite ease-in;
      }
      
      .payment-loader .line2 {
        width: 32px;
        margin-top: 6px;
        margin-left: 3px;
        height: 4px;
        background: #efbc00;
        border-radius: 100px;
        opacity: 0;
        animation: writeline2 3s infinite ease-in;
        animation-delay: 0.5s;
      }
      
      .payment-loader .loader-text {
        text-align: center;
        margin-top: 20px;
        font-size: 16px;
        line-height: 16px;
        color: #5f6571;
        font-weight: bold;
      }
      
      @keyframes writeline {
        0% {
          width: 0px;
          opacity: 0;
        }
        33% {
          width: 52px;
          opacity: 1;
        }
        70% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
      
      @keyframes writeline2 {
        0% {
          width: 0px;
          opacity: 0;
        }
        33% {
          width: 32px;
          opacity: 1;
        }
        70% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
      
}

