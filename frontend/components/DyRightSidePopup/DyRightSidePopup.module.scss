.dy-right-side-popup-overlay{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(#000, 0.7);
}

.dy-right-side-popup-div{
    position: fixed;
    top: 0;
    right: 0;
    width: auto;
    height: 100vh;
    background-color: white;
    overflow: hidden;
    .header-menu-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 45px;
        border-bottom: 1px solid #d0d0d0;
        .head-text{
            font-size: 18px;
            color: #000;
            font-weight: 500;
        }
        .icons{
            font-size: 12px;
            cursor: pointer;
            transform: scale(.8);
            transition: all .5s ease;
            opacity: .5;
            &:hover {
                transform: scale(1);
                opacity: 1;
            }
        }
    }
    .body-div{
        height: calc(100vh - 45px);
        overflow: hidden;
        overflow-y: auto;
        position: relative;
    }
}

@media only screen and (max-width: 768px) {
    .dy-right-side-popup-div{
        .header-menu-div{
            padding: 0 10px;
        }
    }
}