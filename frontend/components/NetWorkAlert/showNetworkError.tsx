import Swal from 'sweetalert2';

export function showNetworkError(onNavigate:()=> void) {
  Swal.fire({
    position: "center",
    icon: "error",
    title: "Unable to load the price from the flight provider",
    text: "Please try again later.",
    showConfirmButton: true,
    confirmButtonText: "OK",
    confirmButtonColor: "#ff5f5f",
    background: "#fefefe",
    customClass: {
      popup: 'swal-popup',
      icon: 'swal-icon',
      htmlContainer: 'swal-text',
    }
  }).then((result) => {
    if (result.isConfirmed ) {
      onNavigate();
    }
  });
}
