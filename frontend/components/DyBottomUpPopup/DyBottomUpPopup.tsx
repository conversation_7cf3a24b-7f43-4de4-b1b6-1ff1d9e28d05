import { ReactNode } from 'react';
import styles from './DyBottomUpPopup.module.scss'

interface BodyDivProps {
    children: ReactNode;
    show: boolean;
    hide: VoidFunction;
    heading: string;
    zindex?: number;
}


const DyBottomUpPopup: React.FC<BodyDivProps> = ({ children, show = false, heading = '', hide, zindex = 201 }: BodyDivProps) => {

    function close() {
        hide();
    }


    //[@OverlayAnimation]="show?'visible':'hidden'"//[@bottomTopBarAni]="show?'visible':'hidden'"
     
    return show ? <>
    
        <div className={styles["dy-bootom-up-popup-overlay"]} style={{ zIndex: zindex - 1 }} onClick={close}></div>
       
        <div className={styles["dy-bootom-up-popup-div"]} style={{ zIndex: zindex }} >
            <div className={styles["header-menu-div"]}>
                <div className={styles["head-text"]}>{ heading }</div>
                <span className={styles["fa fa-times icons"]} onClick={close}></span>
            </div>
            <div className={styles["body-div"]}>
                {children}
            </div>
        </div>
    </>:<></>

}

export default DyBottomUpPopup;