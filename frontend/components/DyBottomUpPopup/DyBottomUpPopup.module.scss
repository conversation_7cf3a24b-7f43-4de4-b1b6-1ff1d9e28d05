.dy-bootom-up-popup-overlay{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(#000, 0.7);
}

.dy-bootom-up-popup-div{
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: auto;
    background-color: white;
    overflow: hidden;
    border-radius: 10px 10px 0 0;
    .header-menu-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 45px;
        border-bottom: 1px solid #d0d0d0;
        .head-text{
            font-size: 18px;
            color: #000;
            font-weight: 500;
        }
        .icons{
            font-size: 12px;
            cursor: pointer;
            transform: scale(.8);
            transition: all .5s ease;
            opacity: .5;
            &:hover {
                transform: scale(1);
                opacity: 1;
            }
        }
    }
    .body-div{
        width: 100%;
        height: auto;
        overflow: hidden;
        overflow-y: auto;
        position: relative;
    }
}

@media only screen and (max-width: 768px) {
    .bottom-up-bar-div{
        .header-menu-div{
            padding: 0 10px;
        }
    }
}