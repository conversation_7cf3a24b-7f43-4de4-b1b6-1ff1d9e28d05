/* HTML: <div class="loader"></div> */
.dy-loader-div{
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    background-color: #00000090;
}
.loader {
    height: 15px;
    aspect-ratio: 4;
    --_g: no-repeat radial-gradient(farthest-side,#ffffff 90%,#0000);
    background: 
      var(--_g) left, 
      var(--_g) right;
    background-size: 25% 100%;
    display: grid;
  }
  .loader:before,
  .loader:after {
    content: "";
    height: inherit;
    aspect-ratio: 1;
    grid-area: 1/1;
    margin: auto;
    border-radius: 50%;
    transform-origin: -100% 50%;
    background: #ffffff;
    animation: l49 1s infinite linear;
  }
  .loader:after {
    transform-origin: 200% 50%;
    --s:-1;
    animation-delay: -.5s;
  }
  
  @keyframes l49 {
    58%,
    100% {transform: rotate(calc(var(--s,1)*1turn))}
  }