"use client";
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState, useRef } from 'react';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';
import LogoutIcon from '@mui/icons-material/Logout';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import styles from './Header.module.scss';
export function Header() {
  // const [isMobile,setIsMobile]=useState(false);
  const router = useRouter();
  const pathName = usePathname()
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    router.push('/');
    localStorage.removeItem('dyUser');
    setIsDropdownOpen(false);
  }

  const handleBookingsClick = () => {
    router.push('/flight/bookings');
    setIsDropdownOpen(false);
  }

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  }
  useEffect(() => {
    const user = localStorage.getItem('dyUser');

    if(user == null){
      setIsLoggedIn(false);
      // router.push('/');
    }else{
      setIsLoggedIn(true);
    }
  },[pathName,router]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  return (
    <>
        <div className={styles["dy-header-div"]}>
            <div className="container mx-auto">
                <div className={styles["inner-header-data"]}>
                    {/* <a href="/flight/FlightHomePage"><img src="	https://www.ecogo.co.in/wp-content/uploads/2024/11/cropped-logo1-300x81.png" /></a> */}
                    <a href="/flight/FlightHomePage">
                      <div className={styles["logotxt"]}>DIGI YATRA</div>
                    </a>

                    {isLoggedIn ? (
                      <div className={styles["profile-container"]} ref={dropdownRef}>
                        <div className={styles["profile-icon"]} onClick={toggleDropdown}>
                          <AccountCircleIcon className={styles["profile-avatar"]} />
                          <ExpandMoreIcon className={`${styles["dropdown-arrow"]} ${isDropdownOpen ? styles["rotate"] : ""}`} />
                        </div>

                        {isDropdownOpen && (
                          <div className={styles["dropdown-menu"]}>
                            <div className={styles["dropdown-item"]} onClick={handleBookingsClick}>
                              <BookmarkBorderIcon className={styles["dropdown-icon"]} />
                              <span>My Bookings</span>
                            </div>
                            <div className={styles["dropdown-item"]} onClick={handleLogout}>
                              <LogoutIcon className={styles["dropdown-icon"]} />
                              <span>Logout</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ):(
                      <div className={styles["signin-register-div"]}>
                        <button className="dy_secondary_bttn">Sign in / Register</button>
                    </div>
                    )}


                </div>
            </div>
        </div>
    </>
  );
}
