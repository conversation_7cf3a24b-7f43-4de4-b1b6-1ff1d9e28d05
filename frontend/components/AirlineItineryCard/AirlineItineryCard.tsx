"use client"

import { AvTimer } from "@mui/icons-material";
import CopyAllIcon from '@mui/icons-material/CopyAll';
import EastIcon from '@mui/icons-material/East';
import FlightIcon from '@mui/icons-material/Flight';
import InfoIcon from '@mui/icons-material/Info';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react"
import formatDate from "helpers/date-helper";
import ItineraryFlightCardHelper from "helpers/flight-itinery-card-helper";
import styles from './AirlineItineryCard.module.scss'


interface AirlineItineryCardProp {
    isItinerary: boolean;
    initialTripData: any;
    currency: string;
    smartPriceLoader: boolean;
    isLoading: boolean
}

interface PaxType {
    ADT: boolean;
    CHD: boolean;
    INF: boolean;
  }

export default function AirlineItineryCard({ isItinerary = false, initialTripData, smartPriceLoader = false  }: AirlineItineryCardProp) {
    const [cancelType, setCancelType] = useState<string>('')
    const [Trips, setTrips] = useState<any[]>(initialTripData)
    const [showFareRule, setShowFareRule] = useState<boolean>(false)
    const [rules, setRules] = useState<any[]>([])
    const [fareRule, setFareRule] = useState<any>()
    const [paxType, setPaxType] = useState<PaxType>({
        ADT: false,
        CHD: false,
        INF: false,
    })
    const [expandAnimation, setExpandAnimation] = useState<boolean>(false)

    useEffect(()=>{
        // Component state has changed - for debugging purposes only
        // console.log(cancelType,fareRule,showFareRule,expandAnimation,paxType);

    },[cancelType,fareRule,showFareRule,expandAnimation,paxType])
    const flightReviewHelper = ItineraryFlightCardHelper();
    const setValue = useCallback((data: any) => {
        setCancelType(data?.cancelType || "");
        if (data?.Rules) {
          const r = data.Rules;
          const fr = r[0];
          setRules(r);
          setFareRule(fr);
        }

        // Update trips if available
        if (data?.Trips !== undefined) {
          const ssrData: any[] = data?.SSR !== undefined ? data.SSR : [];
          // Use the current rules value (from the dependency array)
          setTrips(flightReviewHelper.setFlightCardSegments(data.Trips, ssrData, rules));
        }

        // Update pax type based on itinerary
        if (isItinerary) {
            setPaxType((prev: PaxType) => {
              const updated: PaxType = { ...prev };
              data?.Pax.forEach((x: any) => {
                // Assume x.PTC is one of "ADT", "CHD", "INF"
                const ptc = x.PTC as keyof PaxType;
                updated[ptc] = true;
              });
              return updated;
            });
          } else {
            setPaxType((prev: PaxType) => {
              const updated: PaxType = { ...prev };
              (["ADT", "CHD", "INF"] as (keyof PaxType)[]).forEach((ptc) => {
                if (data[ptc] > 0) {
                  updated[ptc] = true;
                }
              });
              return updated;
            });
          }

      }, [
        rules,
        isItinerary,
        flightReviewHelper,
        setCancelType,
        setRules,
        setFareRule,
        setTrips
      ]);

    useEffect(() => {
        setValue(initialTripData)
    }, [initialTripData])


    function expandCard(indx: number) {
        setExpandAnimation(true)
        const updatedTrips = [...Trips];
        updatedTrips[indx].Journey[0].expand = !updatedTrips[indx].Journey[0].expand;
        setTrips(updatedTrips);
    }

    function copyText(val: string) {
        const selBox = document.createElement('textarea');
        selBox.style.position = 'fixed';
        selBox.style.left = '0';
        selBox.style.top = '0';
        selBox.style.opacity = '0';
        selBox.value = val;
        document.body.appendChild(selBox);
        selBox.focus();
        selBox.select();
        document.execCommand('copy');
        document.body.removeChild(selBox);
    }

    function showFareRulePopup(rulefare: any) {
        if (rulefare) {
            setFareRule(rulefare)
            setShowFareRule(true)
        }
    }

    // function hideFareRulePopup() {
    //     setShowFareRule(false)
    // }
    return (
        <>

            {(() => {
                if (Trips && Trips.length > 0) {
                    return Trips.map((Trip, TripIDX) => {
                        return <div key={TripIDX} className={styles["itinerary-flight-card-div"]}>

                            <div className={`${styles["top-head-div"]} ${styles["flex_row"]} ${styles["align_between_stretch"]}`}>
                                <div className={`${styles["head-left"]} ${styles["flex_row"]} ${styles["align_between_center"]}`}>

                                    <div className={`${styles["left"]} ${styles["flex_row"]} ${styles["alihn_start_center"]}`}>

                                        <div className={`${styles["flex_column"]} ${styles["align_center_end"]} ${styles["depart-date-div"]}`}>
                                            <div className={styles["depart-txt"]}>DEPART</div>
                                            <div className={styles["date-txt"]}>
                                                {formatDate(Trip?.Journey[0]?.DepartureTime, 'dd MMM’yy')}
                                            </div>
                                        </div>


                                        <div className={styles["line-divide"]}></div>


                                        <div className={styles["image-main-mac"]}>
                                            <Image
                                                width={100}
                                                height={100}
                                                src={`/assets/images/AirlineLogo/${Trip?.Journey[0]?.MAC?.slice(-2)}.png`}
                                                alt="Airline Logo"
                                            />
                                        </div>


                                        <div className={`${styles["flex_column"]} ${styles["align_center_start"]}`}>
                                            <div className={styles["from-to"]}>
                                                <span>{Trip?.Journey[0]?.from?.split('|')[1]}</span>
                                                <EastIcon />
                                                <span>{Trip?.Journey[0]?.to?.split('|')[1]}</span>
                                            </div>
                                            <div className={styles["distance-time"]}>
                                                {Trip?.Journey[0]?.Stops === 0 ? 'Non' : Trip?.Journey[0]?.Stops} Stop
                                                {Trip?.Journey[0]?.Stops > 1 ? 's' : ''} | {Trip?.Journey[0]?.Duration}
                                            </div>
                                        </div>
                                    </div>

                                    <div className={`${styles["right"]} ${styles["flex_row"]} ${styles["align_end_center"]}`}>

                                        {isItinerary && Trip?.Journey[0]?.CRSPNR && (
                                            <div>
                                                <div className={styles["gray-text"]}>CRS PNR</div>
                                                <div className={styles["pnr-copy"]}>
                                                    <span className={styles["pnr-txt"]}>{Trip?.Journey[0]?.CRSPNR}</span>
                                                    <span
                                                        className={`${styles["fa"]} ${styles["fa-clipboard"]} ${styles["icons"]}`}
                                                        onClick={() => copyText(Trip?.Journey[0]?.CRSPNR)}
                                                    ></span>
                                                </div>
                                            </div>
                                        )}

                                        {/* Divider between CRS PNR and Airline PNR */}
                                        {isItinerary && Trip?.Journey[0]?.CRSPNR && Trip?.Journey[0]?.AirlinePNR && (
                                            <div className={styles["line-divide"]}></div>
                                        )}

                                        {/* Airline PNR Section */}
                                        {isItinerary && Trip?.Journey[0]?.AirlinePNR && (
                                            <div>
                                                <div className={styles["gray-text"]}>Airline PNR</div>
                                                <div className={styles["pnr-copy"]}>
                                                    <span className={styles["pnr-txt"]}>{Trip?.Journey[0]?.AirlinePNR}</span>
                                                    <CopyAllIcon className={styles["icons"]} onClick={() => copyText(Trip?.Journey[0]?.AirlinePNR)} />
                                                </div>
                                            </div>
                                        )}

                                        {/* Divider after Airline PNR */}
                                        {isItinerary && Trip?.Journey[0]?.AirlinePNR && (
                                            <div className={styles["line-divide"]}></div>
                                        )}

                                        {/* Fare Rules Link */}
                                        {rules.length > 0 && (
                                            <button
                                                type="button"
                                                className="linkbtn"
                                                onClick={() => showFareRulePopup(Trip?.Journey[0]?.fareRule)}
                                            >
                                                Fare Rules
                                            </button>
                                        )}
                                    </div>

                                </div>
                                <div className={styles["head-right"]} onClick={() => expandCard(TripIDX)}>
                                    <KeyboardArrowDownIcon className={`${styles["dwn-icon"]} ${!Trip?.Journey[0]?.expand ? styles["rotate-dwn-icon"] : ''}`} />

                                </div>
                            </div>

                            {(() => {
                                if (Trip?.Journey[0]?.expand) {
                                    /* @cardExpand */

                                    return <div className={styles["flight-travel-details-div"]} >
                                        {(() => {
                                            return Trip?.Journey[0]?.segmentArray.map((seg: any, indx: number) => {
                                                return <React.Fragment key={indx}>
                                                    <>
                                                        {(() => {
                                                            if (seg?.isHops) {
                                                                return <div className={styles["change-flight-div"]}>
                                                                    <AvTimer />
                                                                    <span>Layover Time <strong>{seg?.connectionTime}</strong></span>
                                                                </div>
                                                            }
                                                        })()}


                                                        {(() => {
                                                            if (seg?.isConnection) {
                                                                <div className={styles["change-flight-div"]}>
                                                                    <FlightIcon className={styles["icons"]} />
                                                                    <span>Change planes at <strong>{seg?.connectionAirport} ({seg?.connectionAirportCode})</strong>, Connecting Time: <strong>{seg?.connectionTime}</strong></span>
                                                                </div>
                                                            }
                                                        })()}



                                                        <div className={`${styles["flight-review-div"]} ${styles["flex_row"]} ${styles["align_between_stretch"]}`}>

                                                            <div className={styles["airline-details-div"]}>
                                                                <div className={styles["flight-name"]}>
                                                                    <div className={styles["name-number-div"]}>
                                                                        <div className={styles["name-txt"]}>{seg?.Airline?.split('|')[1]}</div>
                                                                        <div className={styles["number-txt"]}>{seg?.MAC} - {seg?.FlightNo.replace(seg?.MAC, '')}</div>
                                                                    </div>
                                                                </div>
                                                                {
                                                                    seg?.AirCraft && <div className={styles["flight-type-gp"]}>
                                                                        <div>AIRCRAFT</div>
                                                                        <p>{seg?.AirCraft}</p>
                                                                    </div>
                                                                }


                                                                <div className={styles["flight-type-gp"]}>
                                                                    <div>TRAVEL CLASS</div>
                                                                    <p>{seg?.travelClass}</p>
                                                                </div>
                                                            </div>


                                                            <div className={`${styles["from-to-time-div"]} ${styles["flex_row"]} ${styles["align_center_stretch"]}`}>

                                                                <div className={`${styles["left"]} ${styles["flex_column"]} ${styles["align_between_center"]}`}>

                                                                    <div className={styles["box"]}>
                                                                        <h2>{formatDate(seg?.DepartureTime, 'HH:mm')}</h2>
                                                                        <p>{formatDate(seg?.DepartureTime, 'EEE, dd MMM`yy')}</p>
                                                                    </div>


                                                                    <div className={styles["box"]}>
                                                                        <h2>{formatDate(seg?.ArrivalTime, 'HH:mm')}</h2>
                                                                        <p>{formatDate(seg?.ArrivalTime, 'EEE, dd MMM`yy')}</p>
                                                                        {seg?.isNextDay && <p className={styles["next-day"]}>Next Day</p>}
                                                                    </div>
                                                                </div>


                                                                <div className={`${styles["center"]} ${styles["flex_row"]} ${styles["align_center_stretch"]}`}>
                                                                    <span className={styles["divider"]}></span>
                                                                </div>


                                                                <div className={`${styles["right"]} ${styles["flex_column"]} ${styles["align_between_start"]}`}>

                                                                    <div className={styles["sector"]}>
                                                                        <h2>
                                                                            {seg?.DepAirportName?.split('|')[1]} [{seg?.DepartureCode}]
                                                                        </h2>
                                                                        <p>{seg?.DepAirportName?.split('|')[0]}</p>
                                                                        {seg?.DepartureTerminal && (
                                                                            <p>
                                                                                {seg?.DepartureTerminal.includes('Terminal') ? '' : 'Terminal ' + seg?.DepartureTerminal}
                                                                            </p>
                                                                        )}
                                                                    </div>


                                                                    <div className={styles["time-div"]}>
                                                                        <h2>{seg?.Duration}</h2>
                                                                    </div>


                                                                    <div className={styles["sector"]}>
                                                                        <h2>
                                                                            {seg?.ArrAirportName?.split('|')[1]} [{seg?.ArrivalCode}]
                                                                        </h2>
                                                                        <p>{seg?.ArrAirportName?.split('|')[0]}</p>
                                                                        {seg?.ArrivalTerminal && (
                                                                            <p>
                                                                                {seg?.ArrivalTerminal.includes('Terminal') ? '' : 'Terminal ' + seg?.ArrivalTerminal}
                                                                            </p>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div className={`${styles["baggage-confirmation"]} ${styles["flex_column"]} ${styles["align_between_end"]}`}>
                                                                {/* Baggage Box Section */}
                                                                {(smartPriceLoader || (seg?.baggage?.checkin.length > 0 && seg?.baggage?.cabin.length > 0)) && (
                                                                    <div className={`${styles["baggagebox"]} ${styles["flex_column"]} ${styles["align_center_center"]}`}>
                                                                        <div className={`${styles["baggageboxtop"]} ${styles["flex_row"]} ${styles["align_center_center"]}`}>
                                                                            <div>
                                                                                <Image width={100} height={100} src="/assets/images/check-baggage.png" alt="Baggage" />
                                                                            </div>
                                                                            <div className={styles["right"]}>
                                                                                <p>Free</p>
                                                                                <p>Baggage</p>
                                                                                <p>Details</p>
                                                                            </div>
                                                                        </div>

                                                                        <div className={`${styles["baggageboxbottom"]} ${styles["flex_row"]} ${styles["align_center_stretch"]}`}>
                                                                            {/* Baggage Details */}
                                                                            {!smartPriceLoader && (
                                                                                <>
                                                                                    <div className={`${styles["flex_column"]} ${styles["align_center_end"]}`}>
                                                                                        <div className={styles["label"]}>CHECK-IN</div>
                                                                                        {seg?.baggage?.checkin.map((item: any, idx: number) => (
                                                                                            <div className={styles["values"]} key={idx}>{item}</div>
                                                                                        ))}
                                                                                    </div>

                                                                                    {/* Cabin Baggage Details */}
                                                                                    {seg?.baggage?.checkin.length > 0 && seg?.baggage?.cabin.length > 0 && (
                                                                                        <>
                                                                                            <div className={styles["divider"]}></div>
                                                                                            <div className={`${styles["flex_column"]} ${styles["align_center_start"]}`}>
                                                                                                <div className={styles["label"]}>CABIN</div>
                                                                                                {seg?.baggage?.cabin.map((item: any, idx: number) => (
                                                                                                    <div className={styles["values"]} key={idx}>{item}</div>
                                                                                                ))}
                                                                                            </div>
                                                                                        </>
                                                                                    )}
                                                                                </>
                                                                            )}

                                                                            {smartPriceLoader && <div className={`${styles["baggage-loader"]} ${styles["shine"]}`}></div>}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>


                                                        </div>
                                                    </>

                                                </React.Fragment>
                                            })
                                        })()}

                                        {(() => {
                                            if (Trip?.Journey[0]?.Notices && Trip?.Journey[0]?.Notices.length > 0) {
                                                return <div className={styles["notice-div"]}>
                                                    {(() => {
                                                        return Trip?.Journey[0]?.Notices.map((k: any, indx: number) => {
                                                            return <div key={indx} className={styles["icon-notice-txt"]}>
                                                                <div><InfoIcon className={styles["icon"]} /></div>
                                                                {(() => {
                                                                    if (k?.Link === '') {
                                                                        return <div className={styles["notice-text"]} >{k?.Notice}</div>

                                                                    }
                                                                })()}
                                                                {(() => {
                                                                    if (k?.Link !== '') {
                                                                        return <a className={styles["notice-text"]} href="{k?.Link}" target="_blank">{k?.Notice}</a>
                                                                    }
                                                                })()}
                                                            </div>
                                                        })
                                                    })()}

                                                </div>
                                            }
                                        })()}

                                    </div>

                                }
                            })()}


                        </div >

                    })
                }
            })()}

            {
                (() => {
                    if (rules.length > 0) {
                        //    return <dy-right-side-popup [show]="showFareRule" [heading]="'Fare Rules'" (hide)="hideFareRulePopup()">
                        //         <div className={styles["fare-rule-pdng-div"]}>
                        //             <dy-airline-fare-rule [rules]="fareRule" [paxType]="paxType" [currency]="currency"></dy-airline-fare-rule>
                        //         </div>
                        //     </dy-right-side-popup>
                        return <></>
                    }
                })()}




        </>
    );
}
