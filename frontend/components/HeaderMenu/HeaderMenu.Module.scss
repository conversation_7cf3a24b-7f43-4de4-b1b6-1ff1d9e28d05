@use '../../styles/variable.scss' as *;

.head-static-height{
    position: relative;
    z-index: 99;
    height: 60px;
    box-shadow: 0px 2px 13px rgb(5 5 5 / 20%);

    .header-div {
        width: 100%;
        .header-data {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            height: 60px;

            .image-div {
                // height: 40px;
                width: auto;
                text-decoration: none;
                font-size: 25px;
                color: #FFF;
                // font-family: "Pacifico", cursive;
                font-family: "Montserrat", sans-serif;
                font-weight: 600;
                img {
                    height: 100%;
                    width: auto;
                    cursor: pointer;
                }
            }

            .maenu-txt {
                display: none;

                .mat-icon {
                    color: #fff;
                }
            }

            .navigate-links {
                display: flex;
                align-items: center;
                gap: 15px;

                .link-item {
                    padding: 5px 10px;
                    font-size: 13px;
                    border-bottom: solid #ffffff00;
                    cursor: pointer;
                    text-transform: uppercase;
                    color: #fff;
                    text-decoration: none;

                    &:hover {
                        border-bottom: solid $secondary_color;
                    }
                }

                .drop-down-bttn {
                    padding: 5px 15px;
                    font-size: 13px;
                    border: 1px solid $button_color;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    height: fit-content;
                    border-radius: 5px;
                    min-height: 33px;
                    color: $button_color;

                    .text-data {
                        line-height: 13px;
                    }

                    .mat-icon {
                        height: 10px;
                        width: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 24px;
                    }
                }

                .login-profile-bttn {
                    padding: 5px 15px;
                    font-size: 13px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    height: fit-content;
                    border-radius: 5px;
                    min-height: 33px;
                    text-transform: uppercase;

                    .mat-icon {
                        height: 10px;
                        width: 10px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 24px;
                    }
                }

                .login-bttn-color {
                    border: 1px solid $button_color;
                    background-color: #fff;
                    color: $button_color;
                    font-weight: 600;
                }
            }
        }
    }
}

.currency-menu-div {
    display: flex;

    .menu-item-currency {
        width: 33.33%;
        min-width: fit-content;
    }

    .mat-currency-border {
        border-left: 1px solid #e6e5e5;
        border-right: 1px solid #e6e5e5;
    }

    .currency-symbl-div {
        display: flex;
        gap: 5px;
        align-items: center;

        .symbol-txt {
            font-size: 15px;
            font-weight: 500;
        }

        .currency-tx {
            font-size: 13px;
            font-weight: 400;
        }
    }

    .flag-icon {
        font-size: 13px;
    }
}

::ng-deep .cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing {
    opacity: .5;
    background: #000;
}


::ng-deep .mat-mdc-menu-panel {
    max-width: fit-content !important;

    .mat-mdc-menu-content {
        .mat-mdc-menu-item {
            min-height: 35px;
            line-height: 35px;
            font-weight: 400;
            color: #363636;
            position: relative;
            width: 100%;

            .mdc-list-item__primary-text,.mat-mdc-menu-item-text {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 20px;
                justify-content: space-between;
            }

            .mat-mdc-menu-ripple {
                &::before {
                    content: "";
                    left: 0px;
                    top: 50%;
                    width: 4px;
                    height: 0%;
                    background: $primary_color;
                    position: absolute;
                    z-index: 11;
                    opacity: 0;
                    border-radius: 0 999px 999px 0;
                    transition: all 0.5s ease;
                }
            }

            &:hover {
                .mat-mdc-menu-ripple {
                    &::before {
                        height: 100%;
                        opacity: 1;
                        top: 0;
                    }
                }
            }


        }
    }
}

@media screen and (max-width: 900px) {
    .head-static-height{
        height: 46px;
        .header-div {
            .header-data {
                .image-div {
                    height: 30px;
                }

                .maenu-txt {
                    display: block;
                }

                .navigate-links {
                    display: none;
                }
            }
        }
    }
}