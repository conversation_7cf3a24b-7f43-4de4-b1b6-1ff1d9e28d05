import { useEffect, useState } from "react"

export default function HeaderMenu() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [userName, setUsername] = useState("Name")

  useEffect(()=>{
    setIsLoggedIn(false)
    setUsername("Name")
  },[])

  return (
    <>
      <div className="dy-mobile-menu-overlay"></div>
      <div className="dy-mobile-menu-div">
        <div className="inside-menu">
          <div className="navigation-section">
            <div className="header-user-div">
              <div className="round-div head_font">{isLoggedIn ? userName[0] : "U"}</div>
              <div className="hello-gst-div">
                <span className="head_font">Hi</span>
                <strong>{isLoggedIn ? userName : "User"}</strong>
              </div>
            </div>

            <div className="nav-data-item">
              <a className="nav-links-div" href="/">
                <div className="txt-div">Home</div>
                <span className="fa fa-chevron-right"></span>
              </a>

              {(() => {
                if (!isLoggedIn) {
                  return(
                  <>
                    <div className="nav-links-div">
                      <div className="txt-div">Sign in</div>
                      <span className="fa fa-chevron-right"></span>
                    </div>
                    <div className="nav-links-div">
                      <div className="txt-div">Register</div>
                      <span className="fa fa-chevron-right"></span>
                    </div>
                  </>);
                } else {
                  return (
                    <>
                      <div className="nav-links-div" >
                        <div className="txt-div">My Profile</div>
                        <span className="fa fa-chevron-right"></span>
                      </div>

                      <div className="nav-links-div">
                        <div className="txt-div">Logout</div>
                        <span className="fa fa-chevron-right"></span>
                      </div>
                    </>
                  )
                }
              })()}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
