
export interface SmartPricerRequest {
    TripType: string;
    Source: string;
    ClientID: string;
    Mode: string;
    Options: string;
    Trips: RequestTrip[];
}

export interface RequestTrip {
    TUI: string;
    Index: string;
    Amount: number;
    OrderID: number;
    BookingClass?: PRBookingClass[];
    ChannelCode?: string;
}

export interface PRBookingClass {
    FUID: string;
    BookingClass: string;
}




