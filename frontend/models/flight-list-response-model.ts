export interface FlightResponse {
    sh_price: boolean
    TUI: string
    Completed: boolean
    CeilingInfo: string
    TripType: any
    ElapsedTime: string
    Notices: any
    Msg: string[]
    Code: number
    Trips: Trip[]
  }
  
  export interface Trip {
    Journey: Journey[]
  }
  
  export interface Journey {
    Stops: number
    Seats: number
    ReturnIdentifier: any
    Index: string
    Provider: string
    FlightNo: string
    VAC: string
    MAC: string
    OAC: string
    ArrivalTime: string
    DepartureTime: string
    FareClass: string
    Duration: string
    GroupCount: number
    TotalFare: number
    GrossFare: number
    TotalCommission: number
    NetFare: number
    Hops: number
    Notice: string
    NoticeLink: string
    NoticeType: string
    Refundable: string
    Alliances: string
    Amenities: string
    Hold: boolean
    Connections: Connection[]
    From: string
    To: string
    FromName: string
    ToName: string
    AirlineName: string
    AirCraft: string
    RBD: string
    Cabin: string
    FBC: string
    FCBegin: any
    FCEnd: any
    FCType: string
    GFL: boolean
    Promo: string
    Recommended: boolean
    Premium: boolean
    JourneyKey: string
    FareKey: string
    PaxCategory: string
    PrivateFareType: string
    DealKey: any
    VACAirlineLogo: string
    MACAirlineLogo: string
    OACAirlineLogo: string
    //Rechange if needed
    isShowFareType: boolean //
    CreatedJourneyKey?: string
    isDisplay?: boolean
    SubFlights?: SubFlight[]
    ConnectionText?: string
    isSelect?: boolean
    isVisible: boolean
    ChannelCode?: string
    WpIndex?: string | null
    IsSchedule?: boolean
    Inclusions?: Inclusions
  }
  
  export interface Connection {
    Airport: string
    ArrAirportName: string
    Duration: string
    MAC: string
    Type: string
  }
  

export interface Notice {
    Notice?: string
    Link?: string
    NoticeType?: string
}


export interface CombaignTrip {
    Journey: Journey[]
    From: string
    FromName: string
    To: string
    ToName: string
}

export interface MCSplitTuis {
    TUI: string
    From: string
    FromName: string
    To: string
    ToName: string
    Journey: Journey[]
}

export interface MCPaginateSplitTuis {
    TUI: string
    From: string
    FromName: string
    To: string
    ToName: string
    Journey: Journey[][]
}

export interface RTSelectTrip {
    from: string
    to: string
    Journey?: Journey
}

export interface MCSelectTrip {
    Tui: string
    Journey?: Journey
}

export interface Inclusions {
    Baggage?: string
    Meals?: string | null
    PieceDescription?: string | number | null
}


export interface scheduleSave {
    Trip?: Trip[] | null
    SecType?: string
    Search?: string
}

export interface SubFlight {
    Amount?: number
    Index?: string
    ChannelCode?: string
    Baggage?: string
    SeatSelection?: string | number
    Meal?: string
    Refund?: string
    FCType?: string
    isSelect?: boolean
}

export interface CombaignTripMap {
    ReturnIdentifier: number
    total: number
    trips: CombaignTripMapTrip[]
}

export interface CombaignTripMapTrip {
    from: string
    to: string
    journey: Journey[]
}

export interface FlightSectorFilter {
    From?: string
    To?: string
    Tui?: string
    FilterData?: FlightFilterData
}


export interface FlightFilterData {
    stop: FlightFilterStop[]
    Refund: FlightFilterRefund
    DepTime: FlightFilterTime[]
    ArrTime: FlightFilterTime[]
    Airlines: FlightFilterAirline[]
    priceRange: FlightFilterPriceRange
    connectionAirport: FlightFilterConnectionAirport[]
    isconnectionAirportExpand: boolean
    isAirlinesExpand: boolean
}

export interface FlightFilterStop {
    value: number
    text: string
    isSelect: boolean
}

export interface FlightFilterRefund {
    isSelect: boolean
    text: string
}

export interface FlightFilterTime {
    min: number
    max: number
    isSelect: boolean
    icon: string
    text: string
}

export interface FlightFilterAirline {
    Mac: string
    AirlineName: string
    isSelect: boolean
}

export interface FlightFilterPriceRange {
    Min: number
    Max: number
    value: number
}

export interface FlightFilterConnectionAirport {
    Airport: string
    AirportName: string
    isSelect: boolean
}
