import { useCallback, useState } from 'react';
import { useFlightState } from 'context/FlightState/FlightStateContext';
import FlightApiService from 'services/flight-api-service';
import FlightReviewPageHelper from 'helpers/flight-review-helper';
import FlightReviewPageAdapter from 'adapters/flight-review-adapter';

export interface FlightPricingResult {
  priceData: any[];
  priceTuis: string[];
  fareSummary: any;
  travelForm: any;
  isLoading: boolean;
  error: string | null;
}

export const useFlightPricing = () => {
  const { flightSelection, flightPricing, setFlightPricing } = useFlightState();
  const [helper] = useState(() => new FlightReviewPageHelper());
  const [adapter] = useState(() => new FlightReviewPageAdapter());

  const callSmartPricer = useCallback(async (): Promise<FlightPricingResult> => {
    if (!flightSelection.pricingBody || !Array.isArray(flightSelection.pricingBody)) {
      throw new Error('No pricing body available');
    }

    setFlightPricing(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const results: any[] = [];
      const tuis: string[] = [];
      const fareSummaries: any[] = [];

      // Process each pricing request
      for (let i = 0; i < flightSelection.pricingBody.length; i++) {
        const requestBody = flightSelection.pricingBody[i];

        try {
          const data = await FlightApiService.callSmartPricer(requestBody);
          results.push(data);
          tuis.push(data.TUI);
          fareSummaries.push(helper.setFareSummary(data));
        } catch (error) {
          console.error(`Error in pricing request ${i}:`, error);
          throw new Error(`Failed to get pricing for request ${i + 1}`);
        }
      }

      // Calculate final fare summary
      const finalFareSummary = fareSummaries.length > 1
        ? helper.addFareSummaries(fareSummaries)
        : fareSummaries[0];

      // Generate travel form based on the first result
      const firstResult = results[0];
      const travelForm = adapter.paxFormSet(
        firstResult.ADT || 1,
        firstResult.CHD || 0,
        firstResult.INF || 0
      );

      const result: FlightPricingResult = {
        priceData: results,
        priceTuis: tuis,
        fareSummary: finalFareSummary,
        travelForm,
        isLoading: false,
        error: null,
      };

      // Update context state
      setFlightPricing({
        priceData: results,
        priceTuis: tuis,
        fareSummary: finalFareSummary,
        isLoading: false,
        error: null,
      });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get flight pricing';

      setFlightPricing(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      throw new Error(errorMessage);
    }
  }, [flightSelection.pricingBody, setFlightPricing, helper, adapter]);

  const callTravelChecklist = useCallback(async (tuis: string[]): Promise<any[]> => {
    const results: any[] = [];

    for (let i = 0; i < tuis.length; i++) {
      try {
        // For now, using mock data as in the original implementation
        // Replace this with actual API call when backend is ready
        const data = {
          TUI: tuis[i],
          Code: "200",
          Msg: ["Success"],
          GSTEnabledAirline: "6E,S6E,IX,QP,...",
          TravellerCheckList: [
            {
              DOB: 0,
              PassportNo: 0,
              PLI: 0,
              Nationality: 0,
              VisaType: 0,
              Country: 0,
              PDOE: 0,
              DOI: 0,
              "PAN card": 0,
            },
          ],
        };
        results.push(data.TravellerCheckList[0]);
      } catch (error) {
        console.error(`Error in travel checklist request ${i}:`, error);
        // Continue with other requests even if one fails
      }
    }

    return results;
  }, []);

  const callFlightSSR = useCallback(async (priceData: any[], priceTuis: string[], sectType: string): Promise<any[]> => {
    const results: any[] = [];

    for (let i = 0; i < priceData.length; i++) {
      const data = priceData[i];

      // Check if SSR API call is needed
      const trip = data?.Trips?.[0];
      const journey = trip?.Journey?.[0];
      const segment = journey?.Segments?.[0];
      const flight = segment?.Flight;

      if (!data || !trip || !journey || !segment || !flight) {
        console.warn(`Skipping SSR call for index ${i} due to missing data structure`);
        continue;
      }

      try {
        const body = {
          PaidSSR: true,
          Source: data.Source,
          Trips: data.Trips.map((x: any, j: number) => ({
            Amount: 0,
            Index: "",
            OrderID: j + 1,
            TUI: priceTuis[i],
          })),
        };

        const ssrData = await FlightApiService.callFlightSSR(body);
        results.push(ssrData);
      } catch (error) {
        console.error(`Error in SSR request ${i}:`, error);
        // Continue with other requests even if one fails
      }
    }

    return results;
  }, []);

  const callFlightSeat = useCallback(async (priceData: any[], priceTuis: string[], sectType: string): Promise<any[]> => {
    const results: any[] = [];

    for (let i = 0; i < priceData.length; i++) {
      const data = priceData[i];

      // Check if seat API call is needed
      const trip = data?.Trips?.[0];
      const journey = trip?.Journey?.[0];
      const segment = journey?.Segments?.[0];
      const flight = segment?.Flight;

      if (!data || !trip || !journey || !segment || !flight) {
        console.warn(`Skipping seat call for index ${i} due to missing data structure`);
        continue;
      }

      try {
        const body = {
          Source: data.Source,
          Trips: data.Trips && data.Trips.length > 0 &&
            data.Trips.map((x: any, j: number) => ({
            Index: "",
            OrderID: j + 1,
            TUI: priceTuis[i],
          })),
        };

        const seatData = await FlightApiService.callFlightSeat(body);
        results.push(seatData);
      } catch (error) {
        console.error(`Error in seat request ${i}:`, error);
        // Continue with other requests even if one fails
      }
    }

    return results;
  }, []);

  return {
    flightPricing,
    callSmartPricer,
    callTravelChecklist,
    callFlightSSR,
    callFlightSeat,
  };
};
