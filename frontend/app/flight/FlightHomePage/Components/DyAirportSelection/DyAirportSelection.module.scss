@use '../../../../../styles/variable.scss' as *;

.airport_select_close_div {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9;
}

.airport_auto_complete {
    position: absolute;
    left: 0;
    right: 0;
    top: 25px;
    z-index: 99;
    background: #fff;
    border: 1px solid #d4d4d4;
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, .23);

    @media only screen and (max-width: $isMobile) {
        position: relative;
        top: 0;
        bottom: 0;
    }

    .search_div {
        width: 100%;
        display: flex;
        padding: 15px 10px;
        gap: 10px;

        .input_text_data {
            font-weight: 400;
            border: none;
            outline: 0;
            color: #666;
            font-size: 15px;
            flex: 1 1 auto;
        }

        .mat_icon {
            color: #666;
        }
    }

    .previously_search_head {
        padding: 5px 10px;
        background-color: $primary_color;
        font-size: 12px;
        font-weight: 400;
        width: 100%;
        color: $button_txt_color;
    }

    .error_airport {
        padding: 5px 10px;
        background-color: #ffd6d6;
        font-size: 12px;
        font-weight: 500;
        width: 100%;
        color: black;

        .icon_er {
            color: red;
        }
    }

    .search_list_div {
        max-height: 330px;
        overflow: hidden;
        overflow-y: auto;

        @media only screen and (max-width: $isMobile) {
            height: calc(100vh - 56px);
            max-height: none;
        }

        .search_data_div {
            padding: 7px 10px;
            cursor: pointer;
            border-bottom: 1px solid $bordercolor;

            .shimmer_list_div {
                width: 100%;
                height: 35px;
            }

            .airport_data {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .city_airpt_name {
                    width: calc(100% - 50px);

                    .city_name {
                        font-size: 15px;
                        line-height: 15px;
                        color: #010101;
                        margin: 0;
                        font-weight: 400;
                    }

                    .airpt_name {
                        display: block;
                        color: #898989;
                        font-size: 12px;
                        line-height: 14px;
                        width: 100%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        margin-top: 4px;
                        font-weight: 400;
                    }
                }

                .iata_name {
                    width: 40px;
                    align-items: center;
                    display: flex;
                    justify-content: center;
                    background: #404040;

                    .iata_card {
                        color: #fff;
                        font-weight: 700;
                        font-size: 14px;
                        text-transform: uppercase;
                    }
                }
            }

            &:hover {
                background-color: $bg_light;
            }
        }
    }
}