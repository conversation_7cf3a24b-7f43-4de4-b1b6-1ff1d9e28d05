@use '../../../../../styles/variable.scss' as *;

.dy-flight-search-section-div {

    @media screen and (max-width: $isMobile) {
        padding: 0 50px;
    }
    @media screen and (max-width: 750px) {
        padding: 0 10px;
    }
    .service-section-div {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center;

        @media screen and (max-width: $isMobile) {
            display: none;
        }

        .service-postision {
            position: absolute;
            z-index: 9;
            background-color: $primary_color;
            padding: 8px 10px;
            border-radius: 30px;
            top: -30px;
            font-weight: 600;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;

            .service-data {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 5px 10px;
                border-radius: 20px;
                color: #cecdcd;

                .mat-icon{
                    font-size: 18px;
                }
            }

            .active {
                background-color: #fff;
                color: $button_color;
                cursor: pointer;
            }
        }
    }

    .position-div-route{
        display: none;
        width: 100%;
        height: 40px;
        background-color: #fff;
        border-radius: 5px 5px 0 0;
        font-size: 12px;
        @media screen and (max-width: $isMobile) {
            display: flex;
        }

        .sub-service-div{
            position: relative;
            width: 50%;
            height: 100%;
        }
        .active-data{
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 50px;
            background-color: #fff;
            border-radius: 5px 10px 0 0;
            align-items: center;
            justify-content: center;
            display: flex;
            gap: 5px;
            font-weight: 600;
        }
        .deactive-data{
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 40px;
            background-color: #e3e3e3;
            border-radius: 0 5px 0 10px;
            align-items: center;
            justify-content: center;
            display: flex;
            gap: 5px;
            font-weight: 600;
        }
        .mat-icon{
            height: 12px;
            width: 12px;
            font-size: 12px;
        }
    }

    .flight-search-section-div {
        position: relative;
        width: 100%;
        border-radius: 20px;
        background: #fff;
        box-sizing: border-box;
        padding: 10px 0;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

        @media screen and (max-width: $isMobile) {
            border-radius: 0 0 5px 5px;
        }
    }

    .fare-type-selection-div {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 20px;
        padding: 0 10px 10px 10px;

        .selection-bttn{
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 15px;

            .seclection-chekbox{
                width: 17px;
                height: 17px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid #0c0c0c;
                background-color: #fff;
                border-radius: 5px;

                .mat-icon {
                    width: 16px;
                    height: 16px;
                    font-size: 16px;
                    color: #fff;
                }
            }
        }

        .selected-bttn{
            background-color: $primary_color;
            color: $button_txt_color;
            .seclection-chekbox{

                .mat-icon {
                    color: #000;
                }
            }
        }
    }

    .from-to-depart-return-traveller-div {
        display: flex;
        width: 100%;
        border-top: 1px solid $bordercolor2;
        border-bottom: 1px solid $bordercolor2;
        padding: 10px 0;
        flex-wrap: wrap;

        .label-txt {
            color: #646464;
            font-size: 14px;
            font-weight: 400;

            .mat-icon {
                height: 14px;
                width: 14px;
                font-size: 14px;
            }
        }

        .weight-txt-div {
            display: flex;
            align-items: flex-end;
            gap: 5px;
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;

            .sub-wght{
                font-size: 25px;
                line-height: 27px;
                font-weight: 700;
                @media screen and (max-width: $isMobile) {
                    font-size: 20px;
                    line-height: 22px;
                }
            }

            .sub-lght {
                font-size: 16px;
                font-weight: 400;
                @media screen and (max-width: $isMobile) {
                    font-size: 13;
                }
            }
        }

        .sub-text-div {
            font-size: 13px;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            @media screen and (max-width: $isMobile) {
                font-size: 12px;
            }
        }

        .no-date-found{
            font-size: 14px;
            @media screen and (max-width: $isMobile) {
                font-size: 12px;
            }
        }

        .airport-div {
            width: 40%;
            display: flex;
            position: relative;

            @media screen and (max-width: $isMobile) {
                width: 100%;
                padding-bottom: 10px;
                border-bottom: 1px solid $bordercolor2;
            }

            .from-to-airport-div{
                width: 50%;
                padding: 0 20px;
                border-right: 1px solid $bordercolor2;
                position: relative;

                @media screen and (max-width: $isMobile) {
                    padding: 0 10px;
                    width: 49%;
                }

                .from-airport-div {
                    width: 100%;
                    cursor: pointer;
                }
            }

            .to-airport-div{
                width: 49%;
                @media screen and (max-width: $isMobile) {
                    border-right: none;
                }
            }

            .swap-airport-div {
                position: relative;
                width: 1%;
                display: flex;
                align-items: center;
                @media screen and (max-width: $isMobile) {
                    width: 2%;
                }

                .swap-bttn {
                    position: absolute;
                    left: -15px;
                    background-color: #fff;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 1px 30px 0 rgba(0, 0, 0, 0.1);
                    border-radius: 50%;
                    .mat-icon {
                        color: $button_color;
                        cursor: pointer;
                        animation: 5s;
                        transform: rotate(0%);
                    }
                }
            }
        }

        .depart-return-date-div{
            width: 50%;
            display: flex;

            @media screen and (max-width: $isMobile) {
                width: 100%;
                flex-wrap: wrap;
                padding-top: 10px;
            }

            .date-data-div{
                width: 30%;
                padding: 0 20px;
                border-right: 1px solid $bordercolor2;
                cursor: pointer;
                position: relative;

                @media screen and (max-width: $isMobile) {
                    width: 49%;
                }

                .hide-date {
                    width: 0;
                    height: 0;
                    padding: 0;
                    border: none;
                }
            }

            .ret-date-data{
                @media screen and (max-width: $isMobile) {
                    width: 50%;
                    border-right: none;
                }
            }

            .travel-class-div{
                width: 40%;
                padding: 0 20px;
                position: relative;
                @media screen and (max-width: $isMobile) {
                    width: 100%;
                    padding-top: 10px;
                    border-top: 1px solid $bordercolor2;
                    margin-top: 10px;
                }
                .trveler-data{
                    width: 100%;
                    cursor: pointer;
                }
            }
        }

    }

    .flight-search-buttons-div{
        display: flex;
        width: 10%;
        justify-content: flex-end;
        align-items: center;
        padding-right: 20px;

        @media screen and (max-width: $isMobile) {
            button{
                width: 100%;
            }
        }
    }

    .dy-primary-bttn {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        font-size: 15px;
        font-weight: 500;
        line-height: 1;
        transition-duration: 0.4s;
        background: $button_color;
        color: $button_txt_color;
        border-radius: 5px;
        outline: 0;
        border: 1px solid $button_color;
        min-width: 100px;
        width: 100%;
        cursor: pointer;
    }

}