.flight-review-fare-summary-div {

    .head-summary-div {
        font-size: 17px;
        font-weight: 500;
        color: #000;
        margin-bottom: 10px;
    }

    .border-fare-summry {
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
    }

    .total-div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 15px 20px;

        h3 {
            font-size: 19px;
            font-weight: 600;
            margin-bottom: 0;
        }
    }

    .expansion-panel {
        box-shadow: none;
        border-radius: 4px;
        border-bottom: 1px solid #ebebeb;

        .expansion-panel-header {
            padding: 15px 9px;
            display: flex;
            justify-content: space-between;

            .panel-title {
                cursor: pointer;
                font-size: 15px;
                font-weight: 500;
                color: #000;
                // padding-left: 10px;
                position: relative;
                align-items: center;
                text-transform: capitalize;

                .material-icons {
                    height: 16px;
                    line-height: 16px;
                    width: 18px;
                    font-size: 20px;
                }
                .arrow-icon {
                    cursor: pointer;
                    transition: transform 0.3s ease;
                  }
                .rotate {
                    transform: rotate(180deg);
                }
            }

            h5 {
                font-size: 15px;
                font-weight: 500;
                color: #000;
                padding-left: 10px;
                white-space: nowrap;
                margin-bottom: 0;
            }
        }

        &.mat-expansion-panel-body {
            padding: 0 16px 15px 12px;
        }

        &.mat-expansion-panel-spacing {
            margin: 0;
        }

        .datas {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 5px;
            height: 0;
            overflow: hidden;
            transition: height 0.5s ease, padding 0.5s ease;
            &.open {
                padding: 0 15px 10px 38px;
                height: auto; /* Adjust based on content */
              }
            p {
                color: #525252;
                font-size: 12px;
                font-weight: 500;
            }

            h6 {
                color: #525252;
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 0;
            }
        }
    }
}

::ng-deep .mat-expansion-panel-body {
    padding: 0 !important;
}

@media only screen and (max-width: 768px) {
    .flight-review-fare-summary-div {
        border: none;
        border-radius: 0;
    }
}