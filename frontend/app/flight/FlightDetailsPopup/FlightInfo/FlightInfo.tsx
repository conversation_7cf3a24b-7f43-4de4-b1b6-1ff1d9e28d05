"use client";

import React from 'react';
import styles from './FlightInfo.module.scss';
import { useState } from 'react';
import { FlightInfoMap } from 'models/flight-info-ssr-model';

interface  FlightInfoProp{
    flight:FlightInfoMap
}
export function FlightInfo({flight}:FlightInfoProp) {

    return (
        <>
            <div className={styles["flight-details-info-div"]}>
                <div className={styles["trip-text"]}>{flight.from} - {flight.to}</div>
                <div className={styles["flight-card-div"]}>
                    {flight.segments.map((k, index) => (
                        <React.Fragment key={index}>
                            {k.isConnection && (
                                <div className={styles["connection-div"]}>
                                    <span className={styles["fa fa-plane icon-flght"]}></span>
                                    <span>
                                        Change planes at <strong>{k.connectionAirport} ({k.connectionCode})</strong>, Connecting Time:
                                        <strong> {k.connectionTime.slice(0, 2)} Hrs {k.connectionTime.slice(4, 6)} Mins</strong>
                                    </span>
                                </div>
                            )}
                            {k.isHops && (
                                <div className={styles["connection-div"]}>
                                    <span className={styles["fa fa-clock-o icon-time"]}></span>
                                    <span>
                                        Layover at <strong>{k.connectionAirport} ({k.connectionCode})</strong>, Layover Time:
                                        <strong> {k.connectionTime.slice(0, 2)} Hrs {k.connectionTime.slice(4, 6)} Mins</strong>
                                    </span>
                                </div>
                            )}
                            <div className={styles["top-div"]}>
                                <div className={styles["image-name-number"]}>
                                    <div className={styles["image-div"]}>
                                        <img src={`/assets/images/AirlineLogo/${k.Mac}.png`} alt="" />
                                    </div>
                                    <div className={styles["name-number-div"]}>
                                        <div className={styles["name-div"]}>{k.Airline}</div>
                                        <div className={styles["number-div"]}>{k.Mac} - {k.FlightNo}</div>
                                    </div>
                                </div>
                                <div className={styles["aircraft-travel-class-div"]}>
                                    {k.AirCraft && k.Airline && (
                                        <div className={styles["label-text-div"]}>
                                            <div className={styles["label-txt"]}>Aircraft</div>
                                            <div className={styles["txt-div"]}>{k.AirCraft}</div>
                                        </div>
                                    )}
                                    {k.Cabin && (
                                        <div className={styles["label-text-div"]}>
                                            <div className={styles["label-txt"]}>Travel Class</div>
                                            <div className={styles["txt-div"]}>{k.Cabin}</div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className={styles["body-div"]}>
                                <div className={styles["dep-arri-div"]}>
                                    <h3>{new Date(k.DepartureTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</h3>
                                    <div className={styles["date-div"]}>{new Date(k.DepartureTime).toLocaleDateString([], { weekday: 'short', day: '2-digit', month: 'short', year: '2-digit' })}</div>
                                    <div className={styles["place-div"]}>{k.DepAirportName.split('|')[1]} <strong>[{k.DepartureCode}]</strong></div>
                                    <div className={styles["place-div"]}>{k.DepAirportName.split('|')[0]}</div>
                                    {k.DepartureTerminal && <div className={styles["place-div"]}>Terminal {k.DepartureTerminal}</div>}
                                </div>
                                <div className={styles["duration-div"]}>
                                    <h3>{k.Duration.slice(0, 2)} <span>Hr.</span> {k.Duration.slice(4, 6)} <span>Min</span></h3>
                                    <div className={styles["flight-line-div"]}></div>
                                </div>
                                <div className={styles["dep-arri-div arr-div"]}>
                                    <h3>{new Date(k.ArrivalTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</h3>
                                    <div className={styles["date-div"]}>{new Date(k.ArrivalTime).toLocaleDateString([], { weekday: 'short', day: '2-digit', month: 'short', year: '2-digit' })}</div>
                                    <div className={styles["place-div"]}>{k.ArrAirportName.split('|')[1]} <strong>[{k.ArrivalCode}]</strong></div>
                                    <div className={styles["place-div"]}>{k.ArrAirportName.split('|')[0]}</div>
                                    {k.ArrivalTerminal && <div className={styles["place-div"]}>Terminal {k.ArrivalTerminal}</div>}
                                </div>
                            </div>
                        </React.Fragment>
                    ))}

                    {flight.Notices && flight.Notices.length > 0 && (
                        <div className={styles["notice-list-div"]}>
                            {flight.Notices.map((n, index) => (
                                <div className={styles["notice-icon-text"]} key={index}>
                                    <div className={styles["icon-div"]}>INFO</div>
                                    {n.Link ? (
                                        <a href={n.Link} target="_blank" rel="noopener noreferrer" className={styles["notice-link"]}>{n.Notice}</a>
                                    ) : (
                                        <div className={styles["notice-txt"]}>{n.Notice}</div>
                                    )}
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}