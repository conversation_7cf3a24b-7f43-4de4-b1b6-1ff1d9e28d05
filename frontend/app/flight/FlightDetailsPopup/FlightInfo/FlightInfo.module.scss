@use '../../../../styles/variable.scss' as *;

.flight-details-info-div {
    color: #000;

    .trip-text {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 10px;
    }

    .connection-div {
        padding: 10px;
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 13px;
        font-weight: 400;
        color: $text_color;

        .icon-flght {
            font-size: 25px;
        }

        .icon-time {
            font-size: 17px;
        }
    }

    .notice-list-div {
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .notice-icon-text {
            display: flex;
            align-items: center;
            gap: 10px;

            .icon-div {
                display: inline-block;
                background: $button_color;
                height: 20px;
                padding: 0 5px;
                color: #fff;
                font-size: 10px;
                font-weight: 700;
                line-height: 20px;
                vertical-align: middle;
                text-transform: uppercase;
                position: relative;
                left: 0;
                top: -1px;

                &::after {
                    content: "";
                    position: absolute;
                    right: -8px;
                    border-right: 0;
                    border-top: 10px solid transparent;
                    border-bottom: 10px solid transparent;
                    border-left: 8px solid $button_color;
                    top: 0;
                }
            }

            .notice-txt {
                font-size: 13px;
                color: $text_color;
            }

            .notice-link {
                font-size: 13px;
                color: $button_color;
                text-decoration: underline;
            }
        }
    }

    .flight-card-div {
        border: 1px solid #ccc;
        background-color: white;
        border-radius: 4px;

        .top-div {
            border-radius: 4px 4px 0 0;
            padding: 10px;
            background-color: $bg_light;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .image-name-number {
                display: flex;
                align-items: center;
                gap: 10px;

                .image-div {
                    width: 30px;
                    height: 30px;

                    img {
                        width: 100%;
                        object-fit: contain;
                    }
                }

                .name-number-div {
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    color: $text_color;
                    .name-div {
                        font-size: 13px;
                        line-height: 13px;
                        font-weight: 400;
                    }

                    .number-div {
                        font-size: 11px;
                        line-height: 11px;
                        font-weight: 700;
                        color: #878686;
                    }
                }

            }

            .aircraft-travel-class-div {
                display: flex;
                align-items: center;
                gap: 20px;
                color: $text_color;

                .label-text-div {
                    display: flex;
                    flex-direction: column;
                    gap: 3px;

                    .label-txt {
                        font-size: 13px;
                        line-height: 13px;
                        font-weight: 400;
                    }

                    .txt-div {
                        font-size: 12px;
                        color: #878686;
                        line-height: 12px;
                        font-weight: 500;
                    }
                }
            }
        }

        .body-div {
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: center;

            .dep-arri-div {
                width: 35%;
                display: flex;
                flex-direction: column;
                place-content: flex-start;
                align-items: flex-end;

                h3 {
                    font-size: 25px;
                    font-weight: 400;
                }

                .date-div {
                    font-size: 15px;
                    font-weight: 500;
                }

                .place-div {
                    font-size: 12px;
                    color: #525252;
                    line-height: 16px;
                }

            }

            .arr-div {
                align-items: flex-start;
            }

            .duration-div {
                width: 30%;
                display: flex;
                flex-direction: column;
                place-content: center;
                align-items: center;

                h3 {
                    font-size: 14px;
                    font-weight: 700;

                    span {
                        color: #525252;
                        display: inline-block;
                        margin: 0 5px 0 3px;
                        font-weight: 400;
                        text-transform: capitalize;
                    }
                }

                .flight-line-div {
                    width: 60%;
                    position: relative;
                    height: 8px;

                    &::before {
                        content: "";
                        left: 0;
                        width: calc(100% - 10px);
                        height: 0;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        border-bottom: 1px dashed #7d7b89;
                        z-index: 0;
                    }

                    &::after {
                        content: "\e90a";
                        transform: rotate(90deg);
                        position: absolute;
                        right: -3px;
                        top: -6.5px;
                        font-family: icomoon !important;
                        font-size: 13px;
                        color: #c1c1c1;
                    }
                }
            }
        }
    }
}