@use '../../../styles/variable.scss' as *;
.flight-details-popup-div {
    width: 50vw;

    .common-pdng-flight-details {
        padding: 15px;
        display: flex;
        flex-direction: column;
        place-content: stretch flex-start;
        align-items: stretch;
        max-width: 100%;
        gap: 10px;
    }

    .fare-summary-fare-rule-div {
        padding: 15px;
        display: flex;
        gap: 10px;
        width: 100%;

        .fare-summary-div {
            width: 33%;
        }

        .fare-rule-div {
            width: calc(67% - 10px);
        }
    }

    .baggage-inclusion-div {
        padding: 15px;
    }
    .tab-menu-container{
        width: 100%;
        display: flex;
        padding: 10px;
        gap: 5px;
        .tab-menu-item {
            margin-left: 10px;
            text-transform: uppercase;
            font-weight: 400;
            padding: 5px;
            font-size: 13px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: background-color 0.3s ease-in-out, transform 0.2s ease-in-out, box-shadow 0.3s ease-in-out;
            background-color: transparent;
            color: #000;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 18px;
            &:hover {
                border-bottom: 2px solid $blueborder;
                transform: translateY(-2px);
            }

            &.active {
                color: #000;
                border-bottom: 2px solid $blueborder;
                transform: translateY(-2px);
                font-weight: 500;
            }

            &:hover:not(.active) {
                color: #000;
            }
        }
    }
    @media screen and (max-width:1250px){
        width: 60vw;
    }
    @media screen and (max-width:1100px){
        width: 70vw;
    }
    @media screen and (max-width:950px){
        width: 80vw;
    }
}