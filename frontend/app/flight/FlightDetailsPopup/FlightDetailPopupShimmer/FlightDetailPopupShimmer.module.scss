@use '../../../../styles/variable.scss' as *;

@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.shine {
  animation: shimmer 1.5s infinite linear;
  background: linear-gradient(
    to right,
    #e0e0e0 0%,
    #f7f7f7 50%,
    #e0e0e0 100%
);
  background-size: 200% 100%;
  border-radius: 4px;
  background-color: #f0f0f0;
}

.flight-details-info-div {
  .trip-text {
    width: 20%;
    height: 15px;
    margin-bottom: 15px;

  }

  .flight-card-div {
    border: 1px solid #ccc;
    background-color: white;
    border-radius: 4px;
    margin-bottom: 45px;
    .top-div {
      border-radius: 4px 4px 0 0;
      padding: 10px;
      background-color: $bg_light;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .image-name-number {
        display: flex;
        align-items: center;
        gap: 10px;

        .image-div {
          width: 30px;
          height: 30px;
        }

        .name-number-div {
          display: flex;
          flex-direction: column;
          gap: 5px;

          .name-div {
            height: 14px;
            width: 100px;
          }

          .number-div {
            height: 14px;
            width: 60px;
          }
        }
      }

      .aircraft-travel-class-div {
        display: flex;
        align-items: center;
        gap: 20px;

        .label-text-div {
          display: flex;
          flex-direction: column;
          gap: 3px;

          .label-txt {
            height: 14px;
            width: 100px;
          }

          .txt-div {
            height: 12px;
            width: 70px;
          }
        }
      }
    }

    .body-div {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      .dep-arri-div {
        width: 35%;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 3px;

        h3 {
          height: 25px;
          width: 25%;
        }

        .place-div {
          height: 12px;
          width: 50%;
        }
      }

      .arr-div {
        align-items: flex-start;
      }

      .duration-div {
        width: 30%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;

        h3 {
          height: 15px;
          width: 50%;
        }

        .flight-line-div {
          width: 60%;
          position: relative;
          height: 8px;

          &::before {
            content: '';
            left: 0;
            width: calc(100% - 10px);
            height: 0;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            border-bottom: 1px dashed #7d7b89;
          }

          &::after {
            content: '\e90a';
            transform: rotate(90deg);
            position: absolute;
            right: -3px;
            top: -6.5px;
            font-family: icomoon !important;
            font-size: 13px;
            color: #c1c1c1;
          }
        }
      }
    }
  }
}

.fare-details-fare-rule-div {
  .head-div-fare-rule {
    height: 20px;
    width: 50%;
    margin-bottom: 5px;
  }

  .fare-details-fare-rule-card {
    background: #fff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    .text-rules-div {
      padding: 10px;
      font-size: 12px;

      .text-icon {
          margin-bottom: 10px;
          display: flex;
          gap: 10px;
          align-items: start;
          color: #5e5e5e;

          .icon-clr {
              color: $button_color;
            svg{
              font-size: 20px;
            }
          }
      }
  }
  }

  .column-datas {
    padding: 6px 10px 3px 10px;
    border-bottom: 1px solid #ccc;

    .data-txt {
      width: 100%;
      height: 16px;
    }
  }
}

.review-fare-summary-div-shimmer {
  .head-div-fare-summary {
    height: 20px;
    width: 50%;
    margin-bottom: 5px;
  }

  .review-fare-summary-card {
    background: #fff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
  }

  .fare-expance {
    padding: 0 15px;
    border-bottom: 1px solid #ebebeb;

    .fare-head-div {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .head-text {
        width: 45%;
        height: 20px;
      }

      .head-price-txt {
        width: 30%;
        height: 20px;
      }
    }

    .sub-head-div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0 10px 20px;

      .sub-txt {
        width: 45%;
        height: 15px;
      }

      .sub-price {
        width: 30%;
        height: 15px;
      }
    }
  }

  .total-div {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .total-text {
      width: 45%;
      height: 25px;
    }

    .total-price {
      width: 35%;
      height: 25px;
    }
  }
}

.baggage-inclusion-shimmer-div {
  border: 1px solid #d0d0d0;
  border-radius: 4px;

  .bagge-column-div {
    width: 100%;
    display: flex;
    border-bottom: 1px solid #d0d0d0;

    .head-data {
      width: 32%;
      padding: 10px;
    }
    .table-head-clmn {
        border-top: none;
        background-color: $bg_light;
        border-radius: 4px 4px 0 0;
        h3 {
            font-size: 14px;
            font-weight: 500;
            color: $text_color;
            margin-bottom: 0;
        }
    }
    .value-data {
      width: 34%;
      padding: 10px;
      border-left: 1px solid #d0d0d0;
      .text-value {
        width: 100%;
        height: 20px;
        background-color: #f0f0f0;
      }
    }
  }
}
