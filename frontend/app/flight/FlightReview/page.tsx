"use client"

import InfoIcon from '@mui/icons-material/Info';
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import FlightReviewPageAdapter from 'adapters/flight-review-adapter';
import AirlineItineryCard from 'components/AirlineItineryCard/AirlineItineryCard';
import DyBottomUpPopup from 'components/DyBottomUpPopup/DyBottomUpPopup';
import { FlightSSRAddonsHelper } from 'helpers/flight-review-addon-adapter';
import FlightReviewPageHelper from 'helpers/flight-review-helper';
import { useSyncState } from 'helpers/sync-state';
import { useFlightState } from 'context/FlightState/FlightStateContext';
import { useFlightPricing } from 'hooks/useFlightPricing';
// import { SmartPricerRequest } from 'models/smart-pricer-response';
import FlightApiService from 'services/flight-api-service';
import ShimmerComponent from './FlightReviewLoader/FlightReviewLoader';
import styles from './page.module.scss';

// Placeholder components – replace with actual components in your codebase
import FlightReviewFareSummary from './ReviewFareSummary/ReviewFareSummary';
import FlightReviewTravellerForm, { travel_form } from './TravelForm/TravelForm';
import { Loader } from 'components/loader/loader';
import { BookNowBody } from 'models/flight-booking-models';

const FlightReview = () => {
  // Context and hooks
  const { flightSelection, flightPricing, clearFlightSelection } = useFlightState();
  const { callSmartPricer, callTravelChecklist, callFlightSSR, callFlightSeat } = useFlightPricing();

  // Component state
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [smartPriceLoader, setSmartPriceLoader] = useState(true);
  const [cardShimmer, setCardShimmer] = useState(true);
  const [fareSummaryLoader, setFareSummaryLoader] = useState(true);
  const [travellerShimmer, setTravellerShimmer] = useState(true);
  const [addonsShimmer, setAddonsShimmer] = useState(true);
  const [showPriceSummary, setShowPriceSummary] = useState(false);

  // Flight data state (will be populated from context)
  const [getsPriceData, setGetsPriceData, getsPriceDataRef] = useSyncState<any[]>([]);
  const [fareSummary, setFareSummary] = useState<any>();
  const [flightAddons, setFlightAddons] = useState<any>({ ssr: [], seat: [], trip: [] });
  const [ssrData, setSsrData] = useState<any[]>([]);
  const [currency, setCurrency] = useState('INR');
  const [travelForm, setTravelForm, travelFormRef] = useSyncState<any>({});
  const [travellerCheckList, setTravellerCheckList] = useState<any>();
  const [isLoad, setIsLoad] = useState<boolean>(false);

  // Error and loading states
  const [apiError, setApiError] = useState<string | null>(null);

  // const history = useHistory();
  const helper = useMemo(() => new FlightReviewPageHelper(), []);
  const adapter = useMemo(() => new FlightReviewPageAdapter(), []);
  const adonsHelper = useMemo(() => new FlightSSRAddonsHelper(), []);

  useEffect(()=>{
    setCurrency("INR")
    setShowPriceSummary(false)
  },[])

  const api = FlightApiService;
  const router = useRouter();

  const isInitialRender = useRef(true);

  const setAddonsDataFinal = useCallback(() => {
    const updatedSSRData = adonsHelper.addonsDataSet(flightAddons);
    setSsrData(updatedSSRData);
    setAddonsShimmer(false);
  }, [adonsHelper, flightAddons, setSsrData, setAddonsShimmer]);

  const changeFlight = useCallback(() => {
    clearFlightSelection();
    router.back();
  }, [router, clearFlightSelection]);

  // Initialize flight pricing when component mounts or flight selection changes
  const initializeFlightPricing = useCallback(async () => {
    if (!flightSelection.selectedFlight || !flightSelection.pricingBody) {
      // No flight selected, redirect back to flight list
      changeFlight();
      return;
    }

    try {
      setApiError(null);
      setSmartPriceLoader(true);
      setCardShimmer(true);
      setFareSummaryLoader(true);

      // Call smart pricer API
      const pricingResult = await callSmartPricer();

      // Update local state with pricing results
      setGetsPriceData(pricingResult.priceData);
      setFareSummary(pricingResult.fareSummary);
      setTravelForm(pricingResult.travelForm);

      // Call additional APIs for addons
      const travelChecklistData = await callTravelChecklist(pricingResult.priceTuis);
      setTravellerCheckList(travelChecklistData);

      const ssrData = await callFlightSSR(pricingResult.priceData, pricingResult.priceTuis, flightSelection.sectType || 'D');
      const seatData = await callFlightSeat(pricingResult.priceData, pricingResult.priceTuis, flightSelection.sectType || 'D');

      setFlightAddons({
        ssr: ssrData,
        seat: seatData,
        trip: pricingResult.priceData,
      });

      // Update loading states
      setSmartPriceLoader(false);
      setCardShimmer(false);
      setFareSummaryLoader(false);
      setTravellerShimmer(false);
      setAddonsShimmer(false);

    } catch (error) {
      console.error('Error initializing flight pricing:', error);
      setApiError(error instanceof Error ? error.message : 'Failed to load flight pricing');
      setSmartPriceLoader(false);
      setCardShimmer(false);
      setFareSummaryLoader(false);
    }
  }, [
    flightSelection,
    callSmartPricer,
    callTravelChecklist,
    callFlightSSR,
    callFlightSeat,
    changeFlight,
    setGetsPriceData,
    setFareSummary,
    setTravelForm,
    setTravellerCheckList,
    setFlightAddons,
    setSmartPriceLoader,
    setCardShimmer,
    setFareSummaryLoader,
    setTravellerShimmer,
    setAddonsShimmer,
    setApiError,
  ]);

  // Removed old callFlightSeatApi - using hook-based approach now


  // Removed old callFlightSSRApi - using hook-based approach now


  // Removed old callTravellCheckListApi - using hook-based approach now


  // COMMENTED OUT: Using callSmartPricer data instead of calling separate Gets Price API
  // const callGetsPriceApi = useCallback(async (i: number): Promise<void> => {
  //   const body = { TUI: getsPriceTuisRef.current[i] };
  //   try {
  //     const data = await api.callGetsPrice(body);
  //     setGetsPriceData([]);
  //     const x = [...getsPriceDataRef.current];
  //     x[i] = data;
  //     setGetsPriceData(x);

  //     setFlightAddons((prev: any) => ({
  //       ...prev,
  //       trip: [...prev.trip, data],
  //     }));
  //     setFareSummaryArray([
  //       ...fareSummaryArrayRef.current,
  //       helper.setFareSummary(data),
  //     ]);
  //     if (i + 1 === getsPriceTuisRef.current.length) {
  //       if (fareSummaryArrayRef.current.length > 1) {
  //         setFareSummary(helper.addFareSummaries(fareSummaryArrayRef.current));
  //       } else {
  //         setFareSummary(fareSummaryArrayRef.current[0]);
  //       }
  //       setFareSummaryLoader(false);
  //       setSmartPriceLoader(false);
  //       setCardShimmer(false);
  //       setTravelForm(adapter.paxFormSet(data.ADT, data.CHD, data.INF));
  //       callTravellCheckListApi(0);
  //     } else {
  //       await callGetsPriceApi(i + 1);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }, [
  //   api,
  //   getsPriceTuisRef,
  //   getsPriceDataRef,
  //   setGetsPriceData,
  //   setFlightAddons,
  //   fareSummaryArrayRef,
  //   helper,
  //   setFareSummaryArray,
  //   setFareSummary,
  //   setFareSummaryLoader,
  //   setSmartPriceLoader,
  //   setCardShimmer,
  //   setTravelForm,
  //   adapter,
  //   callTravellCheckListApi,
  // ]);


  // Removed duplicate callSmartPricer - using the one from useFlightPricing hook

  // Removed old session storage-based functions - using context-based approach now


  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      setIsMobile(window.innerWidth < 768);

      // Initialize flight pricing from context
      initializeFlightPricing();

      window.addEventListener('resize', checkIsMobile);

      return () => {
        window.removeEventListener('resize', checkIsMobile);
      };
    }
  }, [initializeFlightPricing]);

  // async function navigateRegister() {
  //   await router.push('/')
  // }

  const checkIsMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };



  const calculateAge = (dob: string): number => {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const mapPaxTypeToPTC = (paxType: string): string => {
    switch (paxType) {
      case 'A': return 'adult';
      case 'C': return 'child';
      case 'I': return 'infant';
      default: return 'adult';
    }
  };

  const generateGender = (title: string): string => {
    const maleTitle = ['Mr', 'Master'];
    const femaleTitle = ['Ms', 'Mrs', 'Miss'];

    if (maleTitle.includes(title)) return 'M';
    if (femaleTitle.includes(title)) return 'F';
    return 'M'; // Default
  };

  const handleBookNow = async(travellerFormData:travel_form) => {
    try {
      // Check authentication first
      const token = api.getToken();

      if (!token) {
        alert('Please login first to make a booking');
        router.push('/'); // Redirect to login page
        return;
      }

      if (!travellerFormData) {
        alert('Please fill in all required fields');
        return;
      }

      // Validate required form fields
      if (!travellerFormData.email || !travellerFormData.phone) {
        alert('Please provide email and phone number');
        return;
      }

      if (!travellerFormData.travellers || travellerFormData.travellers.length === 0) {
        alert('Please add traveller information');
        return;
      }

      // Validate traveller data
      for (let i = 0; i < travellerFormData.travellers.length; i++) {
        const traveller = travellerFormData.travellers[i];
        if (!traveller || !traveller.Title || !traveller.FName || !traveller.LName) {
          alert(`Please fill in all required fields for ${traveller?.paxHead || `Traveller ${i + 1}`}`);
          return;
        }
      }

      const formData = travellerFormData;

      // Get flight data from context and local state
      let actualFlightData;

      // First try to get data from context (preferred method)
      if (flightPricing.priceData && flightPricing.priceData.length > 0) {
        actualFlightData = flightPricing.priceData[0];

        // Validate pricing response structure
        if (!actualFlightData.TUI || !actualFlightData.Trips || !Array.isArray(actualFlightData.Trips) || actualFlightData.Trips.length === 0) {
          console.log('Flight data has invalid structure from context. Please select the flight again.', actualFlightData);
          alert('Flight booking data is incomplete. Please select the flight again.');
          return;
        }
      }
      // Fallback to local state (for backward compatibility)
      else if (getsPriceData && getsPriceData.length > 0) {
        const flightData = getsPriceData[0];

        if (flightData.TUI && flightData.Trips) {
          // This is a pricing response object
          actualFlightData = flightData;

          // Validate pricing response structure
          if (!Array.isArray(actualFlightData.Trips) || actualFlightData.Trips.length === 0) {
            console.log('Flight data has invalid Trips structure. Please select the flight again.', actualFlightData);
            alert('Flight booking data is incomplete. Please select the flight again.');
            return;
          }
        } else if (flightData.Index && flightData.MAC) {
          // This is a Journey object (selected flight), try to get from context first
          if (flightSelection.selectedFlight && flightSelection.pricingBody) {
            actualFlightData = {
              TUI: flightSelection.pricingBody[0]?.Trips?.[0]?.TUI || '',
              ADT: 1,
              CHD: 0,
              INF: 0,
              NetAmount: flightData.GrossFare || flightData.NetFare || 0,
              Trips: [{
                Journey: [flightData] // Use the selected journey
              }],
              AirlineNetFare: flightData.NetFare || flightData.GrossFare || 0,
              SSRAmount: 0,
              CrossSellAmount: 0,
              GrossAmount: flightData.GrossFare || flightData.NetFare || 0,
              Hold: false,
              ActualHoldTime: 0,
              ActualDisplayTime: 0
            };
          } else {
            console.log('Flight selection data not found in context. Please select the flight again.', flightData);
            alert('Flight booking data is incomplete. Please select the flight again.');
            return;
          }
        } else {
          console.log('Unknown flight data structure. Please select the flight again.', flightData);
          alert('Flight booking data is incomplete. Please select the flight again.');
          return;
        }
      } else {
        console.log('No flight data available. Please select the flight again.');
        alert('Flight booking data is incomplete. Please select the flight again.');
        return;
      }

      // Build flight booking object
      const flightBooking = {
        provider_info: {
          code: String(actualFlightData.Trips?.[0]?.Journey?.[0]?.Provider || "1G")
        },
        TUI: String(actualFlightData.TUI || ""),
        ADT: Number(actualFlightData.ADT || 1),
        CHD: Number(actualFlightData.CHD || 0),
        INF: Number(actualFlightData.INF || 0),
        NetAmount: Number(actualFlightData.NetAmount || 0),
        Trips: actualFlightData.Trips?.map((trip: any, tripIndex: number) => ({
          Journey: trip.Journey?.map((journey: any) => ({
            Provider: String(journey.Provider || "1G"),
            Stops: Number(journey.Stops || 0),
            Index: String(journey.Index || ""), // Added for TripJack price ID integration
            Segments: journey.Segments?.map((segment: any, segmentIndex: number) => ({
              Flight: {
                FUID: String(segment.Flight.FUID || (segmentIndex + 1).toString()),
                VAC: String(segment.Flight.VAC || ""),
                MAC: String(segment.Flight.MAC || ""),
                OAC: String(segment.Flight.OAC || ""),
                Airline: String(segment.Flight.Airline || ""),
                FlightNo: String(segment.Flight.FlightNo || ""),
                ArrivalTime: String(segment.Flight.ArrivalTime || ""),
                DepartureTime: String(segment.Flight.DepartureTime || ""),
                ArrivalCode: String(segment.Flight.ArrivalCode || ""),
                DepartureCode: String(segment.Flight.DepartureCode || ""),
                Duration: String(segment.Flight.Duration || ""),
                FareBasisCode: String(segment.Flight.FareBasisCode || ""),
                ArrAirportName: String(segment.Flight.ArrAirportName || ""),
                DepAirportName: String(segment.Flight.DepAirportName || ""),
                RBD: String(segment.Flight.RBD || ""),
                Cabin: String(segment.Flight.Cabin || "ECONOMY"),
                Refundable: String(segment.Flight.Refundable || "N")
              },
              Fares: {
                GrossFare: Number(segment.Fares?.GrossFare || journey.GrossFare || 0),
                NetFare: Number(segment.Fares?.NetFare || journey.NetFare || 0)
              }
            })) || [],
            Offer: String(journey.Offer || "DefaultOffer"),
            OrderID: Number(journey.OrderID || tripIndex),
            GrossFare: Number(journey.GrossFare || 0),
            NetFare: Number(journey.NetFare || 0)
          })) || []
        })) || [],
        AirlineNetFare: Number(actualFlightData.AirlineNetFare || actualFlightData.NetAmount || 0),
        SSRAmount: Number(actualFlightData.SSRAmount || 0),
        CrossSellAmount: Number(actualFlightData.CrossSellAmount || 0),
        GrossAmount: Number(actualFlightData.GrossAmount || actualFlightData.NetAmount || 0),
        Hold: Boolean(actualFlightData.Hold || false),
        ActualHoldTime: Number(actualFlightData.ActualHoldTime || 0),
        ActualDisplayTime: Number(actualFlightData.ActualDisplayTime || 0)
      };

      // Build travellers array
      const travellers = formData.travellers?.map((traveller: any, index: number) => ({
        ID: Number(index + 1),
        PaxID: Number(index + 1),
        Title: String(traveller.Title || "MR"),
        FName: String(traveller.FName || "John"),
        LName: String(traveller.LName || "Doe"),
        Age: Number(traveller.DOB ? calculateAge(traveller.DOB) : 25), // Default age if DOB not provided
        DOB: String(traveller.DOB || "1989-05-15"),
        Gender: String(generateGender(traveller.Title)),
        PTC: String(mapPaxTypeToPTC(traveller.paxType)),
        PLI: String(traveller.PLI || "New York"),
        PDOE: String(traveller.PDOE || "2030-12-31"),
        Nationality: String(traveller.Nationality || "US"),
        PassportNo: String(traveller.PassportNo || "A123456789"),
        VisaType: traveller.VisaType || null,
        DocType: String(traveller.PassportNo ? "Passport" : "Other")
      })) || [];

      // Build contact info from first traveller and form data
      const firstTraveller = formData.travellers?.[0];
      const contactInfo = {
        Title: String((firstTraveller as any)?.Title || "MR"),
        FName: String((firstTraveller as any)?.FName || "John"),
        LName: String((firstTraveller as any)?.LName || "Hnoo"),
        Mobile: String(formData.phone || "*********"),
        Phone: null,
        Email: String(formData.email || "<EMAIL>"),
        Address: String("123 Elm street, New York , NY ,USA"), // Not available in form
        CountryCode: String(formData.phone_code || "+91"),
        MobileCountryCode: String(formData.phone_code || "+91"),
        State: String("NY"), // Not available in form
        City: String("New York"), // Not available in form
        PIN: null,
        GSTAddress: null,
        GSTCompanyName: null,
        GSTTIN: null,
        UpdateProfile: Boolean(false),
        IsGuest: Boolean(true),
        SaveGST: Boolean(false),
        Language: null
      };

      const body: BookNowBody = {
        flight_booking: flightBooking as any,
        Travellers: travellers as any,
        ContactInfo: contactInfo as any
      };

      setIsLoad(true); // Show loading state

      const data = await api.bookNowApi(body);

      console.log('API Response:', data); // Temporary log to see the actual response format

      // Check for various success response formats
      // First check for standard success indicators
      if(data.code === 200 || data.Code === 200 || data.status === 200 || data.Status === 200 ||
         data.success === true || data.Success === true || data.code === "200" || data.Code === "200") {
        router.push('/flight/FlightSuccess');
      }
      // Check for booking response format (MasterBooking and FlightBooking objects with IDs)
      else if(data.MasterBooking && data.FlightBooking &&
              data.MasterBooking.id && data.FlightBooking.id) {
        // This indicates successful booking creation
        console.log('Booking created successfully:', {
          masterBookingId: data.MasterBooking.id,
          flightBookingId: data.FlightBooking.id,
          bookingReference: data.MasterBooking.booking_reference
        });

        // Store booking data for success page
        localStorage.setItem('dyLatestBooking', JSON.stringify(data));

        router.push('/flight/FlightSuccess');
      } else {
        alert(`Booking failed: ${data.message || data.Msg || data.error || 'Unknown error'}`);
      }
    } catch(error) {
      if (error instanceof Error) {
        alert(`Booking failed: ${error.message}`);
      } else {
        alert('Booking failed due to an unexpected error. Please try again.');
      }
    } finally {
      setIsLoad(false); // Hide loading state
    }
  }



  // const getAddonsData = (event: any[]) => {
  //   const updatedFareSummary = { ...fareSummary };
  //   updatedFareSummary.totalAmount = fareSummary.totalWithotAddons;
  //   updatedFareSummary.addons.total = 0;
  //   updatedFareSummary.addons.subFare = [];

  //   event.forEach((x) => {
  //     if (x.selectedCount > 0) {
  //       updatedFareSummary.addons.subFare.push({ title: x.title, totalPrice: x.totalPrice });
  //       updatedFareSummary.addons.total += x.totalPrice;
  //       updatedFareSummary.totalAmount += x.totalPrice;
  //     }
  //   });

  //   setFareSummary(updatedFareSummary);
  // };




  // function hidePayment() {

  // }

  // Removed automatic booking call - now only triggered by user action

  function makePayment(data:travel_form) {
    handleBookNow(data)
  }

  function showFareSummary() {

  }

  function hideFareSummary() {

  }

  return (
    <div className={styles["flight-review-div"]}>
      <div className={styles["head-ribben-div"]}>
        <div className="container">
          <h1 className={styles["head_font"]}>Review your Flight</h1>
        </div>
      </div>
      {cardShimmer && (
                  <Loader />
      )}
      <div className="container">
        <div className={styles["body-div"]}>
          <div className={styles["flight-details-div"]}>
            <section>
              <div className={styles["header-div"]}>
                <div className="linkBtn" onClick={changeFlight}>
                  Change Flight
                </div>
              </div>
              {getsPriceData && getsPriceData.length > 0 && getsPriceData.map((tripData, index) => (
                  <AirlineItineryCard
                    key={index}
                    initialTripData={tripData}
                    currency={currency}
                    isLoading={cardShimmer}
                    smartPriceLoader={smartPriceLoader} isItinerary={false} />
                ))}
            </section>

            <section>
             <FlightReviewTravellerForm
                  handleMakePayment={makePayment}
                  travellerForm={travelForm}
                  travellerCheckList={travellerCheckList}
                  pax={[1,2]}
                />
              {/* {travellerShimmer ? (
                <ShimmerComponent type="travelForm" />
              ) : (
                <FlightReviewTravellerForm
                  travellerForm={travelForm}
                  travellerCheckList={travellerCheckList}
                />
              )} */}
            </section>



            {/* Addons Section */}
            {/* <section>
              {addonsShimmer ? (
                <ShimmerComponent type="addons" />
              ) : (
                !emptyArray && (
                  <FlightAddonSection
                    ssrData={ssrData}
                    emitAddOns={getAddonsData}
                    currency={currency}
                    isMobile={isMobile} />
                )
              )}
            </section> */}
{/*
            <section>
              <div className={styles["payment-bttn-div"]}>
                <button className="dy_primary_bttn" onClick={makePayment}>
                  Make Payment
                </button>
              </div>
            </section> */}
          </div>

          <div className={styles["price-div"]}>
            <div className={styles["fare-details-data-div"]}>
              <h2>Fare Details</h2>
              <div className={styles["fare-summary-div"]}>
                {fareSummaryLoader ? (
                  <ShimmerComponent type="faresummary" />
                ) : (
                  <FlightReviewFareSummary
                    fareSummary={fareSummary}
                    currency={currency}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles["mobile-submit-div"]}>
          <div className={styles["fare-div"]}>
            <span className={styles["fr-txt"]}>Total</span>
            <div className={styles["fr-amt"]}>
              <span>{fareSummary?.totalAmount} {currency}</span>
              <InfoIcon className={styles["icon"]} onClick={showFareSummary}/>
            </div>
          </div>
          <button
            className="dy_primary_bttn"
            onClick={() => {
              // Trigger form submission by clicking the form submit button
              const formSubmitButton = document.querySelector('button[type="submit"]') as HTMLButtonElement;
              if (formSubmitButton) {
                formSubmitButton.click();
              } else {
                alert('Please fill in all required fields first');
              }
            }}
          >
            Make Payment
          </button>
        </div>

      {isMobile && (
        <DyBottomUpPopup show={showPriceSummary} hide={hideFareSummary} heading="Fare Details" >
          <FlightReviewFareSummary fareSummary={fareSummary} currency={currency} />
        </DyBottomUpPopup>
      )}
      {isLoad && (
            <Loader />
      )}
    </div>
  );
};

export default FlightReview;