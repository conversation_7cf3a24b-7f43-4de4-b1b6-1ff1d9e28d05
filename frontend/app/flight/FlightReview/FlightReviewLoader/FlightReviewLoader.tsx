import React from 'react';
import styles from './FlightReviewLoader.module.scss';

interface ShimmerComponentProps {
  type: 'card' | 'faresummary' | 'travelForm' | 'addons';
}

export function ShimmerComponent({ type }: ShimmerComponentProps) {
  if (type === 'card') {
    return (
      <div className={styles["review-flight-card-shimmer-div"]}>
      {/* Shimmer Top Section */}
      <div className={styles["avilability-tp"]}>
        <div className={styles["left"]}>
          <div className={styles["spn"]}>
            <div className={`${styles["left35"]} ${styles["shine"]}`}></div>
            <div className={`${styles["right35"]} ${styles["shine"]}`}></div>
          </div>
        </div>
        <div className={styles["right"]}>
          <div className={`${styles["bx"]} ${styles["shine"]}`}></div>
        </div>
      </div>

      {/* Shimmer Availability Section */}
      <div className={styles["availability-brdr"]}>
        {[...Array(2)].map((_, index) => (
          <div key={index}>
            {index !== 0 && (
              <div className={`${styles["flight-layout"]} ${styles["shine"]}`}></div>
            )}
            <div className={styles["avilability"]}>
              <div className={styles["left"]}>
                <div className={styles["mid"]}>
                  <div className={styles["first"]}>
                    <div className={`${styles["stp1"]} ${styles["shine"]}`}></div>
                    <div className={`${styles["stp2"]} ${styles["shine"]}`}></div>
                    <div
                      className={`${styles["stp2"]} ${styles["shine"]} ${styles["web_view"]}`}
                    ></div>
                  </div>
                  <div className={styles["last"]}>
                    <div className={styles["swp"]}>
                      <div className={`${styles["stp1"]} ${styles["shine"]}`}></div>
                    </div>
                    <div className={`${styles["stp2"]} ${styles["shine"]}`}></div>
                    <div className={styles["swp"]}>
                      <div
                        className={`${styles["stp1"]} ${styles["shine"]} ${styles["web_view"]}`}
                      ></div>
                    </div>
                    <div
                      className={`${styles["stp2"]} ${styles["shine"]} ${styles["web_view"]}`}
                    ></div>
                  </div>
                </div>
              </div>
              <div className={`${styles["mdn"]} ${styles["column"]}`}>
                <div className={`${styles["stp1"]} ${styles["shine"]}`}></div>
                <div className={`${styles["stp2"]} ${styles["shine"]}`}></div>
                <div className={`${styles["stp3"]} ${styles["shine"]}`}></div>
                <div className={`${styles["stp1"]} ${styles["shine"]}`}></div>
                <div className={`${styles["stp2"]} ${styles["shine"]}`}></div>
                <div className={`${styles["stp4"]} ${styles["shine"]}`}></div>
              </div>
              <div className={styles["last"]}>
                <div className={`${styles["bx"]} ${styles["shine"]}`}></div>
                <div className={`${styles["btm"]} ${styles["shine"]}`}></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
    );
  }

  if (type === 'faresummary') {
    return (
      <div className={styles["review-fare-summary-div-shimmer"]}>
      {/* Fare Expense Section */}
      <div className={styles["fare-expance"]}>
        <div className={styles["fare-head-div"]}>
          <div className={`${styles["head-text"]} ${styles["shine"]}`}></div>
          <div className={`${styles["head-price-txt"]} ${styles["shine"]}`}></div>
        </div>

        {/* Sub-head Divs */}
        {[...Array(3)].map((_, index) => (
          <div className={styles["sub-head-div"]} key={index}>
            <div className={`${styles["sub-txt"]} ${styles["shine"]}`}></div>
            <div className={`${styles["sub-price"]} ${styles["shine"]}`}></div>
          </div>
        ))}
      </div>

      {/* Fare Expense Summary Section */}
      <div className={styles["fare-expance"]}>
        <div className={styles["fare-head-div"]}>
          <div className={`${styles["head-text"]} ${styles["shine"]}`}></div>
          <div className={`${styles["head-price-txt"]} ${styles["shine"]}`}></div>
        </div>
      </div>

      {/* Total Section */}
      <div className={styles["total-div"]}>
        <div className={`${styles["total-text"]} ${styles["shine"]}`}></div>
        <div className={`${styles["total-price"]} ${styles["shine"]}`}></div>
      </div>
    </div>
    );
  }

  if (type === 'travelForm') {
    return (
      <div className={styles["review-travel-form-shimmer"]}>
      {/* Passenger Information Section */}
      <h2>Passenger Information</h2>
      <div className={styles["card-form"]}>
        <div className={styles["pax-travel-div"]}>
          <div>
            <div className={`${styles["pax-txt"]} ${styles["shine"]}`}></div>
            <div className={styles["form-input-div"]}>
              <div className={`${styles["form-wdth-20"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-40"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-40"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-25"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-25"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-25"]} ${styles["shine"]}`}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Details Section */}
      <h2>Contact Details</h2>
      <div className={styles["card-form"]}>
        <div className={styles["pax-travel-div"]}>
          <div>
            <div className={styles["form-input-div"]}>
              <div className={`${styles["form-wdth-40"]} ${styles["shine"]}`}></div>
              <div className={`${styles["form-wdth-40"]} ${styles["shine"]}`}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    );
  }

  if (type === 'addons') {
    return (
      <div className={styles["review-addons-shimmer-div"]}>
      {/* Addons Header */}
      <h2>Addons (Optional)</h2>

      {/* Addon Cards Section */}
      <div className={styles["card-div"]}>
        {[...Array(3)].map((_, index) => (
          <div className={styles["addon-card"]} key={index}>
            <div className={`${styles["addon-round"]} ${styles["shine"]}`}></div>
            <div className={`${styles["addon-txt"]} ${styles["shine"]}`}></div>
          </div>
        ))}
      </div>
    </div>
    );
  }

  return null;
}

export default ShimmerComponent;