.seat-select-addons-div{
    background: #fff;
    overflow: hidden;
    border-radius: 4px;
    flex-direction: column;
    box-sizing: border-box;
    display: flex;
    place-content: stretch flex-start;
    align-items: stretch;
    max-width: 100%;
    gap: 1px;
    border: 1px solid #d0d0d0;
    .card-one-div{
        width: 100%;
        .head-div{
            background-color: #d0d0d0;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            width: 100%;
            .select-count-price-div{
                color: green;
                font-size: 12px;
            }
        }
        .expand-div{
            .seat-asign-div{
                padding: 10px;
                background-color: #f0f0f0;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
                .seat-name-txt{
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    .seat-assign{
                        width: 25px;
                        height: 25px;
                    }
                    .seat-txt{
                        color: #525252;
                    }
                }
            }
            .seat{
                width: 100%;
                height: 100%;
                border-left: 3px solid;
                border-right: 3px solid;
                border-bottom: 8px solid;
                border-radius: 5px;
                font-size: 12px;
                display: flex;
                align-items: center;
                place-content: center;
            }
            .your-seat{
                background-color: #ccf59b;
                border-color: #60a905;
                cursor: pointer;
            }
            .reserved{
                background-color: #ffa1ab;
                border-color: #b9313f;
                cursor: not-allowed;
            }
            .spice-max{
                background-color: #fae1c5;
                border-color: #c3996b;
                cursor: pointer;
            }
            .aacin{
                cursor: not-allowed;
                background-color: #d1bfc4;
                border-color: #966977;
            }
            .others{
                background-color: #ededed;
                border-color: #a9a9a9;
                cursor: pointer;
            }
            .emergency{
                background-color: #befdff;
                border-color: #187477;
                cursor: pointer;
            }
            .preferred{
                background-color: #addcfe;
                border-color: #2d95de;
                cursor: pointer;
            }
            .seat-selection-div{
                margin: 10px 0;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow-y: auto;
                height: 60vh;
                .flight-div{
                    flex-direction: column;
                    box-sizing: border-box;
                    display: flex;
                    place-content: stretch flex-start;
                    align-items: stretch;
                    max-width: 100%;
                    height: 100%;
                    .top-section{
                        .cockpit {
                            height: 250px; 
                            position: relative;
                            overflow: hidden;
                            align-items: center;
                            border-bottom: 5px solid #d8d8d8;
                            justify-content: center;
                            display: flex;
                            &:before {
                              content: "";
                              display: block;
                              position: absolute;
                              top: 0;
                              left: 0;
                              height: 500px;
                              width: 100%;
                              border-radius: 50%;
                              border-right: 5px solid #d8d8d8;
                              border-left: 5px solid #d8d8d8;
                            }
                        }
                    }
                    .seat-selection-section{
                        border: 5px solid #d8d8d8;
                        padding: 30px 10px;
                        .flight-layout{
                            display: grid;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .seat-select-addons-div{
        .card-one-div{
            .expand-div{
                .seat-asign-div{
                    .seat-name-txt{
                        .seat-assign{
                            width: 20px;
                            height: 20px;
                        }
                        .seat-txt{
                            font-size: 10px;
                        }
                    }
                }
                .seat-selection-div{
                    .flight-div{
                        .top-section{
                            .cockpit {
                                height: 170px;
                                &:before {
                                    height: 350px;
                                }
                            }
                        }
                    }
                }
            }
        } 
    }
}