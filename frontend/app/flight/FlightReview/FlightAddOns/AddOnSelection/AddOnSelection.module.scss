.flight-addons-selection-card-div{
    background: #fff;
    overflow: hidden;
    border-radius: 4px;
    flex-direction: column;
    box-sizing: border-box;
    display: flex;
    place-content: stretch flex-start;
    align-items: stretch;
    max-width: 100%;
    gap: 1px;
    border: 1px solid #d0d0d0;
    .card-one-div{
        width: 100%;
        .head-div{
            background-color: #d0d0d0;
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            width: 100%;
            .select-count-price-div{
                color: green;
                font-size: 12px;
            }
        }
        .expand-div{
            padding: 10px;
            max-height: 65vh;
            overflow-y: auto;
            .list-div{
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }
            .sub-card-div{
                width: calc(25% - 7.5px);
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                overflow: hidden;
                height: auto;
                flex-direction: column;
                box-sizing: border-box;
                display: flex;
                place-content: stretch space-between;
                align-items: stretch;
                max-width: 100%;
                .top-div {
                    flex-direction: column;
                    box-sizing: border-box;
                    display: flex;
                    place-content: stretch flex-start;
                    align-items: stretch;
                    max-width: 100%;
                    .image-div{
                        width: 100%;
                        height: 97px;
                        position: relative;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                        .price-div{
                            position: absolute;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            padding: 5px;
                            background: #00000080;
                            text-align: end;
                            font-size: 16px;
                            font-weight: 500;
                            color: #fff;
                        }
                    }
                    .text-div{
                        min-height: 30px;
                        font-size: 12px;
                        color: #525252;
                        padding: 5px;
                    }
                }
                .button-div{
                    padding: 5px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    gap: 10px;
                    .bttn_add{
                        border: 1px solid #187477;
                        background-color: #187477;
                        padding: 3px 6px;
                        border-radius: 4px;
                        color: #fff;
                        cursor: pointer;
                    }
                    .remove-link{
                        cursor: pointer;
                        color: red;
                        font-size: 12px;
                        text-decoration: underline;
                    }
                    .plus_add_bttn{
                        display: flex;
                        border: 1px solid #187477;
                        border-radius: 4px;
                        overflow: hidden;
                        .bttn{
                            padding: 3px 6px;
                            color: #000;
                        }
                        .bttn-color{
                            background-color: #187477;
                            color: #fff;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
        .one-expand-div{
            max-height: max-content;
        }
    }
}

@media only screen and (max-width: 768px) {
    .flight-addons-selection-card-div{
        border-radius: 0;
        .card-one-div{
            .expand-div{
                .sub-card-div{
                    width: calc(50% - 5px);
                }
            }
        }
    }
}