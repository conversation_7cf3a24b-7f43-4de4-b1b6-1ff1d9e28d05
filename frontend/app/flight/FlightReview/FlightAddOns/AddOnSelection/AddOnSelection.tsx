import React, { useState } from 'react';
import styles from './AddOnSelection.module.scss';

interface DisplayData {
  Charge: number;
  Description: string;
  selectedCount: number;
}

interface Addon {
  from: string;
  to: string;
  selectedCount: number;
  selectedPrice: number;
  extend: boolean;
  displayDatas: DisplayData[];
}

interface SelectedAddons {
  title: string;
  image: string;
  values: Addon[];
}

interface FlightAddonsSelectionCardProps {
  selectedAddons: SelectedAddons;
  currency: string;
  onAddonChange: (ssId: number, adId: number, action: string) => void;
  expand: (n: number) => void
}

export function FlightAddonsSelectionCard({ selectedAddons, currency='INR', onAddonChange, expand }: FlightAddonsSelectionCardProps) {

  function expandedValues(id: number) {
    expand(id);
  }

  function outFunction(s: number, a: number, t: string) {
    onAddonChange(s, a, t)
  }

  return (
    <div className={styles["flight-addons-selection-card-div"]}>
      {selectedAddons?.values?.map((addon, ssId) => (
        <div className={styles["card-one-div"]} key={ssId}>
          <div className={styles["head-div"]} onClick={() => expandedValues(ssId)}>
            <h3>{addon?.from}-{addon?.to}</h3>
            {addon?.selectedCount > 0 && (
              <div className={styles["select-count-price-div"]}>
                {addon?.selectedCount} {selectedAddons?.title} Added <strong>{addon?.selectedPrice}</strong>
              </div>
            )}
            <h3>{addon.extend ? '-' : '+'}</h3>
          </div>

          {selectedAddons.values.length > 0 && (
            <div className={`expand-div ${selectedAddons?.values.length > 0 ? 'one-expand-div' : ''}`}>
              <div className={styles["list-div"]}>
                {addon?.displayDatas?.map((data, adId) => (
                  <div className={styles["sub-card-div"]} key={adId}>
                    <div className={styles["top-div"]}>
                      <div className={styles["image-div"]}>
                        <img src={`/assets/images/${selectedAddons?.image}`} alt="" />
                        <div className={styles["price-div"]}>{(data?.Charge)}</div>
                      </div>
                      <div className={styles["text-div"]}>
                        {data?.Description}
                      </div>
                    </div>

                    <div className={styles["button-div"]}>
                      {data?.selectedCount === 0 ? (
                        <div className={styles["bttn_add"]} onClick={() => onAddonChange(ssId, adId, 'plus')}>
                          Add
                        </div>
                      ) : (
                        <>
                          <div className={styles["remove-link"]} onClick={() => onAddonChange(ssId, adId, 'remove')}>
                            Remove
                          </div>
                          <div className={styles["plus_add_bttn"]}>
                            <div className={styles["bttn bttn-color"]} onClick={() => onAddonChange(ssId, adId, 'minus')}>
                              -
                            </div>
                            <div className={styles["bttn"]}>{data?.selectedCount}</div>
                            <div className={styles["bttn bttn-color"]} onClick={() => onAddonChange(ssId, adId, 'plus')}>
                              +
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

export default FlightAddonsSelectionCard;