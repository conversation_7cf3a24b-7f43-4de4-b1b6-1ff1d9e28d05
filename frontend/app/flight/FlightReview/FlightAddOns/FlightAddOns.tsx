import React, { useState } from 'react';
import styles from './FlightAddons.module.scss';
import AddonsFareSummary from './AddOnSplitup/AddOnSplitup';
import FlightAddonsSelectionCard from './AddOnSelection/AddOnSelection';
import SeatSelectAddons from './SeatSelection/SeatSelection';

interface SSRData {
  title: string;
  icon: string;
  selectedCount: number;
  totalPrice: number;
  type: string;
  values: any[];
  image:string;
}

interface FlightAddonSectionProps {
  ssrData: SSRData[];
  currency: string;
  isMobile: boolean;
  emitAddOns: (ssrData:SSRData[])=>void;
}


export function FlightAddonSection({
  ssrData,
  currency,
  isMobile,
  emitAddOns,
}: FlightAddonSectionProps) {
  const [isOpenPopup, setIsOpenPopup] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const [isSelectBlock, setIsSelectBlock] = useState(false);
  const [errorTxt, setErrorTxt] = useState('');

  const FlightCurrencyConverter = (amount: number): string => {
    // Placeholder for currency conversion logic
    return `${amount} ${currency}`;
  };


  function handleExpand(){

  }

  function handleOutFunc(){

  }

  function handleSeatSelect(){

  }

  

  function openPopup(id:number){
    setSelectedId(id)
    if(ssrData && ssrData[id]){
      ssrData[id]!.values!.forEach((x:any,i:number)=>{
        if(i == 0){
          x.extend = true;
        }else{
          x.extend = false;
        }
      })
    }
    setIsOpenPopup(true)
  }

  function expandedValues(id:number){
    if(selectedId && ssrData && ssrData[selectedId]){
      ssrData[selectedId]!.values!.forEach((x:any,i:number)=>{
        if(i == id){
          x.extend = !x.extend;
        }else{
          x.extend = false;
        }
      })
    }
  }

  function outFunctionAddons(evnt:{"ssId":number,"adId":number,"type":string}){
    if(evnt?.type == 'plus'){
      plusSSRAddons(evnt.ssId,evnt.adId);
    }else if(evnt?.type == 'minus'){
      minusSSRAddons(evnt.ssId,evnt.adId);
    }else if(evnt?.type == 'remove'){
      removeSSRAddons(evnt.ssId,evnt.adId)
    }
  }

  function plusSSRAddons(ssId:number,adId:number){
    if(selectedId && ssrData && ssrData[selectedId]){
      const paxCount = ssrData[selectedId]!.values[ssId]!.adult + ssrData[selectedId]!.values[ssId]!.child;
      if(ssrData[selectedId]!.values[ssId]!.selectedCount < paxCount){
        ssrData[selectedId]!.selectedCount += 1;
        ssrData[selectedId]!.values[ssId].selectedCount += 1;
        ssrData[selectedId]!.values[ssId].displayDatas[adId].selectedCount += 1;

        const price = ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.Charge
        ssrData[selectedId]!.values[ssId]!.selectedPrice +=  price;
        ssrData[selectedId]!.totalPrice += price;
      }else{
        setErrorTxt('Sorry, you cannot select more than '+paxCount+' '+ssrData[selectedId]!.title)
        setIsSelectBlock(true)
        setTimeout(() => {
          setIsSelectBlock(false)
        }, 2000);
      }
    }
  }

  function minusSSRAddons(ssId:number,adId:number){
    if(selectedId && ssrData && ssrData[selectedId]){
      ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.selectedCount -= 1;
      ssrData[selectedId]!.values[ssId]!.selectedCount -= 1;
      ssrData[selectedId]!.selectedCount -= 1;

      const price = ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.Charge;
      ssrData[selectedId]!.values[ssId]!.selectedPrice -=  price;
      ssrData[selectedId]!.totalPrice -=  price;
    }
  }

  function removeSSRAddons(ssId:number,adId:number){
    if(ssrData && selectedId && ssrData[selectedId]){
      const count = ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.selectedCount;
      ssrData[selectedId]!.selectedCount -= count;
      ssrData[selectedId]!.values[ssId]!.selectedCount -= count;
      ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.selectedCount = 0;
      
      const price = ssrData[selectedId]!.values[ssId]!.displayDatas[adId]!.Charge;
      ssrData[selectedId]!.totalPrice -= (price*count);
      ssrData[selectedId]!.values[ssId]!.selectedPrice -= (price*count);
    }
  }

  function removeAllIndexAddon(i: number) {
    if(ssrData && ssrData[i]){
      ssrData[i]!.totalPrice = 0;
      ssrData[i]!.selectedCount = 0;
      ssrData[i]!.values!.forEach((x: any) => {
        x.selectedCount = 0;
        x.selectedPrice = 0;
        x.displayDatas!.forEach((y: any) => {
          if (ssrData[i]!.type == '9') {
            y.select = false;
          } else {
            y.selectedCount = 0;
          }
        })
      });
      emitAddonsFareSummary();
    }
  }

  function seatSelection(evnt:{segId:number,seatId:number}){
    if(selectedId && ssrData && ssrData[selectedId]){
      const value = ssrData[selectedId]!.values[evnt.segId]!.displayDatas[evnt.seatId];
      const paxCount = ssrData[selectedId]!.values[evnt.segId]!.adult + ssrData[selectedId]!.values[evnt.segId]!.child;
      if(value.SeatStatus == 'Open'){
        const price = +value.Fare;

        if(value.select){
          ssrData[selectedId]!.values[evnt.segId]!.displayDatas[evnt.seatId]!.select = false;
          ssrData[selectedId]!.values[evnt.segId]!.selectedCount -= 1;
          ssrData[selectedId]!.selectedCount -= 1;
          ssrData[selectedId]!.values[evnt.segId]!.selectedPrice -= price;
          ssrData[selectedId]!.totalPrice -= price;
        }else{
          if(ssrData[selectedId]!.values[evnt.segId]!.selectedCount < paxCount){
            ssrData[selectedId]!.values[evnt.segId]!.displayDatas[evnt.seatId]!.select = true;
            ssrData[selectedId]!.values[evnt.segId]!.selectedCount += 1;
            ssrData[selectedId]!.selectedCount += 1;
            ssrData[selectedId]!.values[evnt.segId]!.selectedPrice += price;
            ssrData[selectedId]!.totalPrice += price;
          }else{
            setErrorTxt('Sorry, you cannot select more than '+paxCount+' '+ssrData[selectedId]!.title)
            setIsSelectBlock(true)
            setTimeout(() => {
              setIsSelectBlock(false)
            }, 2000);
          }
        }
      }
    }
  }

  function closePopup(){
    setIsOpenPopup(false)
    emitAddonsFareSummary();
  }

  function emitAddonsFareSummary(){
    emitAddOns(ssrData)
  }

  return (
    <div className={styles["flight-addon-section-div"]}>
      <h2>Addons (Optional)</h2>
      <div className={styles["addons-card"]}>
        {ssrData.map((addon, index) => (
          <div className={styles["addon-flight"]} key={index}>
            {addon.values.length > 0 && (
              <>
                <div className={styles["addon-round-div"]} onClick={() => openPopup(index)}>
                  <span className={`icon ${addon?.icon}`}></span>
                  <h5>{addon?.title}</h5>
                </div>
                {addon?.selectedCount === 0 ? (
                  <div onClick={() => openPopup(index)}>
                    <span className={styles["fa fa-plus-square-o"]}></span> Add {addon?.title}
                  </div>
                ) : (
                  <div className={styles["selected-addons-data"]}>
                    <div className={styles["add-addons-data"]}>
                      <span className={styles["fa fa-plus-circle"]}></span> {addon?.selectedCount} {addon?.title} Added
                    </div>
                    <div className={styles["total-amnt"]}>
                      <div>{FlightCurrencyConverter(addon?.totalPrice)}</div>
                      <span>
                        <AddonsFareSummary isMobile={isMobile} fareSummaryData={addon} currency={currency} />
                      </span>
                    </div>
                    <div>
                      <span className={styles["remove-txt"]} onClick={() => removeAllIndexAddon(index)}>
                        Remove
                      </span>{' '}
                      |{' '}
                      <span className={styles["change-txt"]} onClick={() => openPopup(index)}>
                        Change
                      </span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        ))}
      </div>

      {selectedId !== null && (
        <div className={`right-side-popup ${isOpenPopup ? 'show' : ''}`}>
          {isOpenPopup && (
            <div className={styles["flight-addons-pop-up"]}>
              <div className={styles["body-data-div"]}>
                {isSelectBlock && (
                  <div className={styles["error-text-div"]}>
                    <span className={styles["fa fa-info-circle"]}></span> {errorTxt}
                  </div>
                )}

                {ssrData[selectedId]?.type !== '9' ? (
                  <FlightAddonsSelectionCard selectedAddons={ssrData[selectedId]!} expand={handleExpand} onAddonChange={handleOutFunc} currency={currency} />
                ) : (
                  <SeatSelectAddons flightsSeats={ssrData[selectedId]!} expand={handleExpand} onSeatSelect={handleSeatSelect} currency={currency} />
                )}
              </div>

              <div className={styles["footer-data-div"]}>
                <div className={styles["price-totoal-div"]}>
                  <h3>Total amount for your {ssrData[selectedId]?.title}:</h3>
                  <div className={styles["total-price"]}>
                    <div>{FlightCurrencyConverter(ssrData[selectedId]!.totalPrice!)}</div>
                    {ssrData[selectedId]!.totalPrice && ssrData[selectedId]!.totalPrice > 0 && (
                      <AddonsFareSummary isMobile={isMobile} fareSummaryData={ssrData[selectedId]!} currency={currency} />
                    )}
                  </div>
                </div>
                <button className={styles["main_button"]} onClick={closePopup}>
                  Done
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default FlightAddonSection;