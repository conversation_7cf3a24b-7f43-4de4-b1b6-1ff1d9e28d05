.addons-fare-summary-tooltip-bttn{
    position: relative;
    font-weight: normal;
    .info-icon{
        color: #c3996b;
        cursor: pointer;
    }
    .addons-fare-summary-tooltip{
        position: absolute;
        bottom: 20px;
        right: -115px;
        background-color: #e9bc8d;;
        border: 1px solid #c3996b;
        font-size: 12px;
        border-radius: 4px;
        width: 250px;
        opacity: 0;
        z-index: -1;
        -webkit-transition: all 0.3s ease;
	    -moz-transition: all 0.3s ease-in-out;
	    -o-transition: all 0.3s ease;
	    transition: all 0.3s ease-in-out;
        &::after{
            border-color: #c3996b #00000000;
	        border-style: solid;
	        border-width: 5px 5px 0;
	        bottom: -5px;
	        content: "";
	        display: block;
	        left: 50%;
	        position: absolute;
	        width: 0;
        }
    }
    &:hover{
        .addons-fare-summary-tooltip{
            opacity: 1;
            -webkit-transition: all 0.2s ease;
            -moz-transition: all 0.2s ease;
            -o-transition: all 0.2s ease;
            transition: all 0.2s ease;
            z-index: 99;
        }
    }
}

.fare-summary-addons-datas{
    width: 100%;
    text-align: start;
    font-size: 12px;
    .sector-vise-div{
        padding: 3px 5px;
        border-bottom: 1px solid #c3996b;
        .sector-div{
            font-weight: 500;
        }
        .pice-value{
            padding: 2px 0;
            border-top: 1px dashed #c3996b;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .left{
                width: 60%;
                color: #525252;
            }
            .right{
                text-align: end;
                width: 39%;
            }
        }
    }
    .total-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        padding: 3px 5px;
        font-weight: 500;
    }
}

@media only screen and (max-width: 768px) {
    .fare-summary-addons-datas{
        .sector-vise-div{
            padding: 0 5px;
            border-bottom: 1px solid #d0d0d0;
            .sector-div{
                padding: 5px;
            }
            .pice-value{
                padding: 5px;
                border-top: 1px dashed #d0d0d0;
                font-weight: 400;
            }
        }
        .total-div{
            padding: 5px 10px;
        }
    }
}