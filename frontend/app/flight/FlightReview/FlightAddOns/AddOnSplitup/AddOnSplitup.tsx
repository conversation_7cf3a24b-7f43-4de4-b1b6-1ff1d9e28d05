import React, { useState } from 'react';
import styles from './AddOnSplitup.module.scss';

interface DisplayData {
  Description: string;
  Charge: number;
  selectedCount: number;
}

interface FareSummaryValue {
  from: string;
  to: string;
  selectedCount: number;
  selectedPrice: number;
  displayDatas: DisplayData[];
}

interface FareSummaryData {
  type: string;
  values: FareSummaryValue[];
  totalPrice: number;
}

interface AddonsFareSummaryProps {
  fareSummaryData: FareSummaryData;
  currency: string;
  isMobile: boolean;
}

export function AddonsFareSummary({ fareSummaryData, currency, isMobile }: AddonsFareSummaryProps) {
  const [showPopup, setShowPopup] = useState(false);

  const FlightCurrencyConverter = (amount: number): string => {
    // Placeholder for currency conversion logic
    return `${amount} ${currency}`;
  };

  const handleShow = () => setShowPopup(true);
  const handleHide = () => setShowPopup(false);

  const renderFareSummaryContent = () => (
    <div className={styles["fare-summary-addons-datas"]}>
      {fareSummaryData.type !== '9' ? (
        fareSummaryData?.values?.map((f, index) => (
          f.selectedCount > 0 && (
            <div className={styles["sector-vise-div"]} key={index}>
              <div className={styles["sector-div"]}>{f?.from} - {f?.to}</div>
              {f.displayDatas.map((d, dIndex) => (
                d.selectedCount > 0 && (
                  <div className={styles["pice-value"]} key={dIndex}>
                    <div className={styles["left"]}>{d?.Description} X {d.selectedCount}</div>
                    <div className={styles["right"]}>{FlightCurrencyConverter(d?.Charge * d.selectedCount)}</div>
                  </div>
                )
              ))}
            </div>
          )
        ))
      ) : (
        fareSummaryData?.values?.map((f, index) => (
          f.selectedCount > 0 && (
            <div className={styles["sector-vise-div"]} key={index}>
              <div className={styles["sector-div"]}>{f?.from} - {f?.to}</div>
              <div className={styles["pice-value"]}>
                <div className={styles["left"]}>{f?.selectedCount} Seat</div>
                <div className={styles["right"]}>{FlightCurrencyConverter(f?.selectedPrice)}</div>
              </div>
            </div>
          )
        ))
      )}
      <div className={styles["total-div"]}>
        <div>Total</div>
        <div>{FlightCurrencyConverter(fareSummaryData?.totalPrice)}</div>
      </div>
    </div>
  );

  return (
    <>
      <div className={styles["addons-fare-summary-tooltip-bttn"]}>
        <span className={styles["fa fa-info-circle info-icon"]} onClick={handleShow}></span>
        {!isMobile && (
          <span className={styles["addons-fare-summary-tooltip"]}>
            {renderFareSummaryContent()}
          </span>
        )}
      </div>

      {isMobile && (
        <div className={`mobile-popup ${showPopup ? 'show' : ''}`}>
          {showPopup && (
            <div className={styles["mobile-popup-content"]}>
              <button className={styles["close-btn"]} onClick={handleHide}>Close</button>
              {renderFareSummaryContent()}
            </div>
          )}
        </div>
      )}
    </>
  );
}

export default AddonsFareSummary;