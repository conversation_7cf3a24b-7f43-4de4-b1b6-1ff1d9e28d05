@use '../../../../styles/variable.scss' as *;

.flight-addon-section-div{
    h2{
        font-size: 19px;
        font-weight: 500;
        color: #000;
    }
    .addons-card{
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        padding: 15px;
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        .addon-flight{
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            .addon-round-div{
                width: 150px;
                height: 150px;
                border: 3px solid $text_color;
                border-radius: 50%;
                display: flex;
                flex-direction: column;
                box-sizing: border-box;
                place-content: center;
                align-items: center;
                background-color: #ededed;
                gap: 10px;
                .icon{
                    font-size: 60px;
                    color: $text_color;
                }
                h5{
                    font-size: 14px;
                    color: $text_color;
                    font-weight: 500;
                }
            }
            .selected-addons-data{
                text-align: center;
                font-size: 12px;
                cursor: auto;
                .add-addons-data{
                    color: green;
                }
                .total-amnt{
                    font-weight: 500;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    justify-content: center;
                }
                .remove-txt{
                    color: red;
                    cursor: pointer;
                }
                .change-txt{
                    cursor: pointer;
                    color: $primary_color;
                }
            }

        }

    }
}

.flight-addons-pop-up{
    width: 62vw;
    .body-data-div{
        height: calc(100vh - 105px);
        padding: 20px;
        overflow-y: auto;
        position: relative;
    }
    .error-text-div{
        position: fixed;
        right: 0;
        padding: 15px;
        z-index: 999;
        background-color: #fbcbcb;
        border: 1px solid #ff7a7a;
        border-radius: 4px;
        &:hover{
            opacity: 1 !important;
        }
    }
    .footer-data-div{
        height: 60px;
        background-color: #f0f0f0;
        width: 100%;
        padding: 0 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .price-totoal-div{
            display: flex;
            align-items: center;
            gap: 10px;
            h3{
                font-weight: 500;
            }
            .total-price{
                font-size: 18px;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .flight-addon-section-div{
        h2{
            font-size: 17px;
        }
        .addons-card{
            padding: 10px;
            gap: 10px;
            .addon-flight{
                width: calc(50% - 5px);
                .addon-round-div{
                    width: 100px;
                    height: 100px;
                    .icon{
                        font-size: 30px;
                    }
                    h5{
                        font-size: 12px;
                    }
                }
            }

        }
    }

    .flight-addons-pop-up{
        width: 100vw;
        .body-data-div{
            padding: 0;
        }
        .footer-data-div{
            padding: 0 10px;
            .price-totoal-div{
                h3{
                    font-size: 12px;
                    max-width: 120px;
                    line-height: 12px;
                }
                .total-price{
                    font-size: 16px;
                }
            }
        }
    }
}