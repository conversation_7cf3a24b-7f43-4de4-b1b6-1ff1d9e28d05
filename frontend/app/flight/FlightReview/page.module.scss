@use '../../../styles/variable.scss' as *;

.flight-review-div{
    position: relative;
    .head-ribben-div {
        width: 100%;
        background: $secondary_color;
        padding: 10px 0;
        margin-bottom: 10px;
        color: $text_color;

        h1 {
            font-size: 18px;
            line-height: 18px;
            margin-bottom: 0;
        }
    }

    @media only screen and (max-width: 768px) {
        .head-ribben-div {
            h1 {
                font-size: 16px;
                line-height: 16px;
            }
        }
    }
    h2{
        font-size: 19px;
        font-weight: 500;
        color: #000;
    }
    .body-div{
        display: flex;
        gap: 15px;
        .flight-details-div{
            width: calc(75% - 20px);
            .header-div{
                margin-bottom: 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                h2{
                    margin-bottom: 0;
                }
            }
            .payment-bttn-div{
                display: flex;
                align-items: center;
                justify-content: end;
                padding: 10px 0;
            }
        }
        .price-div{
            width: 25%;
            .fare-details-data-div{
                position: sticky;
                top: 10px;
            }
            .fare-summary-div{
                margin-top: 10px;
            }
        }
    }

    .mobile-submit-div{
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9;
        height: 60px;
        background-color: #ffff;
        box-shadow: 0 -5px 5px -5px #d0d0d0;
        display: none;
        align-items: center;
        justify-content: space-between;
        padding: 5px 15px;
        @media only screen and (max-width: 768px) {
            display: flex;
        }
        .fare-div{
            display: flex;
            flex-direction: column;
            width: 50%;
            .fr-txt{
                font-size: 12px;
                color: $text_color;
            }
            .fr-amt{
                font-size: 17px;
                font-weight: 500;
                color: #000;
                display: flex;
                align-items: center;
                gap: 10px;
                .icon{
                    color: $text_color;
                }
            }
        }
    }
}

@media only screen and (max-width: 768px) {
    .flight-review-div{
        h2{
            font-size: 17px;
        }
        .body-div{
            display: block;
            .flight-details-div{
                width: 100%;
                padding-bottom: 70px;
                .header-div{
                    margin-bottom: 0;
                }
                .payment-bttn-div{
                    display: none;
                }
            }
            .price-div{
                width: 100%;
                display: none;
            }
        }
    }
}