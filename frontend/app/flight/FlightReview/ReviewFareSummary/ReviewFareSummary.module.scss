.flight-review-fare-summary-div{
    background: #fff;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    .total-div{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 15px 20px;
        h3{
            font-size: 19px;
            font-weight: 600;
            margin-bottom: 0;
        }
    }
    h5 {
        font-size: 15px;
        font-weight: 500;
        color: #000;
        padding-left: 10px;
        white-space: nowrap;
        margin-bottom: 0;
    }
    .head{
        font-size: 15px;
                font-weight: 400;
                color: #000;
                padding-left: 10px;
                position: relative;
                align-items: center;
                text-transform: capitalize;
    }
    .datas{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 5px;
        padding: 0 15px 10px 33px;
        p {
            color: #525252;
            font-size: 13px;
            font-weight: 400;
        }

        h6 {
            color: #525252;
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 0;
        }
    }
    .mat-expansion-panel {
        box-shadow: none;
        border-radius: 4px;
        border-bottom: 1px solid #ebebeb;
    
        .mat-expansion-panel-header {
            -webkit-box-orient: horizontal;
            -webkit-box-direction: reverse;
            -ms-flex-direction: row-reverse;
            flex-direction: row-reverse;
            padding: 0 15px;
    
            .mat-expansion-panel-header-title {
                font-size: 15px;
                font-weight: 400;
                color: #000;
                padding-left: 10px;
                position: relative;
                align-items: center;
                text-transform: capitalize;
    
                .material-icons {
                    height: 16px;
                    line-height: 16px;
                    width: 18px;
                    font-size: 20px;
                }
            }
    
            
        }

        &.mat-expansion-panel-body {
            padding: 0 16px 15px 12px;
        }
    
        &.mat-expansion-panel-spacing {
            margin: 0;
        }

        
    }
}

::ng-deep .mat-expansion-panel-body {
    padding: 0 !important;
}

@media only screen and (max-width: 768px) {
    .flight-review-fare-summary-div{
        border: none;
        border-radius: 0;
    }
}