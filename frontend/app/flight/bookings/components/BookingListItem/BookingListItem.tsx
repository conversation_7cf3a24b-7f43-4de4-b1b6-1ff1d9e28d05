import React from 'react'
import { BookingList } from 'models/flight-booking-models'
import styles from '../../page.module.scss'

interface BookingListItemProps {
  booking: BookingList
  onViewItinerary: () => void
}

const BookingListItem: React.FC<BookingListItemProps> = ({ booking, onViewItinerary }) => {
  const { MasterBooking, FlightBooking } = booking

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return styles.confirmed
      case 'pending':
        return styles.pending
      case 'cancelled':
        return styles.cancelled
      case 'completed':
        return styles.completed
      default:
        return styles.pending
    }
  }

  const getFirstTrip = () => {
    return FlightBooking.Trips?.[0]
  }

  const getFirstSegment = () => {
    const firstTrip = getFirstTrip()
    return firstTrip?.Segments?.[0]
  }

  const getFirstFlight = () => {
    const firstSegment = getFirstSegment()
    return firstSegment?.Flight
  }

  const firstFlight = getFirstFlight()
  const totalPassengers = FlightBooking.ADT + FlightBooking.CHD + FlightBooking.INF

  return (
    <>
      {/* Desktop/Tablet View */}
      <div className={styles.bookingItem}>
        <div className={styles.bookingRow}>
          {/* Flight Info */}
          <div className={styles.flightInfo}>
            {firstFlight && (
              <>
                <div className={styles.routeDisplay}>
                  <span className={styles.airportCode}>{firstFlight.DepartureCode}</span>
                  <span className={styles.arrow}>→</span>
                  <span className={styles.airportCode}>{firstFlight.ArrivalCode}</span>
                </div>
                <div className={styles.flightDetails}>
                  <img
                    src={`/assets/images/AirlineLogo/${firstFlight.MAC}.png`}
                    alt={firstFlight.Airline}
                    className={styles.airlineLogo}
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none'
                    }}
                  />
                  <span>{firstFlight.Airline} {firstFlight.FlightNo}</span>
                  <span>•</span>
                  <span>{formatTime(firstFlight.DepartureTime)}</span>
                </div>
              </>
            )}
          </div>

          {/* Booking Info */}
          <div className={styles.bookingInfo}>
            <div className={styles.bookingRef}>#{MasterBooking.booking_reference}</div>
            <div className={styles.bookingDate}>{formatDate(MasterBooking.created_at)}</div>
          </div>

          {/* Passengers */}
          <div className={styles.passengerCount}>
            {totalPassengers} {totalPassengers === 1 ? 'Passenger' : 'Passengers'}
          </div>

          {/* Amount */}
          <div className={styles.totalAmount}>
            ₹{FlightBooking.GrossAmount?.toLocaleString()}
          </div>

          {/* Status & Action */}
          <div className={styles.statusColumn}>
            <div className={`${styles.statusBadge} ${getStatusClass(MasterBooking.status)}`}>
              {MasterBooking.status}
            </div>
            <button
              className={styles.viewButton}
              onClick={onViewItinerary}
              aria-label="View itinerary"
            >
              View Details
            </button>
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className={styles.mobileCard} onClick={onViewItinerary}>
        <div className={styles.mobileHeader}>
          <div className={styles.bookingInfo}>
            <div className={styles.bookingRef}>#{MasterBooking.booking_reference}</div>
            <div className={styles.bookingDate}>{formatDate(MasterBooking.created_at)}</div>
          </div>
          <div className={`${styles.statusBadge} ${getStatusClass(MasterBooking.status)}`}>
            {MasterBooking.status}
          </div>
        </div>

        <div className={styles.mobileContent}>
          {firstFlight && (
            <div className={styles.mobileRow}>
              <span className={styles.label}>Route</span>
              <span className={styles.value}>
                {firstFlight.DepartureCode} → {firstFlight.ArrivalCode}
              </span>
            </div>
          )}

          <div className={styles.mobileRow}>
            <span className={styles.label}>Passengers</span>
            <span className={styles.value}>{totalPassengers}</span>
          </div>

          <div className={styles.mobileRow}>
            <span className={styles.label}>Amount</span>
            <span className={styles.value}>₹{FlightBooking.GrossAmount?.toLocaleString()}</span>
          </div>

          {firstFlight && (
            <div className={styles.mobileRow}>
              <span className={styles.label}>Flight</span>
              <span className={styles.value}>
                {firstFlight.Airline} {firstFlight.FlightNo}
              </span>
            </div>
          )}

          <div className={styles.mobileRow}>
            <span className={styles.label}>Payment</span>
            <span className={`${styles.value} ${getStatusClass(MasterBooking.payment_status)}`}>
              {MasterBooking.payment_status}
            </span>
          </div>
        </div>
      </div>
    </>
  )
}

export default BookingListItem
