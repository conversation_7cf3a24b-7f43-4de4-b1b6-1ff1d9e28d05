@use '../../../../../styles/variable.scss' as *;

.itineraryDetails {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media screen and (max-width: $isMobile) {
    padding: 0 10px;
    gap: 15px;
  }
}

// Section Titles - compact style
.sectionTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: $primary_color;
  margin-bottom: 15px;
  border-bottom: 2px solid $primary_color;
  padding-bottom: 8px;
  position: relative;

  @media screen and (max-width: $isMobile) {
    font-size: 1.2rem;
    margin-bottom: 12px;
    padding-bottom: 6px;
  }
}

// Header Card Styles - compact design
.headerCard {
  background: $white;
  border-radius: $radius;
  padding: 15px;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  border: 1px solid $bordercolor;
  position: relative;
  transition: all 0.1s ease-in;

  &:hover {
    box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
  }

  @media screen and (max-width: $isMobile) {
    padding: 12px;
    border-radius: 4px;
  }
}

.headerContent {
  margin-bottom: 15px;

  @media screen and (max-width: $isMobile) {
    margin-bottom: 12px;
  }
}

.titleRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  @media screen and (max-width: $isMobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: $primary_color;
    margin: 0;

    @media screen and (max-width: $isMobile) {
      font-size: 1.6rem;
    }
  }
}

.bookingRef {
  display: flex;
  align-items: center;
  gap: 8px;

  .refLabel {
    font-size: 14px;
    color: $blackgrey;
    font-weight: 500;
  }

  .refValue {
    font-size: 15px;
    font-weight: 600;
    color: $primary_color;
    font-family: 'Courier New', monospace;
    background: $bg_light_color;
    padding: 4px 8px;
    border-radius: $radius;
    border: 1px solid $bordercolor;
  }
}

.statusSection {
  display: flex;
  gap: 8px;
  align-items: center;

  @media screen and (max-width: $isMobile) {
    flex-wrap: wrap;
    gap: 6px;
  }
}

.printButton {
  background: $white;
  border: 1px solid $bordercolor;
  color: $text_color;
  padding: 8px 12px;
  border-radius: $radius;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.1s ease-in;
  display: flex;
  align-items: center;
  gap: 5px;

  &:hover {
    background: $bg_light_color;
    box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
  }

  @media print {
    display: none;
  }
}

.statusBadge,
.paymentBadge {
  padding: 6px 12px;
  border-radius: $radius;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 80px;
  text-align: center;
  border: 1px solid;

  &.confirmed {
    background: $success_color;
    color: $white;
    border-color: $success_color;
  }

  &.pending {
    background: $yellow;
    color: $white;
    border-color: $yellow;
  }

  &.cancelled {
    background: $error_color;
    color: $white;
    border-color: $error_color;
  }

  &.completed {
    background: $blue;
    color: $white;
    border-color: $blue;
  }
}

.quickStats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding-top: 15px;
  border-top: 1px solid $bordercolor;

  @media screen and (max-width: $isMobile) {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

.stat {
  text-align: center;
  padding: 12px 8px;
  background: $bg_light_color;
  border-radius: $radius;
  border: 1px solid $bordercolor;

  .statValue {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: $primary_color;
    margin-bottom: 4px;

    @media screen and (max-width: $isMobile) {
      font-size: 15px;
    }
  }

  .statLabel {
    font-size: 11px;
    color: $blackgrey;
    font-weight: 500;
    text-transform: uppercase;
  }
}

// Journey Section Styles - compact design
.journeySection {
  .tripCard {
    background: $white;
    border-radius: $radius;
    padding: 12px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border: 1px solid $bordercolor;
    margin-bottom: 12px;
    transition: all 0.1s ease-in;

    &:hover {
      box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tripHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid $bordercolor;

    h3 {
      font-size: 15px;
      font-weight: 600;
      color: $primary_color;
      margin: 0;
    }

    .tripMeta {
      display: flex;
      gap: 8px;
      font-size: 13px;
      color: $blackgrey;

      .stops {
        font-weight: 600;
      }

      .provider {
        color: $primary_color;
        font-weight: 600;
      }
    }
  }

  .segmentCard {
    background: $secondary_color;
    border-radius: $radius;
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid $bordercolor;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .segmentHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .segmentInfo {
      display: flex;
      align-items: center;
      gap: 10px;

      .segmentNumber {
        font-size: 11px;
        color: $blackgrey;
        font-weight: 600;
        text-transform: uppercase;
      }

      .flightNumber {
        font-size: 13px;
        font-weight: 600;
        color: $primary_color;
        background: $bg_light_color;
        padding: 4px 8px;
        border-radius: $radius;
        border: 1px solid $bordercolor;
      }
    }

    .flightMeta {
      display: flex;
      gap: 8px;
      font-size: 12px;
      color: $blackgrey;

      .duration {
        font-weight: 600;
        background: $bg_light;
        padding: 2px 6px;
        border-radius: $radius;
      }

      .cabin {
        font-weight: 500;
      }
    }
  }

  .flightRoute {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 12px;
    align-items: center;

    @media screen and (max-width: $isMobile) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .departure,
  .arrival {
    text-align: center;

    .airportCode {
      font-size: 18px;
      font-weight: 600;
      color: $primary_color;
      margin-bottom: 4px;
    }

    .airportName {
      font-size: 11px;
      color: $blackgrey;
      margin-bottom: 6px;
      font-weight: 500;
      line-height: 1.2;
    }

    .dateTime {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .time {
        font-size: 15px;
        font-weight: 600;
        color: $text_color;
      }

      .date {
        font-size: 11px;
        color: $blackgrey;
        font-weight: 500;
      }
    }

    .terminal {
      font-size: 10px;
      color: $grey;
      margin-top: 3px;
      background: $bg_light;
      padding: 1px 4px;
      border-radius: 3px;
      display: inline-block;
    }
  }

  .flightPath {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 0 8px;

    .airlineInfo {
      display: flex;
      align-items: center;
      gap: 6px;
      background: $white;
      padding: 6px 8px;
      border-radius: $radius;
      border: 1px solid $bordercolor;

      .airlineLogo {
        width: 18px;
        height: 18px;
        border-radius: 3px;
        object-fit: contain;
      }

      .flightDetails {
        display: flex;
        flex-direction: column;
        gap: 1px;

        .airline {
          font-size: 13px;
          font-weight: 600;
          color: $primary_color;
        }

        .aircraft {
          font-size: 11px;
          color: $blackgrey;
        }
      }
    }

    .routeLine {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .routeDots {
        width: 60px;
        height: 2px;
        background: linear-gradient(to right, $primary_color 0%, $primary_color 25%, transparent 25%, transparent 75%, $primary_color 75%, $primary_color 100%);
        background-size: 8px 2px;
        background-repeat: repeat-x;
      }

      .refundable {
        font-size: 10px;
        color: $grey;
        text-align: center;
      }
    }
  }
}

// Passenger Section Styles - compact design
.passengersSection {
  .passengersGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;

    @media screen and (max-width: $isMobile) {
      grid-template-columns: 1fr;
    }
  }

  .passengerCard {
    background: $white;
    border-radius: $radius;
    padding: 12px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border: 1px solid $bordercolor;
    transition: all 0.1s ease-in;

    &:hover {
      box-shadow: 0 2px 3px rgba(142, 141, 141, 0.4);
    }
  }

  .passengerHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid $bordercolor;

    h3 {
      font-size: 15px;
      font-weight: 600;
      color: $text_color;
      margin: 0;
    }

    .passengerType {
      background: $primary_color;
      color: $white;
      padding: 3px 6px;
      border-radius: $radius;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }
  }

  .passengerDetails {
    .detailGrid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      @media screen and (max-width: $isMobile) {
        grid-template-columns: 1fr;
        gap: 6px;
      }
    }

    .detailItem {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .label {
        font-size: 11px;
        color: $blackgrey;
        font-weight: 500;
        text-transform: uppercase;
      }

      .value {
        font-size: 13px;
        color: $text_color;
        font-weight: 600;
      }
    }
  }
}

// Bottom Section - side by side layout
.bottomSection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media screen and (max-width: $isMobile) {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

// Contact Section Styles - compact design
.contactSection {
  .contactCard {
    background: $white;
    border-radius: $radius;
    padding: 15px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border: 1px solid $bordercolor;
  }

  .contactCompact {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .contactRow {
    display: flex;
    gap: 8px;
    align-items: flex-start;

    .label {
      font-size: 12px;
      color: $blackgrey;
      font-weight: 600;
      min-width: 60px;
      flex-shrink: 0;
    }

    .value {
      font-size: 12px;
      color: $text_color;
      font-weight: 500;
      line-height: 1.3;
    }
  }
}

// Summary Section Styles - compact design
.summarySection {
  .summaryCard {
    background: $white;
    border-radius: $radius;
    padding: 15px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border: 1px solid $bordercolor;
  }

  .summaryCompact {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
  }

  .summaryRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid $bordercolor;

    &.total {
      border-bottom: none;
      border-top: 2px solid $primary_color;
      padding-top: 12px;
      margin-top: 8px;
      background: $bg_light_color;
      padding: 12px;
      border-radius: $radius;

      .label {
        font-size: 15px;
        font-weight: 600;
        color: $primary_color;
      }

      .value {
        font-size: 16px;
        font-weight: 600;
        color: $primary_color;
      }
    }

    .label {
      font-size: 13px;
      color: $blackgrey;
      font-weight: 500;
    }

    .value {
      font-size: 13px;
      font-weight: 600;
      color: $text_color;
    }
  }

  .bookingMeta {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding-top: 12px;
    border-top: 1px solid $bordercolor;
  }

  .metaRow {
    display: flex;
    gap: 8px;
    align-items: center;

    .label {
      font-size: 11px;
      color: $blackgrey;
      font-weight: 600;
      min-width: 80px;
      flex-shrink: 0;
    }

    .value {
      font-size: 12px;
      font-weight: 600;
      color: $text_color;
      font-family: 'Courier New', monospace;
    }
  }
}

// Common Detail Row Styles - matching app's form style
.detailRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid $bordercolor;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .label {
    font-size: 14px;
    color: $blackgrey;
    font-weight: 500;
    min-width: 100px;
  }

  .value {
    font-size: 14px;
    color: $text_color;
    font-weight: 600;
    text-align: right;
    flex: 1;
  }
}

// Enhanced Print Styles
@media print {
  .itineraryDetails {
    max-width: none;
    padding: 0;
    gap: 15px;
    font-size: 12px;
  }

  .headerCard,
  .tripCard,
  .passengerCard,
  .contactCard,
  .summaryCard {
    box-shadow: none;
    border: 1px solid #ddd;
    page-break-inside: avoid;
    padding: 10px;
  }

  .sectionTitle {
    color: #000;
    border-bottom-color: #000;
    font-size: 16px;
    margin-bottom: 10px;
    padding-bottom: 5px;
  }

  .titleRow h1 {
    font-size: 20px;
  }

  .statusBadge,
  .paymentBadge {
    border: 1px solid #000;
    color: #000 !important;
    background: transparent !important;
  }

  .quickStats {
    page-break-inside: avoid;
    grid-template-columns: repeat(3, 1fr);
  }

  .journeySection {
    page-break-inside: avoid;
  }

  .passengersSection {
    page-break-before: auto;

    .passengersGrid {
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
  }

  .bottomSection {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    page-break-inside: avoid;
  }

  .flightRoute {
    grid-template-columns: 1fr auto 1fr;
  }

  .routeDots {
    background: #000 !important;
  }

  .airlineLogo {
    display: none;
  }

  // Ensure all text is black for printing
  .itineraryDetails * {
    color: #000 !important;
  }

  .primary_color,
  .text_color,
  .blackgrey {
    color: #000 !important;
  }
}
