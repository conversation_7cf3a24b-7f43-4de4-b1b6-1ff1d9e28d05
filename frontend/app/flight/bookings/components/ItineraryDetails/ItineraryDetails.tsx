import React from 'react'
import { DetailedBookingResponse } from 'models/flight-booking-models'
import styles from './ItineraryDetails.module.scss'

interface ItineraryDetailsProps {
  booking: DetailedBookingResponse
}

const ItineraryDetails: React.FC<ItineraryDetailsProps> = ({ booking }) => {
  const { MasterBooking, FlightBooking } = booking

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return styles.confirmed
      case 'pending':
        return styles.pending
      case 'cancelled':
        return styles.cancelled
      case 'completed':
        return styles.completed
      default:
        return styles.pending
    }
  }

  const totalPassengers = FlightBooking.ADT + FlightBooking.CHD + FlightBooking.INF

  return (
    <div className={styles.itineraryDetails}>
      {/* Header Card with Key Information */}
      <div className={styles.headerCard}>
        <div className={styles.headerContent}>
          <div className={styles.bookingInfo}>
            <h1>Flight Itinerary</h1>
            <div className={styles.bookingRef}>
              <span className={styles.refLabel}>Booking Reference</span>
              <span className={styles.refValue}>{MasterBooking.booking_reference}</span>
            </div>
          </div>
          <div className={styles.statusSection}>
            <div className={`${styles.statusBadge} ${getStatusClass(MasterBooking.status)}`}>
              {MasterBooking.status.toUpperCase()}
            </div>
            <div className={`${styles.paymentBadge} ${getStatusClass(MasterBooking.payment_status)}`}>
              {MasterBooking.payment_status.toUpperCase()}
            </div>
          </div>
        </div>
        <div className={styles.quickStats}>
          <div className={styles.stat}>
            <span className={styles.statValue}>{totalPassengers}</span>
            <span className={styles.statLabel}>Passenger{totalPassengers > 1 ? 's' : ''}</span>
          </div>
          <div className={styles.stat}>
            <span className={styles.statValue}>₹{FlightBooking.GrossAmount?.toLocaleString()}</span>
            <span className={styles.statLabel}>Total Amount</span>
          </div>
          <div className={styles.stat}>
            <span className={styles.statValue}>{formatDate(MasterBooking.created_at)}</span>
            <span className={styles.statLabel}>Booked On</span>
          </div>
        </div>
      </div>

      {/* Flight Journey Cards */}
      <div className={styles.journeySection}>
        <h2 className={styles.sectionTitle}>Flight Journey</h2>
        {FlightBooking.Trips?.map((trip, tripIndex) => (
          <div key={trip.id} className={styles.tripCard}>
            <div className={styles.tripHeader}>
              <h3>Journey {tripIndex + 1}</h3>
              <div className={styles.tripMeta}>
                <span className={styles.stops}>{trip.Stops} Stop{parseInt(trip.Stops) !== 1 ? 's' : ''}</span>
                <span className={styles.provider}>via {trip.Provider}</span>
              </div>
            </div>

            {trip.Segments?.map((segment, segmentIndex) => (
              <div key={segment.id} className={styles.segmentCard}>
                <div className={styles.segmentHeader}>
                  <span className={styles.segmentNumber}>Segment {segmentIndex + 1}</span>
                  <span className={styles.flightNumber}>
                    {segment.Flight.Airline.split('|')[0]} {segment.Flight.FlightNo}
                  </span>
                </div>

                <div className={styles.flightRoute}>
                  <div className={styles.departure}>
                    <div className={styles.airportCode}>{segment.Flight.DepartureCode}</div>
                    <div className={styles.airportName}>
                      {segment.Flight.DepAirportName.split('|')[1] || segment.Flight.DepAirportName}
                    </div>
                    <div className={styles.dateTime}>
                      <span className={styles.time}>{formatTime(segment.Flight.DepartureTime)}</span>
                      <span className={styles.date}>{formatDate(segment.Flight.DepartureTime)}</span>
                    </div>
                    {segment.Flight.DepartureTerminal && (
                      <div className={styles.terminal}>Terminal {segment.Flight.DepartureTerminal}</div>
                    )}
                  </div>

                  <div className={styles.flightPath}>
                    <div className={styles.airlineInfo}>
                      <img
                        src={`/assets/images/AirlineLogo/${segment.Flight.MAC}.png`}
                        alt={segment.Flight.Airline}
                        className={styles.airlineLogo}
                        onError={(e) => {
                          (e.target as HTMLImageElement).style.display = 'none'
                        }}
                      />
                      <div className={styles.flightDetails}>
                        <span className={styles.airline}>{segment.Flight.Airline.split('|')[0]}</span>
                        <span className={styles.aircraft}>{segment.Flight.EquipmentType || 'Aircraft'}</span>
                      </div>
                    </div>
                    <div className={styles.flightMeta}>
                      <span className={styles.duration}>{segment.Flight.Duration}</span>
                      <span className={styles.cabin}>{segment.Flight.Cabin}</span>
                      <span className={styles.refundable}>
                        {segment.Flight.Refundable === 'Y' ? 'Refundable' : 'Non-Refundable'}
                      </span>
                    </div>
                  </div>

                  <div className={styles.arrival}>
                    <div className={styles.airportCode}>{segment.Flight.ArrivalCode}</div>
                    <div className={styles.airportName}>
                      {segment.Flight.ArrAirportName.split('|')[1] || segment.Flight.ArrAirportName}
                    </div>
                    <div className={styles.dateTime}>
                      <span className={styles.time}>{formatTime(segment.Flight.ArrivalTime)}</span>
                      <span className={styles.date}>{formatDate(segment.Flight.ArrivalTime)}</span>
                    </div>
                    {segment.Flight.ArrivalTerminal && (
                      <div className={styles.terminal}>Terminal {segment.Flight.ArrivalTerminal}</div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* Passenger Information */}
      <div className={styles.passengersSection}>
        <h2 className={styles.sectionTitle}>Passenger Information</h2>
        <div className={styles.passengersGrid}>
          {FlightBooking.Travellers?.map((traveller, index) => (
            <div key={traveller.id} className={styles.passengerCard}>
              <div className={styles.passengerHeader}>
                <h3>{traveller.Title} {traveller.FName} {traveller.LName}</h3>
                <span className={styles.passengerType}>{traveller.PTC.toUpperCase()}</span>
              </div>
              <div className={styles.passengerDetails}>
                <div className={styles.detailRow}>
                  <span className={styles.label}>Age</span>
                  <span className={styles.value}>{traveller.Age} years</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.label}>Gender</span>
                  <span className={styles.value}>{traveller.Gender === 'M' ? 'Male' : 'Female'}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.label}>Date of Birth</span>
                  <span className={styles.value}>{formatDate(traveller.DOB)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.label}>Nationality</span>
                  <span className={styles.value}>{traveller.Nationality}</span>
                </div>
                {traveller.PassportNo && (
                  <>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Passport Number</span>
                      <span className={styles.value}>{traveller.PassportNo}</span>
                    </div>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Passport Expiry</span>
                      <span className={styles.value}>{formatDate(traveller.PDOE)}</span>
                    </div>
                    <div className={styles.detailRow}>
                      <span className={styles.label}>Place of Issue</span>
                      <span className={styles.value}>{traveller.PLI}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Contact & Billing Information */}
      <div className={styles.contactSection}>
        <h2 className={styles.sectionTitle}>Contact & Billing Information</h2>
        <div className={styles.contactCard}>
          <div className={styles.contactGrid}>
            <div className={styles.contactGroup}>
              <h4>Primary Contact</h4>
              <div className={styles.detailRow}>
                <span className={styles.label}>Name</span>
                <span className={styles.value}>
                  {FlightBooking.ContactInfo.Title} {FlightBooking.ContactInfo.FName} {FlightBooking.ContactInfo.LName}
                </span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.label}>Email</span>
                <span className={styles.value}>{FlightBooking.ContactInfo.Email}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.label}>Mobile</span>
                <span className={styles.value}>
                  {FlightBooking.ContactInfo.CountryCode} {FlightBooking.ContactInfo.Mobile}
                </span>
              </div>
              {FlightBooking.ContactInfo.Phone && (
                <div className={styles.detailRow}>
                  <span className={styles.label}>Phone</span>
                  <span className={styles.value}>{FlightBooking.ContactInfo.Phone}</span>
                </div>
              )}
            </div>

            <div className={styles.contactGroup}>
              <h4>Address</h4>
              <div className={styles.detailRow}>
                <span className={styles.label}>Address</span>
                <span className={styles.value}>{FlightBooking.ContactInfo.Address}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.label}>City</span>
                <span className={styles.value}>{FlightBooking.ContactInfo.City}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.label}>State</span>
                <span className={styles.value}>{FlightBooking.ContactInfo.State}</span>
              </div>
              {FlightBooking.ContactInfo.PIN && (
                <div className={styles.detailRow}>
                  <span className={styles.label}>PIN Code</span>
                  <span className={styles.value}>{FlightBooking.ContactInfo.PIN}</span>
                </div>
              )}
            </div>

            {(FlightBooking.ContactInfo.GSTTIN || FlightBooking.ContactInfo.GSTCompanyName) && (
              <div className={styles.contactGroup}>
                <h4>GST Information</h4>
                {FlightBooking.ContactInfo.GSTCompanyName && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>Company Name</span>
                    <span className={styles.value}>{FlightBooking.ContactInfo.GSTCompanyName}</span>
                  </div>
                )}
                {FlightBooking.ContactInfo.GSTTIN && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>GST Number</span>
                    <span className={styles.value}>{FlightBooking.ContactInfo.GSTTIN}</span>
                  </div>
                )}
                {FlightBooking.ContactInfo.GSTAddress && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>GST Address</span>
                    <span className={styles.value}>{FlightBooking.ContactInfo.GSTAddress}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Booking Summary */}
      <div className={styles.summarySection}>
        <h2 className={styles.sectionTitle}>Booking Summary</h2>
        <div className={styles.summaryCard}>
          <div className={styles.summaryGrid}>
            <div className={styles.summaryRow}>
              <span className={styles.label}>Base Fare</span>
              <span className={styles.value}>₹{FlightBooking.NetAmount?.toLocaleString()}</span>
            </div>
            <div className={styles.summaryRow}>
              <span className={styles.label}>Airline Net Fare</span>
              <span className={styles.value}>₹{FlightBooking.AirlineNetFare?.toLocaleString()}</span>
            </div>
            {FlightBooking.SSRAmount > 0 && (
              <div className={styles.summaryRow}>
                <span className={styles.label}>Additional Services</span>
                <span className={styles.value}>₹{FlightBooking.SSRAmount?.toLocaleString()}</span>
              </div>
            )}
            <div className={`${styles.summaryRow} ${styles.total}`}>
              <span className={styles.label}>Total Amount</span>
              <span className={styles.value}>₹{FlightBooking.GrossAmount?.toLocaleString()}</span>
            </div>
          </div>

          <div className={styles.bookingMeta}>
            <div className={styles.metaItem}>
              <span className={styles.label}>Transaction ID</span>
              <span className={styles.value}>{FlightBooking.TransactionID}</span>
            </div>
            <div className={styles.metaItem}>
              <span className={styles.label}>Provider</span>
              <span className={styles.value}>{FlightBooking.provider_info.code}</span>
            </div>
            <div className={styles.metaItem}>
              <span className={styles.label}>Booking Date</span>
              <span className={styles.value}>{formatDate(MasterBooking.created_at)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ItineraryDetails
