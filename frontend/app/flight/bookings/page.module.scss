@use 'sass:color';
@use '../../../styles/variable.scss' as *;

.bookingsPage {
  padding: 40px 20px;
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5f4 50%, #ffffff 100%);

  @media screen and (max-width: 768px) {
    padding: 20px 10px;
  }
}

.pageHeader {
  text-align: center;
  margin-bottom: 50px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, $primary_color, color.scale($primary_color, $lightness: 20%));
    border-radius: 2px;
  }

  h1 {
    font-size: 2.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, $primary_color, color.scale($primary_color, $lightness: -10%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
    letter-spacing: -0.02em;

    @media screen and (max-width: 768px) {
      font-size: 2.2rem;
    }
  }

  p {
    font-size: 1.2rem;
    color: #5a6c7d;
    margin: 0;
    font-weight: 400;
    opacity: 0.9;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  gap: 30px;

  .loader {
    font-size: 1.2rem;
    color: $primary_color;
    font-weight: 500;
  }
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  padding: 20px;
}

.errorMessage {
  text-align: center;
  padding: 50px 40px;
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  max-width: 550px;
  border: 1px solid rgba(220, 53, 69, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #e74c3c);
  }

  h3 {
    color: #dc3545;
    margin-bottom: 18px;
    font-size: 1.6rem;
    font-weight: 700;
  }

  p {
    color: #6c757d;
    margin-bottom: 30px;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  padding: 20px;
}

.emptyMessage {
  text-align: center;
  padding: 60px 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fffe 100%);
  border-radius: 24px;
  box-shadow: 0 15px 50px rgba(8, 119, 103, 0.08), 0 5px 15px rgba(8, 119, 103, 0.04);
  max-width: 600px;
  border: 1px solid rgba(8, 119, 103, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, $primary_color, color.scale($primary_color, $lightness: 15%));
  }

  &::after {
    content: '✈️';
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2rem;
    opacity: 0.1;
  }

  h3 {
    color: $primary_color;
    margin-bottom: 18px;
    font-size: 1.8rem;
    font-weight: 700;
  }

  p {
    color: #5a6c7d;
    margin-bottom: 35px;
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

.bookingsList {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, $primary_color, color.scale($primary_color, $lightness: 20%));
  }

  @media screen and (max-width: 768px) {
    margin: 0 10px;
    border-radius: 8px;
  }
}

.listHeader {
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9f8 100%);
  border-bottom: 2px solid rgba(8, 119, 103, 0.1);
  padding: 20px 24px;
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 140px;
  gap: 20px;
  align-items: center;
  font-weight: 600;
  font-size: 0.9rem;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr 120px;
    gap: 16px;

    .hideOnTablet {
      display: none;
    }
  }

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.bookingItem {
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: white;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, $primary_color, color.scale($primary_color, $lightness: 10%));
    transition: width 0.3s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8fffe 100%);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(8, 119, 103, 0.1);

    &::before {
      width: 4px;
    }
  }

  &:last-child {
    border-bottom: none;
  }
}

.bookingRow {
  padding: 20px 24px;
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 140px;
  gap: 20px;
  align-items: center;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr 120px;
    gap: 16px;
  }

  @media screen and (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
}

// List item styles
.flightInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;

  @media screen and (max-width: 768px) {
    order: 1;
  }
}

.routeDisplay {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #1f2937;

  .airportCode {
    font-size: 1.1rem;
    font-weight: 700;
    color: $primary_color;
  }

  .arrow {
    color: #9ca3af;
    font-size: 0.9rem;
  }
}

.flightDetails {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #6b7280;

  .airlineLogo {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    object-fit: contain;
  }
}

.bookingInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;

  @media screen and (max-width: 768px) {
    order: 2;
  }

  .bookingRef {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.95rem;
  }

  .bookingDate {
    font-size: 0.8rem;
    color: #6b7280;
  }
}

.passengerCount {
  font-weight: 600;
  color: #374151;
  text-align: center;

  @media screen and (max-width: 1024px) {
    display: none;
  }

  @media screen and (max-width: 768px) {
    display: block;
    order: 3;
    text-align: left;
    font-size: 0.9rem;
  }
}

.totalAmount {
  font-weight: 700;
  color: $primary_color;
  font-size: 1.1rem;
  text-align: center;

  @media screen and (max-width: 768px) {
    order: 4;
    text-align: left;
    font-size: 1rem;
  }
}

.statusColumn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;

  @media screen and (max-width: 768px) {
    order: 5;
    align-items: flex-start;
  }
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 80px;

  @media screen and (max-width: 768px) {
    min-width: auto;
  }

  &.confirmed {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  &.pending {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
  }

  &.cancelled {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  &.completed {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
  }
}

.viewButton {
  background: linear-gradient(135deg, $primary_color, color.scale($primary_color, $lightness: -10%));
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  box-shadow: 0 2px 4px rgba(8, 119, 103, 0.2);

  &:hover {
    background: linear-gradient(135deg, color.scale($primary_color, $lightness: -5%), color.scale($primary_color, $lightness: -15%));
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(8, 119, 103, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  @media screen and (max-width: 768px) {
    display: none;
  }
}

.mobileCard {
  display: none;

  @media screen and (max-width: 768px) {
    display: block;
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
    background: white;
    transition: all 0.2s ease;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 0;
      background: linear-gradient(135deg, $primary_color, color.scale($primary_color, $lightness: 10%));
      transition: width 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #fafbfc 0%, #f8fffe 100%);
      transform: translateX(2px);

      &::before {
        width: 3px;
      }
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

.mobileHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.mobileContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mobileRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9fafb;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .label {
    font-size: 0.85rem;
    color: #6b7280;
    font-weight: 500;
  }

  .value {
    font-size: 0.9rem;
    color: #1f2937;
    font-weight: 600;
  }
}

// Modern skeleton loading animation
.bookingsSkeleton {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: hidden;

  @media screen and (max-width: 768px) {
    margin: 0 10px;
    border-radius: 8px;
  }
}

.skeletonHeader {
  background: linear-gradient(135deg, #f8fffe 0%, #f0f9f8 100%);
  border-bottom: 2px solid rgba(8, 119, 103, 0.1);
  padding: 20px 24px;
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 140px;
  gap: 20px;
  align-items: center;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr 120px;
    gap: 16px;
  }

  @media screen and (max-width: 768px) {
    display: none;
  }

  .skeletonHeaderItem {
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;

    &.wide {
      width: 80%;
    }

    &.medium {
      width: 60%;
    }

    &.narrow {
      width: 40%;
    }
  }
}

.skeletonRow {
  padding: 20px 24px;
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1fr 140px;
  gap: 20px;
  align-items: center;
  border-bottom: 1px solid #f3f4f6;

  @media screen and (max-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr 120px;
    gap: 16px;
  }

  @media screen and (max-width: 768px) {
    display: none;
  }

  .skeletonItem {
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;

    &.route {
      height: 20px;
      width: 70%;
    }

    &.booking {
      height: 18px;
      width: 80%;
    }

    &.passengers {
      height: 16px;
      width: 30px;
      margin: 0 auto;
    }

    &.amount {
      height: 18px;
      width: 60px;
      margin: 0 auto;
    }

    &.status {
      height: 24px;
      width: 80px;
      border-radius: 12px;
    }
  }
}

.skeletonMobile {
  display: none;

  @media screen and (max-width: 768px) {
    display: block;
  }

  .skeletonMobileItem {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;

    .skeletonMobileHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .skeletonTitle {
        width: 60%;
        height: 18px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 4px;
      }

      .skeletonStatus {
        width: 70px;
        height: 24px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 12px;
      }
    }

    .skeletonMobileContent {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .skeletonMobileRow {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .skeletonLabel {
          width: 80px;
          height: 14px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }

        .skeletonValue {
          width: 100px;
          height: 14px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bookingsList {
  animation: fadeInUp 0.6s ease-out;
}

.bookingItem {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i + 1}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}

.mobileCard {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.05}s;
    }
  }
}
