"use client"

import React, { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import FlightApiService from 'services/flight-api-service'
import { DetailedBookingResponse } from 'models/flight-booking-models'
import styles from './page.module.scss'
import ItineraryDetails from '../../components/ItineraryDetails/ItineraryDetailsNew'

function ItineraryPage() {
  const router = useRouter()
  const params = useParams()
  const bookingReference = params.id as string

  const [booking, setBooking] = useState<DetailedBookingResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (bookingReference) {
      fetchBookingDetails()
    }
  }, [bookingReference])

  const fetchBookingDetails = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Fetching booking details for reference:', bookingReference)

      const bookingData = await FlightApiService.getBookingDetails(bookingReference)
      console.log('Booking data:', bookingData)

      setBooking(bookingData)
    } catch (err) {
      console.error('Error fetching booking details:', err)
      setError('Failed to load booking details. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToBookings = () => {
    router.push('/flight/bookings')
  }

  const handleRetry = () => {
    fetchBookingDetails()
  }

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loader}>Loading itinerary details...</div>
      </div>
    )
  }

  if (error || !booking) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.errorMessage}>
          <h3>Oops! Something went wrong</h3>
          <p>{error || 'Booking not found'}</p>
          <div className={styles.errorActions}>
            <button className="dy_primary_bttn" onClick={handleRetry}>
              Try Again
            </button>
            <button className="dy_secondary_bttn" onClick={handleBackToBookings}>
              Back to Bookings
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.itineraryPage}>
      <div className="container mx-auto">
        <div className={styles.pageHeader}>
          <button className={styles.backButton} onClick={handleBackToBookings}>
            ← Back to Bookings
          </button>
          <h1>Flight Itinerary</h1>
          <p>Booking Reference: {booking.MasterBooking.booking_reference}</p>
        </div>

        <ItineraryDetails booking={booking} />
      </div>
    </div>
  )
}

export default ItineraryPage
