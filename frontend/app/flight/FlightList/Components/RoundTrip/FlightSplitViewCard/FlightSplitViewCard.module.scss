@use '../../../../../../styles/variable.scss' as *;

.flight_split_view_card_div {
    transition: all .1s ease-in;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #ffff;
    height: 100%;
    flex-direction: column;
    display: flex;
    place-content: stretch flex-start;
    align-items: stretch;
    max-width: 100%;
    gap: 10px;
    box-sizing: border-box;
    cursor: pointer;

    &:hover {
        box-shadow: 0 3px 9px #0006;
    }

    .radio-bttn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border: 1px solid $button_color;
        background-color: $white;
        border-radius: 50%;

        .select-radio-bttn {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: $button_color;
        }
    }

    .select_div_flight {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .flight_name_no_div {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 10px;

            .flight_name_div {
                font-size: 14px;
                font-weight: 400;
                color: #000;
                line-height: 14px;
            }

            .number_div {
                font-size: 12px;
                font-weight: 700;
                color: #878686;
                line-height: 12px;
            }
        }

        .seat_refund_div {
            display: flex;
            align-items: center;
            gap: 10px;

            .seat_count {
                display: flex;
                align-items: center;
                gap: 5px;
                color: $text_color;

                .icon_seat {
                    font-size: 14px;
                    font-weight: 700;
                }

                .count_seat {
                    font-size: 11px;
                    font-weight: 700;
                }
            }

            .refund_txt {
                font-size: 15px;
                font-weight: 700;

                &.r_txt {
                    color: $success_color;
                }

                &.n_txt {
                    color: $error_color;
                }
            }
        }
    }

    .flight_details_bttn {
        display: flex;
        justify-content: flex-end;
    }

    .flight_icon_depature_duration_div {
        display: flex;
        align-items: center;
        gap: 10px;

        .img_div {
            height: 32px;
            width: 32px;

            img {
                width: 100%;
                height: 100%;
            }
        }

        .depature_duration_div {
            width: calc(100% - 42px);
            display: flex;
            align-items: center;
            justify-content: space-between;

            .dep_ret_div {
                width: 22%;
                display: flex;
                flex-direction: column;
                place-content: flex-start;
                gap: 3px;

                h2 {
                    font-size: 17px;
                    font-weight: 500;
                    color: #000;
                    margin-bottom: 0;
                    line-height: 17px;
                }

                .sub_txt {
                    font-size: 12px;
                    text-transform: capitalize;
                    color: #a2a1a1;
                    font-weight: 400;
                    line-height: 12px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                }
            }

            .dep_div {
                align-items: flex-start;
                text-align: start;
            }

            .ret_div {
                align-items: flex-end;
                text-align: end;
            }

            .duration_div {
                width: 31%;
                display: flex;
                flex-direction: column;
                text-align: center;

                h4 {
                    font-size: 13px;
                    font-weight: 500;
                    color: #000;

                    span {
                        color: #a2a1a1;
                        font-weight: 400;
                    }
                }

                .connections_text {
                    font-size: 12px;
                    font-weight: 400;
                    color: #525252;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                    width: 100%;
                }

                .flight_line_div {
                    width: 100%;
                    position: relative;
                    height: 8px;

                    &::before {
                        content: "";
                        left: 0;
                        width: calc(100% - 10px);
                        height: 0;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        border-bottom: 1px dashed #7d7b89;
                        z-index: 0;
                    }

                    &::after {
                        content: "\e90a";
                        transform: rotate(90deg);
                        position: absolute;
                        right: 3px;
                        top: -6.5px;
                        font-family: icomoon !important;
                        font-size: 11px;
                        color: #c1c1c1;
                    }

                    .connections {
                        position: absolute;
                        left: 0;
                        right: 0;
                        display: flex;
                        align-items: center;
                        place-content: center space-evenly;
                        gap: 10px;

                        .connection_dot {
                            width: 7px;
                            height: 7px;
                            border-radius: 50%;
                            background: $text_color;
                            border: 1px solid $text_color;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }

    .price_div {
        width: 22%;
        font-size: 17px;
        font-weight: 500;
        color: #000;
        text-align: end;
        display: flex;
        flex-direction: column;
    }
}

.flight_split_view_selected_card {
    border-radius: 0;
    border: none;
}