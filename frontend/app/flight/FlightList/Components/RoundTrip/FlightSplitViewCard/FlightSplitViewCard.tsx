import { Journey } from 'models/flight-list-response-model';
import './FlightSplitViewCard.module.scss'
import WheelchairPickup from '@mui/icons-material/WheelchairPickup'
import styles from './FlightSplitViewCard.module.scss'

interface FlightSplitViewCardProp {
    flight: Journey | undefined;
    currency?: string;
    selectFlight?: VoidFunction;
    isBottomCard?: boolean;
    isMobile?: boolean;
    emitFlightDetails?: VoidFunction;

}

export default function FlightSplitViewCard({ flight, currency = 'INR', isBottomCard = false, isMobile = false, selectFlight, emitFlightDetails }: FlightSplitViewCardProp) {

    function selectFlightCard() {
        if (selectFlight) {
            selectFlight()
        }
    }

    function flightDetailsShow() {
        if (emitFlightDetails) {
            emitFlightDetails();
        }
    }

    return (
        <>
            <div
                className={`${styles['flight_split_view_card_div']} ${isBottomCard ? styles['flight_split_view_selected_card'] : ''}`}
                onClick={selectFlightCard}
            >
                <div className={styles["select_div_flight"]}>
                    <div className={styles["flight_name_no_div"]}>
                        {!isBottomCard && (
                            <span className={styles["radio-bttn"]}>
                                {flight?.isSelect && <span className={styles["select-radio-bttn"]}></span>}
                            </span>
                        )}
                        <span className={styles["flight_name_div"]}>
                            {flight?.AirlineName ? flight.AirlineName.split('|')[1] : ''}
                        </span>
                        <span className={styles["number_div"]}>|</span>
                        <span className={styles["number_div"]}>{flight?.MAC} - {flight?.FlightNo}</span>
                    </div>
                    {(isBottomCard || isMobile) && (
                        <div className={styles["price_div"]}>
                            {flight?.GrossFare} {currency}
                        </div>
                    )}
                    {!isBottomCard && !isMobile && (
                        <div className={styles["seat_refund_div"]}>
                            {flight?.Seats && flight.Seats <= 9 && (
                                <span className={styles["seat_count"]} title={`Hurry! only ${flight.Seats} seat left`}>
                                    <WheelchairPickup className={styles["icon_seat"]} />
                                    <span className={styles["count_seat"]}>{flight.Seats} {isMobile ? '' : 'LEFT'}</span>
                                </span>
                            )}
                            <span
                                className={`${flight?.Refundable === 'Y' ? `${styles["refund_txt"]} ${styles["r_txt"]}` : `${styles["refund_txt"]} ${styles["n_txt"]}`}`}
                                title={flight?.Refundable === 'Y' ? 'Partially Refundable' : 'Non Refundable'}
                            >
                                {flight?.Refundable === 'Y' ? 'R' : 'N'}
                            </span>
                        </div>
                    )}
                </div>

                <div className={styles["flight_icon_depature_duration_div"]}>
                    <div className={styles["img_div"]}>
                        <img src={`/assets/images/AirlineLogo/${flight?.MAC}.png`} alt="Airline Logo" />
                    </div>
                    <div className={styles["depature_duration_div"]}>
                        <div className={`${styles["dep_ret_div"]} ${styles["dep_div"]}`}>
                            <h2>{new Date(flight?.DepartureTime || '').toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</h2>
                            <div className={styles["sub_txt"]}>{new Date(flight?.DepartureTime || '').toLocaleDateString([], { day: '2-digit', month: 'short' })}</div>
                            <div className={styles["sub_txt"]}>
                                {flight?.FromName ? flight.FromName.split('|')[1] : ''}
                            </div>
                        </div>
                        <div className={styles["duration_div"]}>
                            <h4>
                                {flight?.Duration?.slice(0, 2)} <span>{isMobile || isBottomCard ? 'H' : 'Hrs'}</span>
                                {flight?.Duration?.slice(4, 6)} <span>{isMobile || isBottomCard ? 'M' : 'Mins'}</span>
                            </h4>
                            <div className={styles["flight_line_div"]}>
                                <div className={styles["connections"]}>
                                    {flight?.Connections?.map((connection, index) => (
                                        <span key={index} className={styles["connection_dot"]} title={connection.ArrAirportName}></span>
                                    ))}
                                </div>
                            </div>
                            {flight?.ConnectionText && (
                                <div className={styles["connections_text"]} title={flight?.ConnectionText}>
                                    {flight.ConnectionText}
                                </div>
                            )}
                        </div>
                        <div className={`${styles["dep_ret_div"]} ${styles["ret_div"]}`}>
                            <h2>{new Date(flight?.ArrivalTime || '').toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</h2>
                            <div className={styles["sub_txt"]}>{new Date(flight?.ArrivalTime || '').toLocaleDateString([], { day: '2-digit', month: 'short' })}</div>
                            <div className={styles["sub_txt"]}>
                                {flight?.ToName ? flight.ToName.split('|')[1] : ''}
                            </div>
                        </div>
                        {!isBottomCard && !isMobile && (
                            <div className={styles["price_div"]}>
                                <small>{currency}</small>
                                <span>{flight?.GrossFare}</span> 
                            </div>
                        )}
                    </div>
                </div>

                {!isBottomCard && !isMobile && (
                    <div className={styles["flight_details_bttn"]}>
                        <span className="linkBtn" onClick={flightDetailsShow}>+ Flight Details</span>
                    </div>
                )}
            </div>


        </>
    )
}