import { FilterAlt, RestartAltRounded } from '@mui/icons-material';
import React, { useEffect, useRef, useState } from 'react'
import { FlightFilterHelper } from 'helpers/flight-filter-helper';
import { FlightListHelper } from 'helpers/flight-list-helper';
import { FlightDetailsEmitData } from 'models/flight-info-ssr-model';
import { FlightSectorFilter, Journey, SubFlight } from 'models/flight-list-response-model'
import { FormSearch } from 'models/flight-search-model';
import { useFlightState } from 'services/flight-state-service';
import styles from './flightOneWayList.module.scss';
import FilterFlight from '../../Filter/FilterFlight';
import { OneWayFlightListShimmer } from '../../OneWayFlightList/OneWayFlightListShimmer/OneWayFlightListShimmer';
import FlightSplitViewCard from '../FlightSplitViewCard/FlightSplitViewCard';
import OneWayFlightCard from '../../OneWayFlightList/OneWayFlightCard/OneWayFlightCard';
import BottomToTopPopup from 'components/BottomToTopPopup/BottomToTopPopup';



interface propData {
    isMobile: boolean;
    isLoading: boolean;
    isCache: boolean;
    data: Journey[];
    currency:string;
    TUI:string
    handleSelection: (journey: Journey,type:string) => void;
    flightSearchData: FormSearch;
    type:string
    showFlightDetails?: boolean;
}
const FLightOneWayList: React.FC<propData> = ({isMobile , isLoading , isCache , data  , currency = 'INR' , handleSelection , TUI , flightSearchData , type="ONWARD" , showFlightDetails = false }) => {
    const [isNoDataFound, setIsNoDataFound] = useState(false)
    const [isMobileFilter, setIsMobileFilter] = useState(false)
    const [dummyList, setDummyList] = useState<string>('')
    const [filterData, setFilterData] = useState<FlightSectorFilter[]>([])
    const [filterShimmer, setFilterShimmer] = useState<boolean>(true)
    const [listShimmer, setListShimmer] = useState<boolean>(true)
    const [nextPageIndex, setNextPageIndex] = useState<number>(0)
    const [tripList, setTripList] = useState<Journey[]>([]);
    const [paginateList, setPaginateList] = useState<Journey[][]>([]);
    const [displayList,setDisplayList] = useState<Journey[] | undefined>([])
    const datePipe = new Date();
    const filterHelper = new FlightFilterHelper(datePipe);
    const helper = new FlightListHelper()
    const loadingRef = useRef(false);
    const { flightDetailsBodyData, setFlightDetailsBodyData, isFlightDetailsPopup, setIsFlightDetailsPopup } = useFlightState()
    useEffect(()=>{
        if(isLoading){
            setListShimmer(true)
            setFilterShimmer(true)
        }
    },[isLoading])

    useEffect(()=>{
        let list:Journey[]  = []
        if (data.length > 0) {
          list = tripList.concat(helper.oneWayJournyKeySet(data))
          setTripList(list)
          setValueInList(list);
          setFilterDataFromResponse(list)
          setIsNoDataFound(false)
        }else{
            if(!isLoading){
                setIsNoDataFound(true)
            }
        }
    },[data])

  function setValueInList(TripList:Journey[]) {
    const compaignList: Journey[] = helper.combineSameFlight(TripList);
    const list: Journey[] = compaignList?.filter(x => x.isDisplay == true && x.GrossFare > 0 && x.Seats != 0) || [];
    if (list.length > 0) {
      listConvertPagination(list);
    }
  }
    function setFilterDataFromResponse(TripList:Journey[]) {
        const compaignList: Journey[] = helper.combineSameFlight(TripList);
        const list: Journey[] = compaignList?.filter(x => x.isDisplay == true && x.GrossFare > 0 && x.Seats != 0) || [];
        if (list.length > 0) {
          setDummyList(JSON.stringify(list))
          setFilterData([
            {
              From: flightSearchData?.trips[0]?.from?.iata || '',
              To: flightSearchData?.trips[0]?.to?.iata || '',
              Tui: '',
              FilterData: filterHelper.setFlightFilterDate(list)
            }
          ])
          setFilterShimmer(false)
        } else {
          setFilterShimmer(false)
          setIsNoDataFound(true)
        }
        setListShimmer(false)
      }

      const handleScroll = () => {
        if (loadingRef.current) return;
        loadingRef.current = true;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = Math.max(
          document.body.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.clientHeight,
          document.documentElement.scrollHeight,
          document.documentElement.offsetHeight
        );

        if (scrollPosition >= documentHeight - 400) {
          setNextPageIndex((prevIndex) => prevIndex + 1);
        }
        setTimeout(() => (loadingRef.current = false), 200);
      };
      useEffect(() => {
        window.addEventListener('scroll', handleScroll);
        return () => {
          window.removeEventListener('scroll', handleScroll);
        };
      }, []);

      useEffect(() => {
        if (nextPageIndex < paginateList.length && nextPageIndex > 0) {
          console.log("Loading page:", nextPageIndex, "of", paginateList.length);
          const newPageData = paginateList[nextPageIndex];
          if (newPageData) {
            setDisplayList(prevList => {
              const updatedList = prevList?.concat(newPageData);
              console.log("Updated display list length:", updatedList?.length);
              return updatedList;
            });
          }
        }
      },[nextPageIndex,paginateList]);

    function listConvertPagination(list: Journey[]){
        const itemsPerPage = 10;
        const numberOfPages = Math.ceil(list.length / itemsPerPage);
        setPaginateList([]);
        setDisplayList([]);
        setNextPageIndex(0)

        // Reset selection states for all flights
        const resetList = list.map(flight => ({
            ...flight,
            isSelect: false,
            SubFlights: flight.SubFlights?.map(sf => ({
                ...sf,
                isSelect: false
            }))
        }));

        const newPaginateList = [];
        for (let i = 0; i < numberOfPages; i++) {
            let startIndex = i * itemsPerPage;
            let endIndex = startIndex + itemsPerPage;
            newPaginateList.push(resetList.slice(startIndex, endIndex));
          }

          if (newPaginateList && newPaginateList.length > 0) {
            setListShimmer(false)
            setFilterShimmer(false)
            setDisplayList(newPaginateList[0])
            setPaginateList(newPaginateList)
          }else{
            setDisplayList([])
            setPaginateList([])
            setNextPageIndex(0)
          }
    }
    function fliterDataGet() {
        const dmmlist: Journey[] = JSON.parse(dummyList) as Journey[];
        if (filterData && filterData.length > 0) {
          const list = filterHelper.addFilterInList(filterData[0]!.FilterData, dmmlist);
          listConvertPagination(list);
        }
      }

    function handleFlightSelect(journeyItem:Journey){
        console.log("Attempting to select flight:", {
            CreatedJourneyKey: journeyItem.CreatedJourneyKey,
            Index: journeyItem.Index,
            type: type
        });

        // Find the index in displayList instead of data array
        const displayIndex = displayList?.findIndex((item) =>
            item.CreatedJourneyKey === journeyItem.CreatedJourneyKey &&
            item.Index === journeyItem.Index
        );

        if(displayIndex !== -1 && displayIndex !== undefined){
             console.log("Flight found at display index:", displayIndex);

            const newList = displayList?.map((item, i) => ({
              ...item,
              isSelect: i === displayIndex
        }));
            setDisplayList(newList)
            handleSelection(journeyItem,type)
        }else{
            console.error("Flight not found in display list. Available flights:",
                displayList?.map(item => ({
                    CreatedJourneyKey: item.CreatedJourneyKey,
                    Index: item.Index
                }))
            );
        }
    }

    function handleSubFlightSelect(parentJourney: Journey, subFlight: SubFlight) {
        console.log("Attempting to select sub-flight:", {
            parentJourneyKey: parentJourney.CreatedJourneyKey,
            subFlightIndex: subFlight.Index,
            subFlightAmount: subFlight.Amount,
            type: type
        });

        // Create a modified journey with the selected sub-flight data
        const modifiedJourney: Journey = {
            ...parentJourney,
            GrossFare: subFlight.Amount || parentJourney.GrossFare,
            Index: subFlight.Index || parentJourney.Index,
            ChannelCode: subFlight.ChannelCode || parentJourney.ChannelCode,
            FCType: subFlight.FCType || parentJourney.FCType,
            Refundable: subFlight.Refund || parentJourney.Refundable
        };

        // Find and update the display list
        const displayIndex = displayList?.findIndex((item) =>
            item.CreatedJourneyKey === parentJourney.CreatedJourneyKey
        );

        if(displayIndex !== -1 && displayIndex !== undefined){
            console.log("Parent flight found at display index:", displayIndex);

            const newList = displayList?.map((item, i) => ({
              ...item,
              isSelect: i === displayIndex,
              // Update SubFlights selection state
              SubFlights: item.CreatedJourneyKey === parentJourney.CreatedJourneyKey
                ? item.SubFlights?.map(sf => ({
                    ...sf,
                    isSelect: sf.Index === subFlight.Index
                  }))
                : item.SubFlights
            }));
            setDisplayList(newList)
            handleSelection(modifiedJourney, type)
        } else {
            console.error("Parent flight not found in display list for sub-flight selection");
        }
    }

    function handleShowFlightDetails(flight:Journey){
        const body: FlightDetailsEmitData = {
            TripType: 'ON',
            Trips: [[{
              "Amount": flight.GrossFare,
              "ChannelCode": null,
              "Index": flight.Index || '',
              "OrderID": 1,
              "TUI": TUI
            }]]
          }
          setFlightDetailsBodyData(body)
          setIsFlightDetailsPopup(true)
    }
    function showFareType(i: number) {
        if (displayList) {
          const disList = displayList?.map((item, index) => {
            if (index === i) {
              return {
                ...item,
                isShowFareType: !item.isShowFareType
              };
            }
            return item;
          });
          setDisplayList(disList);
        }
      }
    function resetFilter() {
        setIsMobileFilter(false)
        const dmmlist: Journey[] = JSON.parse(dummyList) as Journey[];
        setFilterData([
          {
            From: flightSearchData?.trips[0]?.from?.iata || '',
            To: flightSearchData?.trips[0]?.to?.iata || '',
            Tui: '',
            FilterData: filterHelper.setFlightFilterDate(dmmlist)
          }
        ])
        listConvertPagination(dmmlist);
      }

      function openFilter() {
        setIsMobileFilter(true)
      }

      function closeFilter() {
        setIsMobileFilter(false)
      }
    return (
        <>
          <div className={`${styles['one-way-flight-list-div']}`}>
              {!isNoDataFound ? (
                  <>
                      <div className={`${styles['filter-div']}`}>
                          <FilterFlight shimmer={filterShimmer} initialFilterData={filterData} currency={currency} handleFilterChange={fliterDataGet} emitFilter={fliterDataGet}  />
                      </div>
                      <div className={`${styles['list-div']}`}>
                          {listShimmer ? (
                              [...Array.from({ length: 10 }).map((_, index: number) => (
                                  <OneWayFlightListShimmer key={index} />
                              ))]
                          ):(
                              displayList &&  displayList.length > 0 ? (
                                  displayList.map((item,index)=>(
                                      <OneWayFlightCard
                                          key={index}
                                          flight={item}
                                          tui={TUI}
                                          emitFareTypeShow={() => showFareType(index)}
                                          emitNavigate={() => handleFlightSelect(item)}
                                          emitSubFlightSelect={(subFlight) => handleSubFlightSelect(item, subFlight)}
                                          currency={currency}
                                          isMobile={isMobile}
                                          showDetails={() => handleShowFlightDetails(item)}
                                          isCache = {isCache}
                                          type={type}
                                          forceShowDetails={showFlightDetails}

                                      />
                                  // <FlightSplitViewCard
                                  //         key={index}
                                  //         flight={item}
                                  //         currency={currency}
                                  //         isMobile={isMobile}
                                  //         selectFlight={() => handleFlightSelect(item.Index)}
                                  //         emitFlightDetails={() => handleShowFlightDetails(item)}
                                  //     />
                                  ))
                          ):(
                              <div className="no-data-found-div">
                                  <div className="main-txt">Sorry no results were found.</div>
                                  <div>Please try changing your filters.</div>
                                  <button className="out-line-button" onClick={resetFilter}><RestartAltRounded/> Reset</button>
                              </div>
                          )
                          )}
                      </div>
                  </>
              ):(
                  <div className="no-data-found-div">
                      <div className="main-txt">Sorry no results were found.</div>
                      <div>Please try changing your cities or dates. </div>
                  </div>
              )}

          </div>
            {isMobile && (
              <>
                  <div className="mobile-filter-div" onClick={openFilter}>
                      <FilterAlt className="maticon"/>
                  </div>

                <BottomToTopPopup isOpen={isMobileFilter} onClose={()=>setIsMobileFilter(false)} heading="" type="filter">
                    <FilterFlight handleClose={closeFilter} shimmer={filterShimmer} initialFilterData={filterData} currency={currency} handleFilterChange={fliterDataGet} emitFilter={fliterDataGet} />
                </BottomToTopPopup>
            </>
          )}
        </>
  )
}

export default FLightOneWayList