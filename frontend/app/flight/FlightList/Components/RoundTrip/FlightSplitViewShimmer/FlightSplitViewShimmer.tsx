import styles from './FlightSplitViewShimmer.module.scss'

export default function FlightSplitViewShimmer() {
    return (
        <>
            <div className={styles["shimmer_split_view_card"]}>
                <div className={styles["top_div"]}>
                    <div className={`${styles["left_div"]} ${styles["shine"]}`}></div>
                    <div className={`${styles["right_div"]} ${styles["shine"]}`}></div>
                </div>
                <div className={styles["center_div"]}>
                    <div>
                        <div className={`${styles["img_div"]} ${styles["shine"]}`}></div>
                    </div>
                    <div className={styles["other_div"]}>
                        <div className={styles["dep_div"]}>
                            <span className={`${styles["time_plce"]} ${styles["shine"]}`}></span>
                        </div>

                        <div className={styles["duration_div"]}>
                            <div className={`${styles["connections_text"]} ${styles["shine"]}`}></div>
                            <div className={styles["flight_line_div"]}></div>
                            <div className={`${styles["stop_div"]} ${styles["shine"]}`}></div>
                        </div>

                        <div className={`${styles["dep_div"]} ${styles["ret_div"]}`}>
                            <span className={`${styles["time_plce"]} ${styles["shine"]}`}></span>
                        </div>
                    </div>
                </div>
                <div className={styles["flight_details"]}>
                    <div className={`${styles["link_div"]} ${styles["shine"]}`}></div>
                </div>
            </div>


        </>
    )
}