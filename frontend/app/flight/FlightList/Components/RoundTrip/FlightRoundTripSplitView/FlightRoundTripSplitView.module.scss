@use '../../../../../../styles/variable.scss' as *;
.round_trip_list_div {
    display: flex;
    gap: 20px;
    padding-bottom: 50px;

    .filter_div {
        width: 22%;
        position: sticky;
        top: 70px;
        height: calc(100vh - 215px);
        overflow-y: hidden;
        overflow-x: hidden;
        border: 1px solid #ccc;
        background-color: white;
        border-radius: 4px;

        &:hover {
            overflow-y: auto;
        }
    }

    .list_div {
        width: calc(78% - 20px);

        .mobile_filter_div {
            display: none;
        }

        .rt_split_view_list_div {
            width: 100%;
            display: flex;
            gap: 10px;

            .card_list_div {
                width: calc(50% - 5px);
                height: 100%;
                flex-direction: column;
                box-sizing: border-box;
                display: flex;
                place-content: stretch flex-start;
                align-items: stretch;
                max-width: 100%;
                gap: 10px;
            }
        }
    }

    .no_data_found_div {
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 5px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #ffff;
        box-shadow: 0 3px 9px #0006;
        color: #363636;
        font-size: 15px;
        font-weight: 400;

        .main_txt {
            font-size: 19px;
            font-weight: 600;
            color: #000;
        }

        .linkwdth {
            width: fit-content;
        }
    }
}

@media only screen and (max-width: 768px) {
    .round_trip_list_div {
        display: block;

        .filter_div {
            display: none;
            width: 0;
        }

        .list_div {
            width: 100%;

            .mobile_filter_div {
                position: absolute;
                right: 10px;
                top: 75px;
                z-index: 1;
                display: flex;

                .mob_filter_bttn {
                    display: flex;
                    padding: 5px;
                    gap: 5px;
                    align-items: center;
                    color: #fff;
                    background-color: red;
                    font-size: 14px;
                    border: 1px solid #fff;
                    border-radius: 4px;
                    font-weight: 400;
                }
            }

            .rt_split_view_list_div {
                width: 100%;
                overflow-x: auto;
                box-sizing: border-box;
                padding: 0 10px;

                .card_list_div {
                    white-space: nowrap;
                    width: max-content;
                    min-width: 90%;
                }
            }
        }
    }
}

.rt_split_view_select_card_overlay_div {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 98;
    background-color: #00000063;
}

.rt_split_view_select_card_div {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    background-color: #fff;
    box-shadow: 0 -5px 6px 0 #0000001a;
    width: 100%;

    &.show_details_border {
        border-radius: 5px 5px 0 0;
    }

    .select_card_data {
        display: flex;
        align-items: center;

        @media only screen and (max-width: 768px) {
            display: none;
        }

        .onw_ret_card {
            width: 38%;
            border-right: 1px solid #ccc;
        }

        .total_price_bttn {
            width: 24%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .total_price_div {
                font-size: 25px;
                font-weight: 700;
                color: #000;
                padding: 0 15px;
            }

            .button_details_div {
                display: flex;
                flex-direction: column;
                place-content: center flex-start;
                align-items: center;
                gap: 5px;
            }
        }
    }

    .mobile_close_details_div {
        position: relative;
        display: none;
        justify-content: center;

        @media only screen and (max-width: 768px) {
            display: flex;
        }

        .icons {
            position: absolute;
            top: -25px;
            font-size: 18px;
            font-weight: 600;
        }
    }

    .mobile_select_card_data {
        padding: 0;
        width: 100%;
        display: none;
        flex-direction: column;
        place-content: stretch flex-start;
        align-items: stretch;
        gap: 10px;

        @media only screen and (max-width: 768px) {
            display: flex;
        }

        .price_tot_bttn_div {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 10px 10px 0 10px;

            .price_div {
                display: flex;
                align-items: center;
                gap: 20px;

                .text_div {
                    font-size: 13px;
                    font-weight: 400;
                }

                .price_div {
                    color: #000;
                    font-size: 15px;
                    font-weight: 700;
                }
            }

            .total_bttn_div {
                display: flex;
                align-items: center;
                gap: 10px;

                .total_amt {
                    font-size: 18px;
                    font-weight: 700;
                    color: #000;
                }
            }
        }

        .linkwdth {
            width: fit_content;
            padding: 0 10px 10px 10px;
        }

        .brdr_top_flight {
            border-top: 1px solid #ccc;
        }
    }
}

.mob_flight_filter_view_div {
    height: calc(100vh - 50px);
    overflow: auto;
    padding-bottom: 60px;
    position: relative;

    .button_div {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-end;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 10px;
        background-color: #fff;
        z-index: 99;
        box-shadow: 0 3px 9px #0006;

        .searchbtn {
            width: fit-content;
        }
    }
}


.filter_div::-webkit-scrollbar {
    width: 4px;
}

.filter_div::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 100vw;
}

.filter_div::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 100vw;
}

.filter_div::-webkit-scrollbar-thumb:hover {
    background-color: #9a9a9a;
}

/* Hide Scrollbar Buttons (Up/Down Arrows) */
.filter_div::-webkit-scrollbar-button {
    display: none; /* Completely remove arrows */
    width: 0;
    height: 0;
}
