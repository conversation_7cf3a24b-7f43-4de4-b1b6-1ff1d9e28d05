@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

.flight-filter-loader-div {
    .common-pdng-brdr {
        padding: 10px;
        border-bottom: 1px solid #ccc;
    }

    .common-head {
        height: 15px;
        width: 50%;
    }
    .mat-tab-groups{
        width: 95%;
        height: 40px;
        text-align: center;
        background-color: #f0f0f0;
        margin-left: auto;
        margin-right: auto;
        animation: shimmer 1.5s infinite linear;
        background: linear-gradient(
            to right,
            #e0e0e0 0%,
            #f7f7f7 50%,
            #e0e0e0 100%
        );
        background-size: 200% 100%;
    }

    .stops-div {
        display: flex;
        gap: 10px;

        .stops {
            background-color: #f0f0f0;
            width: 50px;
            border-radius: 4px;
            height: 28px;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(
                to right,
                #e0e0e0 0%,
                #f7f7f7 50%,
                #e0e0e0 100%
            );
            background-size: 200% 100%;
        }
    }

    .time-card-div {
        width: 100%;
        display: flex;
        gap: 10px;

        .time-card {
            border: 1px solid #ccc;
            width: calc(25% - 7.5px);
            padding: 7px 0;
            border-radius: 4px;
            background-color: #f0f0f0;
            min-height: 55px;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(
                to right,
                #e0e0e0 0%,
                #f7f7f7 50%,
                #e0e0e0 100%
            );
            background-size: 200% 100%;
        }
    }

    .list-check-box {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .more-bttn {
        width: 30%;
        height: 13px;
        background-color: #f0f0f0;
        animation: shimmer 1.5s infinite linear;
        background: linear-gradient(
            to right,
            #e0e0e0 0%,
            #f7f7f7 50%,
            #e0e0e0 100%
        );
        background-size: 200% 100%;
    }

    .check-box-div {
        display: flex;
        gap: 10px;
        align-items: center;
        width: 100%;

        .check {
            height: 16px;
            width: 16px;
            border-radius: 2px;
            background-color: #f0f0f0;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(
                to right,
                #e0e0e0 0%,
                #f7f7f7 50%,
                #e0e0e0 100%
            );
            background-size: 200% 100%;
        }

        .chec-txt {
            width: 70%;
            height: 15px;
            background-color: #f0f0f0;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(
                to right,
                #e0e0e0 0%,
                #f7f7f7 50%,
                #e0e0e0 100%
            );
            background-size: 200% 100%;
        }
    }

    .price-range {
        height: 13px;
        width: 100%;
        background-color: #f0f0f0;
        animation: shimmer 1.5s infinite linear;
        background: linear-gradient(
            to right,
            #e0e0e0 0%,
            #f7f7f7 50%,
            #e0e0e0 100%
        );
        background-size: 200% 100%;
    }

    .price-range-txt {
        display: flex;
        justify-content: space-between;
        width: 100%;

        span {
            width: 15%;
            height: 13px;
            background-color: #f0f0f0;
            margin-top: 10px;
            animation: shimmer 1.5s infinite linear;
            background: linear-gradient(
                to right,
                #e0e0e0 0%,
                #f7f7f7 50%,
                #e0e0e0 100%
            );
            background-size: 200% 100%;
        }
    }
}