import { useState } from "react"
import styles from './OneWayFlightListShimmer.module.scss'

export function OneWayFlightListShimmer() {
  const [isMobile, setIsMobile] = useState(false)

  return (
    <>
      <div className={styles["one-way-flight-card-shimmer-div"]}>

        <div className={styles["flight-card-shimmer-div"]}>
          <div className={styles["first-div"]}>
            <div className={styles["left-div"]}>
              <div className={`${styles["airline-name-div"]} ${styles['airline-flex-auto']}`}>
                <div className={`${styles["logo"]} ${styles['shine']}`}></div>
                <div className={styles["flight-name-no"]} >
                  <div className={`${styles["flight-name"]} ${styles['shine']}`}></div>
                  <div className={`${styles["flight-no shine"]} ${styles['shine']}`}></div>
                </div>
              </div>
              <div  className={`${styles["airline-flex-auto"]} ${styles['flight-name-no']}`}>
                <div  className={`${styles["time-div"]} ${styles['shine']}`}></div>
                <div  className={`${styles["flight-no"]} ${styles['shine']}`}></div>
                <div  className={`${styles["date-div"]} ${styles['shine']}`}></div>
              </div>
              <div  className={`${styles["duration-div"]} ${styles['airline-flex-auto']}`}>
                <div  className={`${styles["time-duration"]} ${styles['shine']}`}></div>
                <div className={styles["flight-line-div"]}></div>
              </div>
              <div  className={`${styles["airline-flex-auto"]} ${styles['return-flight']}`}>
                <div  className={`${styles["time-div"]} ${styles['shine']}`}></div>
                <div  className={`${styles["flight-no"]} ${styles['shine']}`}></div>
                <div  className={`${styles["date-div"]} ${styles['shine']}`}></div>
              </div>
            </div>
            <div className={styles["return-flight"]} >
              <div  className={`${styles["time-div"]} ${styles['shine']}`}></div>
              <div  className={`${styles["button-div"]} ${styles['shine']}`}></div>
              <div  className={`${styles["date-div"]} ${styles['shine']}`}></div>
            </div>
          </div>
        </div>


        <div className={styles["flight-card-shimmer-mob-div"]}>
          <div  className={`${styles["date-div "]} ${styles['shine']}`}></div>
          <div className={styles["first-div"]}>
            <div  className={`${styles["airline-name-div"]} ${styles['airline-flex-auto']}`}>
              <div  className={`${styles["logo"]} ${styles['shine']}`}></div>
              <div className={styles["flight-name-no"]} >
                <div  className={`${styles["time-mob-div"]} ${styles['shine']}`}></div>
                <div  className={`${styles["city-div"]} ${styles['shine']}`}></div>
              </div>
            </div>
            <div  className={`${styles["duration-div"]} ${styles['airline-flex-auto']}`}>
              <div  className={`${styles["flight-no "]} ${styles['shine']}`}></div>
              <div className={styles["flight-line-div"]}></div>
            </div>
            <div  className={`${styles["return-flight"]} ${styles['airline-flex-auto']}`}>
              <div  className={`${styles["time-mob-div"]} ${styles['shine']}`}></div>
              <div  className={`${styles["city-div"]} ${styles['shine']}`}></div>
            </div>
          </div>
          <div className={styles["bottom-price-div"]} >
            <div  className={`${styles["refund-box"]} ${styles['shine']}`}></div>
            <div  className={`${styles["time-div"]} ${styles['shine']}`}></div>
          </div>
        </div>
      </div>
    </>
  )
}
