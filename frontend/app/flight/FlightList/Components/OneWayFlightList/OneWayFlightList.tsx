import { useEffect, useState } from "react"
import { OneWayFlightListShimmer } from "./OneWayFlightListShimmer/OneWayFlightListShimmer"
import OneWayFlightCard from "./OneWayFlightCard/OneWayFlightCard"
import { FlightFilterHelper } from "helpers/flight-filter-helper";
import { FlightListHelper } from "helpers/flight-list-helper";
import { useRouter } from "next/navigation";
import { useFlightState } from "services/flight-state-service";
import FlightApiService from "services/flight-api-service";
import { FlightSectorFilter, Journey, scheduleSave } from "models/flight-list-response-model";
import { ExpressSearchBody, FormSearch, GetExpressSearchBody } from "models/flight-search-model";
import { FlightDetailsEmitData } from "models/flight-info-ssr-model";
import { scheduleBodySet } from "adapters/flight-search-adapter";
import styles from './OneWayFlightList.module.scss'
import FilterFlight from "app/flight/FlightList/Components/Filter/FilterFlight";
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import { useSyncState } from "helpers/sync-state";
import { RestartAltRounded, RestoreOutlined } from "@mui/icons-material";
import { showNetworkError } from "components/NetWorkAlert/showNetworkError";
import BottomToTopPopup from "components/BottomToTopPopup/BottomToTopPopup";
import { Loader } from "components/loader/loader";

interface FlightListProps {
  currency: string;
  isMobile: boolean;
  getFlightSearchBody: FormSearch;
}



const OneWayFlightList: React.FC<FlightListProps> = ({ getFlightSearchBody, currency = 'INR', isMobile }) => {
  const [isNoDataFound, setIsNoDataFound] = useState(false)
  const [isMobileFilter, setIsMobileFilter] = useState(false)
  const [flightSearchData, setFlightSearchData] = useState<FormSearch | null>(null)
  const [dummyList, setDummyList] = useState<string>('')
  const [filterData, setFilterData] = useState<FlightSectorFilter[]>([])
  const [filterShimmer, setFilterShimmer] = useState<boolean>(true)
  const [listShimmer, setListShimmer] = useState<boolean>(true)
  const [nextPageIndex, setNextPageIndex] = useState<number>(0)
  const [displayList, setDisplayList,displayListRef] = useSyncState<Journey[] | undefined>([]);
  const [paginateList, setPaginateList] = useState<Journey[][]>([]);
  const [tripList, setTripList] = useState<Journey[]>([]);
  const [TUI, setTUI,TUIRef] = useSyncState<string>('');
  const [isCache,setIsCache] = useState<boolean>(true)
  useEffect(() => {
    // Whenever getFlightSearchBody changes, update flightSearchData and call checkScheduledData
    if (getFlightSearchBody) {
      
      setFlightSearchData(getFlightSearchBody);
      if (flightSearchData) {
        checkScheduledData();
      }
    }
  }, [getFlightSearchBody,flightSearchData]);

  useEffect(() => {
   
  }, [flightSearchData]);
  
  const router = useRouter();

  const datePipe = new Date(); // In React, use the JavaScript Date object or a library like `date-fns`
  const helper = new FlightListHelper();

  const filterHelper = new FlightFilterHelper(datePipe);
  const api = FlightApiService;
  const { flightSchedule, setFlightDetailsBodyData,setIsFlightDetailsPopup } = useFlightState();

  function checkScheduledData() {
    setTUI('')
    setTripList([]);
    setPaginateList([])
    setDisplayList([])
    setNextPageIndex(0)
    setListShimmer(true)
    setFilterShimmer(true)
    setFilterData([])
    setDummyList('')
    setIsNoDataFound(false)

    const scheduleData: scheduleSave[] = flightSchedule;
    if (scheduleData && scheduleData.length > 0) {
      const searchSector = flightSearchData?.trips.map((trip: any) => trip.from.iata + '-' + trip.to.iata).join('-');
      if (searchSector == scheduleData[0]?.Search) {
        apiCalls();
      }
    } else {
      apiCalls();
    }
  }

  async function apiCalls() {
    const storageTuiID = sessionStorage.getItem('dySearchTuiID')
    if(storageTuiID){
      setIsCache(false);
      setTUI(storageTuiID)
      callSearchList(storageTuiID , 0)
    }else{
      callSearchApi()
      
    }
  }

  // search api
  async function callSearchApi() {
    const body = scheduleBodySet(flightSearchData, "ON");
    try{
      const data = await api.callExpressSearch(body)
      if(data.Code === 200){
        if(data.sh_price){
          setIsCache(false);
        }else{
          setIsCache(true)
        }
        setTUI(data.TUI)
        setIsNoDataFound(false)
        sessionStorage.setItem('dySearchTuiID', TUI)
        callSearchList(data.TUI , 0)
        if(data.Trips && data.Trips[0] && data.Trips[0].Journey){
          const list = tripList.concat(helper.oneWayJournyKeySet(data.Trips[0].Journey))
          setTripList(list)
          setValueInList(list)
          setFilterDataFromResponse(list)
        }
      } else {
        if (data.Completed) {
          setListShimmer(false)
          setFilterShimmer(false)
          setIsNoDataFound(false)
        }
      }
    }catch (error){
      setIsNoDataFound(true)
      setListShimmer(false)
      setFilterShimmer(false)
    }
  }
  // async function callExpressSearchApis() {
  //   if (sessionStorage && sessionStorage.getItem('dySearchTuiID')) {
  //     setTUI(sessionStorage.getItem('dySearchTuiID') || '');
  //     const body: ExpressSearchBody = {
  //       "ClientID": "",
  //       "TUI": sessionStorage.getItem('dySearchTuiID'),
  //       "Source": "ST",
  //       "Mode": "SY",
  //       "FareType": flightSearchData?.FareType
  //     }
  //     try {
  //       const data = await api.callExpressSearch(body)
  //       if (data.Code == 200) {
  //         if (data.Trips && data.Trips[0] && data.Trips[0].Journey) {
  //           const list = tripList.concat(helper.oneWayJournyKeySet(data.Trips[0].Journey))
  //           setTripList(list)
  //           setValueInList(list);
  //           setFilterDataFromResponse(list);
  //         } else {
  //           if (data.Completed == 'True') {
  //             setListShimmer(false)
  //             setFilterShimmer(false)
  //             setIsNoDataFound(false)
  //           }
  //         }
  //       } else {
  //         sessionStorage.removeItem('dySearchTuiID');
  //         callExpressSearchApi();
  //       }

  //     } catch (error) {
  //       setListShimmer(false)
  //       setFilterShimmer(false)
  //       setIsNoDataFound(true)
  //     }


  //   } else {
  //     const body = scheduleBodySet(flightSearchData, "ON");
  //     try {
  //       const data = await api.callExpressSearch(body)
  //       if (data.Code == 200) {
  //         setTUI(data.TUI)
  //         setIsNoDataFound(false)
  //         sessionStorage.setItem('dySearchTuiID', TUI)
  //         callGetExpressSearch(data.TUI)
  //       }
  //     } catch (error) {
  //       setIsNoDataFound(true)
  //       setListShimmer(false)
  //       setFilterShimmer(false)
  //     }
  //   }
  // }

  async function callSearchList(TUI: string , callCount = 0) {
    const body: GetExpressSearchBody = {
      ClientID: '',
      TUI: TUI
    }
    try {
      const data = await api.getExpressSearch(body);
      if (data.Code == 200) {
        console.log("search list",data);
        
        let list:Journey[]  = []
        if (data.Trips && data.Trips[0] && data.Trips[0].Journey) {
          list = tripList.concat(helper.oneWayJournyKeySet(data.Trips[0].Journey))
          setTripList(list)
          setValueInList(list);
          setFilterDataFromResponse(list)
        }
      }
      if (!data.Completed) {
        if(callCount < 9){
          setTimeout(() => {
             callSearchList(TUI, callCount + 1)         
          }, 1000);
        }else{
          showNetworkError(() => router.push('/flight/FlightHomePage'))
        }
      } else {
        setIsCache(false)
      }
    } catch (error) {
      setListShimmer(false)
      setFilterShimmer(false)
      setIsNoDataFound(true)
    }
  }

  function setValueInList(TripList:Journey[]) {
    const compaignList: Journey[] = helper.combineSameFlight(TripList);
    const list: Journey[] = compaignList?.filter(x => x.isDisplay == true && x.GrossFare > 0 && x.Seats != 0) || [];
    if (list.length > 0) {
      listConvertPagination(list);
    }
  }

  function setFilterDataFromResponse(TripList:Journey[]) {
    const compaignList: Journey[] = helper.combineSameFlight(TripList);
    const list: Journey[] = compaignList?.filter(x => x.isDisplay == true && x.GrossFare > 0 && x.Seats != 0) || [];
    if (list.length > 0) {
      setDummyList(JSON.stringify(list))
      setFilterData([
        {
          From: flightSearchData?.trips[0]?.from?.iata || '',
          To: flightSearchData?.trips[0]?.to?.iata || '',
          Tui: '',
          FilterData: filterHelper.setFlightFilterDate(list)
        }
      ])
      setFilterShimmer(false)
    } else {
      setFilterShimmer(false)
      setIsNoDataFound(true)
    }
    setListShimmer(false)
  }

  function listConvertPagination(list: Journey[]) {
    const itemsPerPage = 10;
    const numberOfPages = Math.ceil(list.length / itemsPerPage);
    setPaginateList([])
    setDisplayList([])
    setNextPageIndex(0)
    const newPaginateList = [];

    for (let i = 0; i < numberOfPages; i++) {
      let startIndex = i * itemsPerPage;
      let endIndex = startIndex + itemsPerPage;
      newPaginateList.push(list.slice(startIndex, endIndex));
    }

    if (newPaginateList.length > 0) {
      setListShimmer(false)
      setDisplayList(newPaginateList[0])
      setPaginateList(newPaginateList)
    }else{
      setDisplayList([])
      setPaginateList([])
      setNextPageIndex(0)
    }
  }

// for setting up timer  
let isLoading = false;
const handleScroll = () => {
  if (isLoading) return;
  isLoading = true;

  const scrollPosition = window.innerHeight + window.scrollY;
  const documentHeight = Math.max(
    document.body.scrollHeight,
    document.body.offsetHeight,
    document.documentElement.clientHeight,
    document.documentElement.scrollHeight,
    document.documentElement.offsetHeight
  );

  if (scrollPosition >= documentHeight - 250) {
    setNextPageIndex((prevIndex) => prevIndex + 1);
  }

  setTimeout(() => (isLoading = false), 200);
};


  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // function loadNextPage() {
  //   setNextPageIndex((prevIndex) => prevIndex + 1);
  //   setNextPageIndex((prevIndex) => {
  //     const newIndex = prevIndex + 1;
  //     if (newIndex < paginateList.length) {
  //       setDisplayList(prevList => prevList.concat(paginateList[newIndex]!));;
  //     }
  //     return newIndex;
  //   });
  // }

  useEffect(() => {
    if (nextPageIndex < paginateList.length) {
      const display = displayList?.concat(paginateList[nextPageIndex]!);
      setDisplayList(display);
    }
  },[nextPageIndex,paginateList]);
  

  function showFareType(i: number) {
    if (displayListRef.current) {
      const disList = displayList?.map((item, index) => {
        if (index === i) {
          return {
            ...item,
            isShowFareType: !item.isShowFareType
          };
        }
        return item;
      });
      setDisplayList(disList);
    }
  }

  function navigateReview() {
    sessionStorage.removeItem('dySearchTuiID');
    router.push('/flight/FlightReview')
  }

  function showFlightDetails(k: Journey) {
    
    const body: FlightDetailsEmitData = {
      TripType: 'ON',
      Trips: [[{
        "Amount": k.GrossFare,
        "ChannelCode": null,
        "Index": k.Index || '',
        "OrderID": 1,
        "TUI": TUIRef.current
      }]]
    }
    setFlightDetailsBodyData(body)
    setIsFlightDetailsPopup(true)
  }

  function resetFilter() {
    setIsMobileFilter(false)
    const dmmlist: Journey[] = JSON.parse(dummyList) as Journey[];
    setFilterData([
      {
        From: flightSearchData?.trips[0]?.from?.iata || '',
        To: flightSearchData?.trips[0]?.to?.iata || '',
        Tui: '',
        FilterData: filterHelper.setFlightFilterDate(dmmlist)
      }
    ])
    listConvertPagination(dmmlist);
  }

  function fliterDataGet() {
    const dmmlist: Journey[] = JSON.parse(dummyList) as Journey[];
    if (filterData && filterData.length > 0) {
      const list = filterHelper.addFilterInList(filterData[0]!.FilterData, dmmlist);
      listConvertPagination(list);
    }

  }

  function openFilter() {
    setIsMobileFilter(true)
  }

  function closeFilter() {
    setIsMobileFilter(false)
  }

  return (<>
    {listShimmer ? (
      <Loader />
    ):(
      <></>
    )
    }
    <div className="container mx-auto">
    <div className={styles["one-way-flight-list-div"]}>
      {(() => {
        if (!isNoDataFound) {
          return <>
            <div className={styles["filter-div"]}>
              <FilterFlight shimmer={filterShimmer} initialFilterData={filterData} currency={currency} handleFilterChange={fliterDataGet} emitFilter={fliterDataGet}></FilterFlight>
            </div>
            <div className={styles["list-div"]}>
              {listShimmer ? (
                [...Array.from({ length: 10 }).map((_, index: number) => (
                    <OneWayFlightListShimmer key={index} />
                  ))]
              ) : (
                displayList && displayList.length > 0 ? (
                  displayList.map((k, i) => (
                    <OneWayFlightCard
                      type="ON"
                      key={i}
                      flight={k}
                      tui={TUI}
                      emitFareTypeShow={() => showFareType(i)}
                      emitNavigate={navigateReview}
                      currency={currency}
                      isMobile={isMobile}
                      showDetails={() => showFlightDetails(k)}
                      isCache = {isCache}
                    />
                  ))
                ) : (
                  <div className="no-data-found-div">
                      <div className="main-txt">Sorry no results were found.</div>
                      <div>Please try changing your filters.</div>
                      <button className="out-line-button" onClick={resetFilter}><RestartAltRounded/> Reset</button>
                  </div>
                )
              )}



            </div>
          </>
        } else {
          return <div className="no-data-found-div">
            <div className="main-txt">Sorry no results were found.</div>
            <div>Please try changing your cities or dates. </div>
          </div>
        }
      })()}

    </div>
    </div>
    {isMobile && (
      <>
        <div className="mobile-filter-div" onClick={openFilter}>
            <FilterAltIcon className="maticon"/>
        </div>
        <BottomToTopPopup isOpen={isMobileFilter} onClose={()=>setIsMobileFilter(false)} heading="" type="filter">
            <FilterFlight handleClose={closeFilter} shimmer={filterShimmer} initialFilterData={filterData} currency={currency} handleFilterChange={fliterDataGet} emitFilter={fliterDataGet} />
        </BottomToTopPopup>
      </>
      )}
    {/* {(() => {
            if (isMobile) {
                return <div>
        // <dy-bottom-top-popup [open]="isMobileFilter" [heading]="'Filter'" (close)="closeFilter()">
        //     <div className={styles["mob-flight-filter-view-div"]}>
        //         <dy-flight-filter [shimmer]="filterShimmer" [filterData]="filterData" [currency]="currency"
        //             (emitFilter)="fliterDataGet()"></dy-flight-filter>
        //         <div className={styles["button-div"]}>
        //             <div className={styles["linkstyle"]} (click)="resetFilter()">Reset all filters</div>
        //             <button className={styles["button-search searchbtn"]} (click)="closeFilter()">Apply</button>
        //         </div >
        //     </div>
        // </dy-bottom-top-popup>
    </div >
    }
}) ()} */}

  </>);
}


export default OneWayFlightList;