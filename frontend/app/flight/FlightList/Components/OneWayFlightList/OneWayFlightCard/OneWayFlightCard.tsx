import { useEffect, useRef } from "react";
import React from 'react';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { Journey, SubFlight } from "models/flight-list-response-model";
import { useFlightState } from 'context/FlightState/FlightStateContext';
import styles from './OneWayFlightCard.module.scss'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import formatDate from "helpers/date-helper";
import { ArrowCircleLeft, ArrowCircleRight, BusinessCenter, CancelScheduleSend, Luggage, PublishedWithChanges } from "@mui/icons-material";

interface OneWayFlightCardProps {
  flight: Journey;
  tui: string;
  currency: string;
  isMobile: boolean;
  emitFareTypeShow: VoidFunction
  emitNavigate: VoidFunction
  emitSubFlightSelect?: (subFlight: SubFlight) => void
  showDetails: VoidFunction
  isCache: boolean
  type?:string
  forceShowDetails?: boolean;
}

const OneWayFlightCard: React.FC<OneWayFlightCardProps> = ({ flight, tui, currency = 'SAR', isMobile = false, emitFareTypeShow, emitNavigate, emitSubFlightSelect, showDetails , isCache = true ,type="ON", forceShowDetails = false }) => {
  const widgetsContentRef = useRef<HTMLDivElement | null>(null);
  const { selectFlight } = useFlightState();

  useEffect(()=>{
    // Flight data has changed - component will re-render automatically
  },[flight])
  function showFareType() {
    emitFareTypeShow();
  }

  function bookType(data: SubFlight) {
    // Create a modified journey with the selected sub-flight data
    const modifiedFlight: Journey = {
      ...flight,
      GrossFare: data.Amount || flight.GrossFare,
      Index: data.Index || flight.Index,
      ChannelCode: data.ChannelCode || flight.ChannelCode,
      FCType: data.FCType || flight.FCType,
      Refundable: data.Refund || flight.Refundable
    };

    console.log(data,"datatttattat");

    // Get sectType from localStorage (fallback to 'D' if not found)
    const searchSectorData = localStorage.getItem('dySearchSectorData');
    const sectType = searchSectorData ? (JSON.parse(searchSectorData) as any)?.SecType || 'D' : 'D';

    // Use context to select flight instead of session storage
    selectFlight(modifiedFlight, tui, sectType);
    emitNavigate();
  }

  function bookNow(data: Journey | undefined) {
    if (data) {
      console.log(data,"selected flight");

      // Get sectType from localStorage (fallback to 'D' if not found)
      const searchSectorData = localStorage.getItem('dySearchSectorData');
      const sectType = searchSectorData ? (JSON.parse(searchSectorData) as any)?.SecType || 'D' : 'D';

      // Use context to select flight instead of session storage
      selectFlight(data, tui, sectType);
      emitNavigate();
    }
  }

  function flightDetails() {
    showDetails();
  }

  function scrollLeft() {
    if (widgetsContentRef?.current) {
      widgetsContentRef.current.scrollLeft -= 250;
    }
  }

  function scrollRight() {
    if (widgetsContentRef?.current) {
      widgetsContentRef.current.scrollLeft += 250;
    }
  }

  // Currency converter (replace this with the actual logic)
  const FlightCurrencyConverter = (amount: number | undefined, currency: string) => {
    return amount ? `${currency} ${amount.toFixed(0)}` : "";
  };


  return (
    <div className={styles["flight-one-way-mob-desk-div"]}>

      <div className={`${styles["one-way-flight-list-card"]} ${flight.isSelect ? styles.active : ''}`}>
        <div className={styles["first-div"]}>
          <div className={styles["left-div"]}>
            <div className={styles["airline-name-div"]}>
              <div className={styles["logo"]}><img src={`../../../../assets/images/AirlineLogo/${flight?.MAC}.png`} width={50} height={50} alt="" /></div>
              <div>
                <div className={styles["flight-name"]}>{flight && flight.AirlineName ? flight.AirlineName.split('|')[1] : ''}</div>
                <div className={styles["flight-no"]}>{flight?.MAC} - {flight?.FlightNo}</div>
              </div>
            </div>
            <div className={`${styles["date-time-place-div"]} ${styles['depature-div']}`}>
              <div className={styles["time-div"]}>{formatDate(flight?.DepartureTime, 'HH:mm')}</div>
              <div className={styles["place-div"]}>{flight?.FromName ? flight.FromName.split('|')[1]?.split(',')[0] : ''}

                {flight?.FromName?.split('|')[1]?.split(',')[1] && (
                  <strong style={{ color: "#000" }}>  {flight?.FromName?.split('|')[1]?.split(',')[1]}
                  </strong>
                )}
              </div>
              <div className={styles["place-div"]}>{formatDate(flight?.DepartureTime, 'EEE, d LLL,yyyy')}</div>
            </div>
            <div className={styles["duration-connection-div"]}>
              <h3>{flight?.Duration?.slice(0, 2)} <span>Hrs</span> {flight?.Duration?.slice(4, 6)}
                <span>Mins</span>
              </h3>
              <div className={styles["flight-line-div"]}>
                <div className={styles["connections"]}>
                  {flight?.Connections.map((c) => {
                    return <React.Fragment key={c.MAC}><span className={styles["connection-dot"]} data-tip={c.ArrAirportName} ></span>
                      <ReactTooltip place="top" /></React.Fragment>
                  })}
                </div>
              </div>
              {(() => {
                if (flight?.ConnectionText != '') {
                  return <div className={styles["connections-text"]}>{flight?.ConnectionText}</div>
                }
              })()}
            </div>
            <div className={`${styles["date-time-place-div"]} ${styles["arrival-div"]}`}>
              <div className={styles["time-div"]}>{formatDate(flight?.ArrivalTime, 'HH:mm')}</div>
              <div className={styles["place-div"]}>{flight?.ToName ? flight.ToName.split('|')[1]?.split(',')[0] : ''}
                {flight?.ToName?.split('|')[1]?.split(',')[1] && (
                  <strong style={{ color: "#000" }} >  {flight?.ToName?.split('|')[1]?.split(',')[1]}</strong>
                )}
              </div>
              <div className={styles["place-div"]}>{formatDate(flight?.ArrivalTime, 'EEE, d LLL,yyyy')}</div>
            </div>
          </div>
          <div className={styles["right-div"]}>
            {isCache ? (
              <>
                <div className={`${styles["price-div-shimmer"]} ${styles["shine"]}`}></div>
                <div className={`${styles["button-div-shimmer"]} ${styles["shine"]}`}>Book Now</div>
                <div className={`${styles["price-div-shimmer"]} ${styles["shine"]}`}></div>
              </>
            ):(
              <>
                <div className={styles["price-div"]}>{FlightCurrencyConverter(flight?.GrossFare, '₹')}</div>
                {(() => {
                  if (flight && flight.SubFlights && flight?.SubFlights?.length === 1) {
                    return <button className="dy_primary_bttn" onClick={() => type === 'ON'? bookNow(flight) : emitNavigate()}>{type === 'ON' ? 'Book Now' : 'Select'}</button>

                  } else if (flight && flight.SubFlights && flight.SubFlights.length > 1) {
                    return <button className="dy_primary_bttn" onClick={showFareType} >{type === 'ON' ? 'Book Now' : 'Select'} <KeyboardArrowDownIcon /></button>
                  }
                })()}
                {type === 'ON' && !forceShowDetails && (
                  <div className="linkBtn" onClick={flightDetails}>+ Flight Details</div>
                )}
                {forceShowDetails && (
                  <div className={styles["flight-details-section"]}>
                    <div className={styles["details-content"]}>
                      <div className={styles["detail-item"]}>
                        <span>Flight Duration: </span>
                        <strong>{flight?.Duration?.slice(0, 2)}h {flight?.Duration?.slice(4, 6)}m</strong>
                      </div>
                      <div className={styles["detail-item"]}>
                        <span>Aircraft: </span>
                        <strong>{flight?.MAC}</strong>
                      </div>
                      <div className={styles["detail-item"]}>
                        <span>Refundable: </span>
                        <strong>{flight?.Refundable === 'Y' ? 'Yes' : 'No'}</strong>
                      </div>
                      {flight?.ConnectionText && (
                        <div className={styles["detail-item"]}>
                          <span>Connections: </span>
                          <strong>{flight.ConnectionText}</strong>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
        {(() => {
          if (flight?.isShowFareType) {
            //   return  <div className={styles["flight-type-div"]} [@expand] >

            return <div className={styles["flight-type-div"]}>

              {(() => {
                if (flight && flight.SubFlights && flight.SubFlights.length > 3) {
                  return <div className={styles["left-right-icon"]} onClick={scrollLeft} >
                    <ArrowCircleLeft />
                  </div>
                }
              })()}



              <div
                ref={widgetsContentRef}
                className={`${styles["custom-slider-main"]} ${flight?.SubFlights && flight.SubFlights.length <= 3 ? styles["custome-slider-center"] : ''
                  }`}
              >
                {flight?.SubFlights!.map((sub, index) => {
                  return <React.Fragment key={index}>
                    <><div className={styles["flight-type-card"]}>
                      <div className={styles["header-type"]}>
                        <div className={styles["logo"]}><img src={`../../../../assets/images/AirlineLogo/${flight?.MAC}.png`} width={50} height={50} alt="" /></div>
                        <div>{sub?.FCType}</div>
                      </div>

                      <div className={styles["tp-type-div"]}>
                        <div className={styles["section-service"]}>
                          <div className={styles["head-div"]}>Baggage</div>
                          <div className={styles["icon-value"]}>
                            <BusinessCenter className={styles["icon"]} />
                            <span>Carry-on Baggage: </span>
                            <strong>1 piece</strong>
                          </div>
                          <div className={styles["icon-value"]}>
                            <Luggage className={styles["icon"]} />
                            <span>Checked Baggage: </span>
                            <strong>1 piece</strong>
                          </div>
                        </div>

                        <div className={styles["section-service"]}>
                          <div className={styles["head-div"]}>Fare Rules</div>
                          <div className={styles["icon-value"]}>
                            <CancelScheduleSend className={styles["icon"]} />
                            <span>Cancellation fee: from INR 3,000.00 </span>
                          </div>
                          <div className={styles["icon-value"]}>
                            <PublishedWithChanges className={styles["icon"]} />
                            <span>Change fee: from INR 2,500.00 </span>
                          </div>
                        </div>

                      </div>

                      {/* <div className={styles["body-of-type"]}>
                        <div className={styles["left-type"]}>
                          <div className={styles["icon-txt"]}>
                            <i className={styles["fa fa-suitcase icon-clr"]}></i>
                            <span>Baggage</span>
                          </div>
                          <div className={styles["icon-txt"]}>
                            <span className={"fa fa-wheelchair icon-clr"}></span>
                            <span>Seat selection</span>
                          </div>
                          <div className={styles["icon-txt"]}>
                            <span className={"fa fa-cutlery icon-clr"}></span>
                            <span>Meal</span>
                          </div>
                          <div className={styles["icon-txt"]}>
                            <i className={"fa fa-coins icon-clr"}></i>
                            <span>Refund</span>
                          </div>
                        </div>
                        <div className={styles["right-type"]}>
                          <div>{sub?.Baggage}</div>
                          <div>{sub?.SeatSelection}</div>
                          <div>{sub?.Meal}</div>
                          <div>
                            {(() => {
                              if (sub?.Refund == 'Y') {
                                return <span className={styles["refund-clr"]}>Refundable</span>

                              } else if (sub?.Refund == 'N') {
                                return <span className={styles["nonrefund-clr"]}>Non Refundable</span>

                              }

                            })()}
                          </div>
                        </div>
                      </div> */}

                      <div className={styles["price-button-div"]}>
                        <div className={styles["price-type"]}>
                          {FlightCurrencyConverter(sub?.Amount, '₹')}
                        </div>
                        <button className="dy_primary_bttn" onClick={() => type === "ON" ?  bookType(sub) : (emitSubFlightSelect ? emitSubFlightSelect(sub) : emitNavigate())}>{type === "ON" ? "Book" : "Select"}</button>
                      </div>

                    </div>
                    </>
                  </React.Fragment>
                })}

              </div>

              {(() => {
                if (flight && flight.SubFlights && flight.SubFlights.length > 3) {
                  return <div className={styles["left-right-icon"]} onClick={scrollRight} >
                    <ArrowCircleRight />
                  </div>
                }
              })()}

            </div>
          }
        })()}

      </div>


      <div className={styles["one-way-mob-div"]} onClick={() => bookNow(flight)}>
        <div className={styles["airline-name-number-div"]}>
          <div className={styles["airline-name"]}>
            {flight?.AirlineName ? flight.AirlineName.split('|')[1] : ''}
          </div>
          <div>|</div>
          <div className={styles["number-div"]}>{`${flight?.MAC} - ${flight?.FlightNo}`}</div>
        </div>

        <div className={styles["logo-dep-ari-dur-div"]}>
          <div className={styles["logo-dep-div"]}>
            <div className={styles["log-div"]}>
              <img src={`../../../../assets/images/AirlineLogo/${flight?.MAC}.png`} width={50} height={50} alt="" />
            </div>
            <div className={`${styles["dip-ari-div"]} ${styles['dep-align-div']}`}>
              <div className={styles["time-div"]}>
                {flight?.DepartureTime ? new Date(flight.DepartureTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''}
              </div>
              <div className={styles["loc-div"]}>
                {flight?.FromName ? flight.FromName.split('|')[1]?.split(',')[0] : ''}
                {flight?.FromName?.split('|')[1]?.split(',')[1] && (
                  <strong style={{ color: "#000" }} >   {flight?.FromName?.split('|')[1]?.split(',')[1]}</strong>
                )}

              </div>
            </div>
          </div>

          <div className={styles["duration-connection-div"]}>
            <h4>
              {flight?.Duration?.slice(0, 2)} <span>h</span> {flight?.Duration?.slice(4, 6)} <span>m</span>
            </h4>
            <div className={styles["flight-line-div"]}>
              <div className={styles["connections"]}>
                {flight?.Connections?.map((c, index) => (
                  <span key={index} className={styles["connection-dot"]} title={c.ArrAirportName}></span>
                ))}
              </div>
            </div>
            {flight?.ConnectionText && (
              <div className={styles["connections-text"]}>{flight.ConnectionText}</div>
            )}
          </div>

          <div className={`${styles["dip-ari-div"]} ${styles["arr-align-div"]}`}>
            <div className={styles["time-div"]}>
              {flight?.ArrivalTime ? new Date(flight.ArrivalTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''}
            </div>
            <div className={styles["loc-div"]}>
              {flight?.ToName ? flight.ToName.split('|')[1]?.split(',')[0] : ''}
              {flight?.ToName?.split('|')[1]?.split(',')[1] && (
                <strong style={{ color: "#000" }} > {flight?.ToName?.split('|')[1]?.split(',')[1]}</strong>
              )}
            </div>
          </div>
        </div>

        <div className={styles["price-div"]}>
          <div>
            {flight?.Refundable === 'Y' && (
              <span className={`${styles["refund-txt"]} ${styles["refund-box"]}`}>{flight.Refundable}</span>
            )}
            {flight?.Refundable === 'N' && (
              <span className={`${styles["refund-txt"]} ${styles["nonrefund-box"]}`}>{flight.Refundable}</span>
            )}
          </div>
          <div className={styles["price-text"]}>
            {FlightCurrencyConverter(flight?.GrossFare, '₹')}
          </div>
        </div>
      </div>

    </div>
  );
}

export default OneWayFlightCard;