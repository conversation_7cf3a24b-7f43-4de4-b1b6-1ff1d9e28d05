"use client"
import BorderColorIcon from '@mui/icons-material/BorderColor';
import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation'
import { useCallback, useEffect, useState } from "react"
import React from "react"
import { DyRightSidePopup } from "components/DyRightSidePopup/DyRightSidePopup"
import { useGeneralState } from "context/GeneralState/GeneralStateContext"
import { FlightDetailsEmitData } from "models/flight-info-ssr-model"
import { FormSearch } from "models/flight-search-model"
import { useFlightState } from "services/flight-state-service"
import OneWayFlightList from "./Components/OneWayFlightList/OneWayFlightList"
import RoundTrip from "./Components/RoundTrip/RoundTrip"
import styles from './page.module.scss';
import { AirlineDetailsPopup } from "../FlightDetailsPopup/FlightDetailsPopup"
import DyFlightSearchSection from "../FlightHomePage/Components/DyFlightSearchSection/DyFlightSearchSection"


export default function FlightListPage() {
  const [flightSearchData, setFlightSearchData] = useState<FormSearch>()
  const [currency, setCurrency] = useState<string>('INR')
  const [isMobile, setIsMobile] = useState<boolean>(false)
  const [isModify, setIsModify] = useState<boolean>(false)
  // const [isFlightDetails, setIsFlightDetails] = useState<boolean>(false)
  // const [slug,setSlug] = useState();
  const { globalCurrency } = useGeneralState();
  const { flightDetailsBodyData, setFlightDetailsBodyData, isFlightDetailsPopup, setIsFlightDetailsPopup } = useFlightState()

  const route = useRouter();
  // const pathName = usePathname();

  const initialCall = useCallback(() => {
    if (typeof flightDetailsBodyData === 'string') {
  setFlightDetailsBodyData(JSON.parse(flightDetailsBodyData) as FlightDetailsEmitData);
}

// setIsFlightDetails(isFlightDetailsPopup);

// flightSatate.isFlightDetailsPopup.subscribe(data => isFlightDetails = data);

setCurrency(globalCurrency)
// userService.globalCurrency.subscribe(data => currency = data); 

}, [flightDetailsBodyData, globalCurrency, setFlightDetailsBodyData, setCurrency]);

  useEffect(() => {
    initialCall();
    // const slugs = pathName.split('/')
    // if(slugs.length > 2){
    //   const extractSlug = slugs[slugs.length - 1];
    //   if(extractSlug){
    //     setSlug(extractSlug) 
    //   }
    // }
    // console.log(slug,"slug test");
    
  }, [flightDetailsBodyData, isFlightDetailsPopup, globalCurrency,initialCall])





  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 950);
    };

    window.addEventListener('resize', handleResize);

    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  useEffect(() => {
    checkStorage();
  }, [])

  function modifySearch() {
    setIsModify(true)
  }

  function closeModify() {
    setIsModify(false)
  }

  function hideFlightDetails() {
    setIsFlightDetailsPopup(false);
  }

  function mobModySearch() {
    if (isMobile)
      modifySearch()
  }

  function checkStorage() {
    setIsModify(false)
    if (localStorage && localStorage.getItem('dySearchSectorData')) {
      const search = localStorage.getItem('dySearchSectorData');
      if (search != null) {
        setFlightSearchData(JSON.parse(search) as FormSearch)
      }
    } else {
      //rewrite
      // route.navigate(['flight-bookings'])
    }
  }

  function handleNavigation(url:string){
    route.push(url);
  }

  const formatDate = (date: string) => {
    return format(new Date(date), 'dd MMM');
  }

  return (
    <>
      <div className={styles["flight-list-div"]}>
        <div className={styles["head-ribben-div"]}>
          <div className={'container mx-auto'}>
            <div className={styles["header-data-div"]}>
              <div className={styles["mob-modi-txt"]} onClick={mobModySearch}>
                <div className={styles["mob-icon"]}><BorderColorIcon /></div>
                <div>
                  <h1 className={`${styles["head_font"]} ${styles['airport-list']}`}>
                    {flightSearchData && flightSearchData.trips.map((k: any) => (
                      <React.Fragment key={k}> <strong>{k.from.city}</strong> - <strong>{k.to.city}</strong> <strong className={styles["divider-bar"]}>|</strong></React.Fragment>
                    ))}
                  </h1>
                  <div className={styles["detail-search-data"]}>
                    <span>{flightSearchData && (flightSearchData.FareType === 'ON' ? 'One Way' : flightSearchData.FareType === 'RT' ? 'Round Trip' : flightSearchData.FareType === 'MC' ? 'Multi City' : '')},</span>
                    {
                      flightSearchData &&
                      flightSearchData.trips.map((k: any) => (
                        <div key={k}>
                          {(() => {
                            if (flightSearchData.FareType === 'RT') {
                              return <><span>{formatDate(k.depart)}</span><span>-</span><span>{formatDate(k.return)}</span></>
                            } else {
                              return <><span>{formatDate(k.depart)}</span><span className={styles["divider-bar"]}>-</span></>
                            }
                          })()}
                        </div>
                      ))
                    }

                  </div>
                </div>
              </div>
              <button className={styles["modify-bttn"]} onClick={modifySearch} >Modify Search</button>  
            </div>
          </div>
        </div>

        {(() => {
          if (isModify) {
            return <div className={styles["modify-popup-div"]} >
              <div className={styles["all-bg"]}></div>
              <div className={`${styles["pop-up-div"]} container mx-auto`}>
                <div className={styles["pop-up-brdr"]}>
                  <div className={styles["modify-close-bttn"]}>
                    <CloseIcon className={styles["mody-cl"]} onClick={closeModify} />
                  </div>
                  <DyFlightSearchSection searchEmit={checkStorage} isModify={true}></DyFlightSearchSection>
                </div>
              </div>
            </div>
          }
        })()}

        {(() => {
          if (flightSearchData?.FareType === 'ON') {
            return <div className={styles["container"]}>
              <OneWayFlightList currency={currency} isMobile={isMobile} getFlightSearchBody={flightSearchData}></OneWayFlightList>
            </div>
          }
        })()}

        {(() => {
          if (flightSearchData?.FareType === 'RT' && flightSearchData?.SecType === 'D') {
            return <div className={`${styles["container"]} ${styles["rt-container"]}`} >
              {/* <FlightRoundTripSplitView getFlightSearchBody={flightSearchData} currency={currency} isMobile={isMobile} ></FlightRoundTripSplitView> */}
              <RoundTrip onNavigate={handleNavigation} getFlightSearchBody={flightSearchData} currency={currency} isMobile={isMobile}/>
            </div>
          }
        })()}



      </div>

        {isFlightDetailsPopup && <DyRightSidePopup hide={hideFlightDetails} heading={'Your Flight Details'} zindex={201}>
          <AirlineDetailsPopup flightDetailsBody={flightDetailsBodyData} isMobile={isMobile}
          currency={currency}></AirlineDetailsPopup>
        </DyRightSidePopup>} 

    </>
  )
}
