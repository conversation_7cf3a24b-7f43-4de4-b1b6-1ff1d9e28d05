'use client'
import Script from "next/script";
import { GeneralStateProvider } from "context/GeneralState/GeneralStateProvider"
import { FlightStateProvider } from "context/FlightState/FlightStateProvider"
import "styles/tailwind.css";
import "styles/global.scss";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const GTM_ID = "GTM-PZ99F8B5";

  return (
    <html lang="en">
      <head>
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','${GTM_ID}');
    `,
          }}
        />
      </head>
      <body>
        <noscript>
          <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-PZ99F8B5"
            height="0" width="0" style={{ display: 'none', visibility: 'hidden' }} title="Google Tag Manager"></iframe>
        </noscript>
        <GeneralStateProvider>
          <FlightStateProvider>
            {children}
          </FlightStateProvider>
        </GeneralStateProvider>
      </body>
    </html>
  )
}
