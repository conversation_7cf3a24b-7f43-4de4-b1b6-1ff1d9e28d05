"use client";

import RefreshIcon from '@mui/icons-material/Refresh';
import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react';
import FlightApiService from 'services/flight-api-service';
import "styles/global.scss"

const Page: React.FC = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    name: '',
    phone_number: '',
    phone_country_code: '91',
    email: '',
    role:'admin'
  });
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);
  const [isTermsPopupOpen, setIsTermsPopupOpen] = useState(false);
  const api = FlightApiService;
  const [isLoader, setIsLoader] = useState(false);

  // Initialize token on component mount
  useEffect(() => {
    const storedToken = localStorage.getItem('dyAccessToken');
    if (storedToken) {
      api.setToken(storedToken);
      console.log('Token initialized from localStorage:', storedToken);
    }
  }, []);

  // Login/Register mode toggle - Initially show login
  const [isLoginMode, setIsLoginMode] = useState(true);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);

  // OTP verification states
  const [showOtpStep, setShowOtpStep] = useState(false);
  const [userId, setUserId] = useState<number | null>(20);
  const [otpValue, setOtpValue] = useState(['', '', '', '', '', '']);
  const [isOtpLoader, setIsOtpLoader] = useState(false);
  const [otpError, setOtpError] = useState('');

  useEffect(() => {
    const user = localStorage.getItem('dyUser');

    if(user != null){
      router.push('flight/FlightHomePage')
    }

  }, [router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsTermsAccepted(e.target.checked);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLoginMode) {
      // Login flow - only email required
      if (!formData.email) {
        alert('Please enter your email address.');
        return;
      }

      setIsLoader(true);

      try {
        const data = await api.loginSubmit({ email: formData.email , userId:userId });

        if(data.id && data.email){
          setUserId(data.id);
        } else {
          // Set a dummy user ID if API doesn't return one
          setUserId(1);
        }
      } catch(error) {
        // Even if API fails, show OTP form for login
        setUserId(1);
        console.log('Login API failed, but showing OTP form:', error);
      }

      // Always show OTP step for login
      setShowOtpStep(true);
      setIsLoader(false);

    } else {
      // Register flow - terms acceptance required
      if (isTermsAccepted) {

        setIsLoader(true)

        try {

          const data = await api.registerSubmit(formData);

          if(data.id && data.email){
            setUserId(data.id);
            setShowOtpStep(true); // Only show OTP on success
            setIsLoader(false);
          } else {
            setIsLoader(false);
            alert('Registration failed. Please try again.');
          }

        } catch(error){
          // For registration, show error and stay on form
          setIsLoader(false);
          alert('Registration failed. Please try again.');
          console.log('Registration API failed:', error);
        }

      } else {
        alert('Please accept the terms and conditions.');
      }
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtpValue = [...otpValue];
      newOtpValue[index] = value;
      setOtpValue(newOtpValue);
      setOtpError(''); // Clear error when user types

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otpValue[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

const handleRegistrationOtpSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  const otpString = otpValue.join('');
  if (!userId || otpString.length !== 6) {
    setOtpError('Please enter the complete 6-digit OTP');
    return;
  }

  setIsOtpLoader(true);
  setOtpError('');

  try {
    // Use registration OTP verification endpoint
    const data = await api.verifyOtp({ email: formData.email, otp: otpString });

    if(data.message === 'user registration successfull'){
      // For registration: show success and switch to login
      setRegistrationSuccess(true);
      setShowOtpStep(false);
      setIsLoginMode(true);
      setOtpValue(['', '', '', '', '', '']);
      // setUserId(null);
      setIsOtpLoader(false);
      // Reset form data but keep email for login
      setFormData({
        name: '',
        phone_number: '',
        phone_country_code: '91',
        email: formData.email,
        role: 'admin'
      });
      setIsTermsAccepted(false);

      // Show success message for 3 seconds then hide it
      setTimeout(() => {
        setRegistrationSuccess(false);
      }, 3000);

      return;
    } else {
      setOtpError(data.message || 'Invalid OTP. Please try again.');
      setIsOtpLoader(false);
    }

  } catch(error) {
    console.error('Registration OTP verification error:', error);
    setOtpError('Failed to verify registration OTP. Please try again.');
    setIsOtpLoader(false);
  }
};

const handleLoginOtpSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  const otpString = otpValue.join('');
  if (!userId || otpString.length !== 6) {
    setOtpError('Please enter the complete 6-digit OTP');
    return;
  }

  setIsOtpLoader(true);
  setOtpError('');

  try {
    // Use login OTP verification endpoint
    const data = await api.verifyLoginOtp({ email: formData.email, otp: otpString });

    if(data.access_token){
      // For login: store access_token and navigate home
      localStorage.setItem('dyUser', JSON.stringify({
        email: formData.email,
        access_token: data.access_token
      }));
      localStorage.setItem('dyAccessToken', data.access_token);

      // Set the token in the API service
      api.setToken(data.access_token);

      navigateHome();
    } else {
      setOtpError(data.message || 'Invalid OTP. Please try again.');
      setIsOtpLoader(false);
    }

  } catch(error) {
    console.error('Login OTP verification error:', error);
    setOtpError('Failed to verify login OTP. Please try again.');
    setIsOtpLoader(false);
  }
};

  const handleResendOtp = async () => {
    if (!userId) return;

    setIsOtpLoader(true);
    try {
      if (isLoginMode) {
        // Resend OTP by calling login again with email
        await api.loginSubmit({ email: formData.email , userId:userId });
        alert('OTP resent successfully!');
      } else {
        // Resend OTP by calling register again with same data
        await api.registerSubmit(formData);
        alert('OTP resent successfully!');
      }
    } catch(error) {
      if (isLoginMode) {
        // For login, show success even if API fails
        alert('OTP resent successfully!');
        console.log('Resend login OTP API failed, but showing success:', error);
      } else {
        // For registration, show actual error
        setOtpError('Failed to resend OTP. Please try again.');
        console.log('Resend registration OTP API failed:', error);
        setIsOtpLoader(false);
        return;
      }
    }

    // Reset OTP inputs and clear errors
    setOtpValue(['', '', '', '', '', '']);
    setOtpError('');
    setIsOtpLoader(false);
  };

  const toggleMode = () => {
    setIsLoginMode(!isLoginMode);
    setShowOtpStep(false);
    setRegistrationSuccess(false);
    setUserId(null);
    setOtpValue(['', '', '', '', '', '']);
    setOtpError('');
    setIsLoader(false);
    setIsOtpLoader(false);
    // Reset form data for login mode (keep only email)
    if (!isLoginMode) {
      setFormData({
        name: '',
        phone_number: '',
        phone_country_code: '91',
        email: formData.email, // Keep email if switching to login
        role: 'admin'
      });
      setIsTermsAccepted(false);
    }
  };


const navigateHome = async () => {
  try {
    console.log("Navigation function called");
    await router.push('/flight/FlightHomePage'); // Added leading slash for absolute path
    console.log("Navigation completed successfully");
  } catch (error) {
    console.error("Navigation failed:", error);
    // Optional: Show error message to user
    setOtpError('Navigation failed. Please try again.');
    setIsOtpLoader(false);
  }
};


  return (
    <>
      <div className="container-signup">
        <div className="signup-container">
          <div className="left-section">
            <h1><strong>Welcome to DigiYatra.in</strong></h1>
            <br />
            <p>
              {isLoginMode
                ? 'Welcome back! Please login to access your account and continue your journey with us.'
                : 'Join us today to experience the best features and services tailored for you.'
              }
            </p>
            <br />
            <p>{isLoginMode ? 'Login to continue!' : 'Register now to get started!'}</p>
          </div>
          <div className="right-section">
            {registrationSuccess && (
              <div className="success-message" style={{
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 1000,
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                fontSize: '16px',
                fontWeight: '500',
                backgroundColor: '#4caf50',
                color: 'white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
              }}>
                <span style={{ fontSize: '20px' }}>✓</span>
                Registration successful! Please login to continue.
              </div>
            )}
            {!showOtpStep ? (
              <form onSubmit={handleSubmit} className="signup-form">

                <h2>{isLoginMode ? 'Login' : 'Register Now'}</h2>

                {!isLoginMode && (
                  <>
                    <input
                      type="text"
                      name="name"
                      placeholder="Full Name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                    <div className="phone-input">
                      <span className="country-code">+91</span>
                      <input
                        type="tel"
                        name="phone_number"
                        placeholder="Phone Number"
                        value={formData.phone_number}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                  </>
                )}

                <input
                  type="email"
                  name="email"
                  placeholder="Email Address"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />

                {!isLoginMode && (
                  <div className="terms-checkbox">
                    <input
                      style={{cursor:'pointer'}}
                      type="checkbox"
                      className='checkbox'
                      id="terms"
                      checked={isTermsAccepted}
                      onChange={handleCheckboxChange}
                    />
                    <label htmlFor="terms">
                      I accept the{' '}
                      <span className="terms-link" onClick={() => setIsTermsPopupOpen(true)}>
                        terms and conditions
                      </span>
                    </label>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isLoginMode ? false : !isTermsAccepted}
                  className={` dy_primary_bttn ${isLoader ? ' active' : ''}`}
                >
                  <RefreshIcon className='btn_icon' />
                  <span>{isLoginMode ? 'Send OTP' : 'Register'}</span>
                </button>
                <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '20px' }}>
                  <button
                    type="button"
                    onClick={toggleMode}
                    className="mode-toggle-btn"
                  >
                    {isLoginMode ? 'Need to register? Click here' : 'Already have an account? Login here'}
                  </button>
                </div>
              </form>
            ) : (
              <form onSubmit={isLoginMode ? handleLoginOtpSubmit : handleRegistrationOtpSubmit} className="signup-form">
                <div className="otp-section">
                  <div className="otp-icon">
                    📱
                  </div>
                  <h2 className="otp-title">Verify OTP</h2>
                  <p className="otp-description">
                    We've sent a verification code to your {isLoginMode ? 'email address' : 'phone number'}
                  </p>

                  <div className="otp-inputs">
                    {otpValue.map((digit, index) => (
                      <input
                        key={index}
                        id={`otp-${index}`}
                        type="text"
                        value={digit}
                        onChange={(e) => handleOtpChange(index, e.target.value)}
                        onKeyDown={(e) => handleOtpKeyDown(index, e)}
                        maxLength={1}
                      />
                    ))}
                  </div>

                  {otpError && (
                    <div className="otp-error">
                      <p>{otpError}</p>
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={otpValue.join('').length !== 6 || isOtpLoader}
                    className={`dy_primary_bttn otp-submit-btn ${isOtpLoader ? ' active' : ''}`}
                    style={{
                      opacity: otpValue.join('').length !== 6 ? 0.6 : 1
                    }}
                  >
                    <RefreshIcon className='btn_icon' />
                    <span>Verify OTP</span>
                  </button>

                  <div style={{ textAlign: 'center' }}>
                    <p style={{ color: '#666', fontSize: '14px', marginBottom: '10px' }}>
                      Didn't receive a code?
                    </p>
                    <button
                      type="button"
                      onClick={handleResendOtp}
                      disabled={isOtpLoader}
                      className="back-to-form-btn"
                    >
                      Resend OTP
                    </button>
                  </div>
                </div>
              </form>
            )}
          </div>
        </div>

        {isTermsPopupOpen && (
          <div className="terms-popup">
            <div className="terms-popup-content">
              <h2>Disclaimer for Data Access and User Experience</h2>

              <p>To provide you with a personalized and seamless travel planning experience, we may collect and use certain personal information, such as your browsing preferences, travel searches, and booking history. This data helps us recommend the most relevant travel options, destinations, and offers tailored to your interests.</p>
              <p>We are committed to protecting your privacy and handle all data in compliance with applicable data protection laws. Information collected is used solely to improve our services and enhance your experience on our website. We do not share or sell your personal data to third parties without your explicit consent, except as required by law or for fulfilling your travel requests.</p>
              <p>By using our website, you agree to this collection and usage of data as outlined in our Privacy Policy.</p>
              <button onClick={() => setIsTermsPopupOpen(false)}>Close</button>
            </div>
          </div>
        )}
      </div>

      {/* <style jsx>{`
        .container-signup {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          background-color: #f4f4f4;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          z-index: 9999;
        }
        .signup-container {
          display: flex;
          width: 60%;
          background-color: white;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          overflow: hidden;
        }
          @media screen and (max-width: 768px) {
            .signup-container {
              width: 95%;
              flex-direction: column;
            }
          }
        .left-section {
          flex: 1;
          padding: 40px;
          background-color: #FF671F;
          color: white;
          display: flex;
          flex-direction: column;
          justify-content: center;
          @media screen and (max-width: 768px) {
            padding: 10px;
          }
        }
        .right-section {
          flex: 1;
          padding: 40px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          @media screen and (max-width: 768px) {
            padding: 10px;
          }
        }
        .signup-form {
          display: flex;
          flex-direction: column;
        }
        .signup-form h2 {
          margin-bottom: 20px;
          text-align: center;
        }
        .signup-form input {
          margin-bottom: 15px;
          padding: 12px;
          font-size: 16px;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
        .phone-input {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
        }
        .country-code {
          padding: 12px;
          background-color: #eee;
          border: 1px solid #ccc;
          border-radius: 4px 0 0 4px;
          font-size: 16px;
          margin-right: -1px;
          margin-bottom: 15px;
        }
        .phone-input input {
          flex: 1;
          border-radius: 0 4px 4px 0;
        }
        .terms-checkbox {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
        }
        .terms-link {
          color: #1976d2;
          cursor: pointer;
          margin-left: 5px;
          text-decoration: underline;
        }
        .signup-form button {
          padding: 12px;
          font-size: 16px;
          color: white;
          background-color: ${isTermsAccepted ? '#046A38' : '#aaa'};
          border: none;
          border-radius: 4px;
          cursor: ${isTermsAccepted ? 'pointer' : 'not-allowed'};
        }
        .signup-form button:hover {
          background-color: ${isTermsAccepted ? '#046A38' : '#aaa'};
        }
        .terms-popup {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.5);
        }
        .terms-popup-content {
          background-color: white;
          padding: 30px;
          border-radius: 8px;
          width: 50%;
          text-align: center;
          @media screen and (max-width: 768px) {
            width: 95%;
            padding: 10px;
          }
        }
        .terms-popup-content h2 {
          margin-bottom: 20px;
          font-size: 20px;
          font-weight: 600;
          text-align: justify;
        }
        .terms-popup-content p {
          margin-bottom: 20px;
          text-align: justify;
          font-size: 12px;
        }
        .terms-popup-content button {
          padding: 10px 20px;
          background-color: #1976d2;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }
        .checkbox{
          margin-bottom: 0 !important;
          margin-right: 10px;
        }
      `}</style> */}
    </>
  );
};

export default Page;
