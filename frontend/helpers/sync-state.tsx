import { useRef, useState } from "react";

export function useSyncState<T>(initialValue: T): [T, (value: T) => void, React.MutableRefObject<T>] {
    const [state, setState] = useState<T>(initialValue);
    const ref = useRef<T>(initialValue);
  
    const updateState = (value: T) => {
      ref.current = value;  // Update the ref
      setState(value);      // Update the state
    };
  
    return [state, updateState, ref];
  }