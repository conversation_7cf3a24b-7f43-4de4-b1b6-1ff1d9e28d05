import { format, isValid, parseISO } from 'date-fns';

export default function formatDate(dt: any, fm: string): string {
    // Handle null, undefined, or empty values
    if (!dt) {
        return '';
    }

    let date: Date;

    // If dt is already a Date object
    if (dt instanceof Date) {
        date = dt;
    } else if (typeof dt === 'string') {
        // Try to parse ISO string first (common format from APIs)
        try {
            date = parseISO(dt);
        } catch {
            // Fallback to regular Date constructor
            date = new Date(dt);
        }
    } else {
        // Try to convert to Date
        date = new Date(dt);
    }

    // Check if the date is valid
    if (!isValid(date)) {
        console.warn('Invalid date value:', dt);
        return '';
    }

    try {
        return format(date, fm);
    } catch (error) {
        console.error('Date formatting error:', error, 'Date:', dt, 'Format:', fm);
        return '';
    }
}
