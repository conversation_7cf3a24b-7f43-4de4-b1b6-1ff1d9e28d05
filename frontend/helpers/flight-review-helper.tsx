interface PaxCount {
    title: string;
    price: number;
    id: string;
    count: number;
    totalPrice: number;
  }
  
  interface TaxItem {
    title: string;
    totalPrice: number;
  }
  
  interface FareSummary {
    baseFare: {
      title: string;
      total: number;
      subFare: PaxCount[];
    };
    taxs: {
      title: string;
      total: number;
      subFare: TaxItem[];
    };
    addons: {
      title: string;
      total: number;
      subFare: any[];
    };
    totalWithotAddons: number;
    totalAmount: number;
  }
  
  class FlightReviewPageHelper {
    setFareSummary(response: any): FareSummary {
      const adult = response?.ADT || 0;
      const child = response?.CHD || 0;
      const infant = response?.INF || 0;
  
      const fareSummary: FareSummary = {
        baseFare: {
          title: 'Base Fare',
          total: 0,
          subFare: [
            { title: 'Adult', price: 0, id: 'ADT', count: adult, totalPrice: 0 },
            { title: 'Child', price: 0, id: 'CHD', count: child, totalPrice: 0 },
            { title: 'Infant', price: 0, id: 'INF', count: infant, totalPrice: 0 },
          ],
        },
        taxs: {
          title: 'Tax & Charges',
          total: 0,
          subFare: [
            { title: 'Fuel Surcharge', totalPrice: 0 },
            { title: 'Psgr Svs. Fee', totalPrice: 0 },
            { title: 'User Dev. Fee', totalPrice: 0 },
            { title: 'K3 Tax', totalPrice: 0 },
            { title: 'Airline Misc', totalPrice: 0 },
            { title: 'Total VAT', totalPrice: 0 },
            { title: 'User Dev. Fee – Arrival(UDF)', totalPrice: 0 },
            { title: 'Service Fee', totalPrice: 0 },
          ],
        },
        addons: {
          title: 'Addons',
          total: 0,
          subFare: [],
        },
        totalWithotAddons: 0,
        totalAmount: 0,
      };
  
      response.Trips.forEach((trip: any) => {
        const fare = trip.Journey[0].Segments[0].Fares;
        fareSummary.totalAmount += fare.GrossFare;
        fareSummary.totalWithotAddons += fare.GrossFare;
        fareSummary.taxs.total += fare.TotalTax;
        let totalTax = fare.TotalTax;
  
        fare.PTCFare.forEach((ptc: any) => {
          const otherTaxKey = ptc.OTT.split(',');
          const otherTax = ptc.OT.split(',');
          let otherTaxValue = 0;
          let gstValue = 0;
          let UDFValue = 0;
  
          for (let i = 0; i < otherTaxKey.length; i++) {
            switch (otherTaxKey[i]) {
              case 'CGST':
              case 'SGST':
              case 'IGST':
              case 'UGST':
              case 'JN':
                gstValue += +otherTax[i];
                break;
              case 'AAT':
                UDFValue += +otherTax[i];
                break;
              default:
                otherTaxValue += +otherTax[i];
                break;
            }
          }
  
          const paxCount = ptc.PTC === 'ADT' ? adult : ptc.PTC === 'CHD' ? child : ptc.PTC === 'INF' ? infant : 0;
          const find = fareSummary.baseFare.subFare.find((x) => x.id === ptc.PTC);
  
          if (find) {
            find.price = ptc?.Fare || 0;
            find.totalPrice = (ptc?.Fare || 0) * paxCount;
            fareSummary.baseFare.total += find.totalPrice;
          }
  
          const updateTax = (title: string, value: number) => {
            const taxItem = fareSummary.taxs.subFare.find((x) => x.title === title);
            if (taxItem) {
              taxItem.totalPrice += value * paxCount;
              totalTax -= value * paxCount;
            }
          };
  
          if (ptc.YQ > 0) updateTax('Fuel Surcharge', ptc.YQ);
          if (ptc.PSF > 0) updateTax('Psgr Svs. Fee', ptc.PSF);
          if (ptc.UD > 0) updateTax('User Dev. Fee', ptc.UD);
          if (ptc.TransactionFee > 0) updateTax('Service Fee', ptc.TransactionFee);
  
          if (ptc.K3 > 0 || (ptc.JN > 0 && gstValue > 0)) {
            const tot = (gstValue + ptc.K3 + (ptc.JN ? ptc.JN : 0)) * paxCount;
            updateTax('K3 Tax', tot);
          }
  
          if (ptc.VATonServiceCharge > 0 || ptc.VATonTransactionFee > 0 || ptc.K7 > 0) {
            const tot = (ptc.VATonServiceCharge + ptc.VATonTransactionFee + ptc.K7) * paxCount;
            updateTax('Total VAT', tot);
          }
  
          if (UDFValue > 0) updateTax('User Dev. Fee – Arrival(UDF)', UDFValue);
        });
  
        const valueTax = fareSummary.taxs.subFare.find((x) => x.title === 'Airline Misc');
        if (valueTax) {
          valueTax.totalPrice += totalTax;
        }
      });
  
      return fareSummary;
    }
  
    addFareSummaries(fare: any[]): any | undefined {
      if (fare.length === 0) return;
  
      const fareSummaryValue = { ...fare[0] };
  
      for (let i = 1; i < fare.length; i++) {
        const currentFare = fare[i];
  
        fareSummaryValue.baseFare.total += currentFare.baseFare.total;
        fareSummaryValue.taxs.total += currentFare.taxs.total;
        fareSummaryValue.totalAmount += currentFare.totalAmount;
        fareSummaryValue.totalWithotAddons += currentFare.totalWithotAddons;
  
        for (let k = 0; k < currentFare.baseFare.subFare.length; k++) {
          fareSummaryValue.baseFare.subFare[k].price += currentFare.baseFare.subFare[k].price;
          fareSummaryValue.baseFare.subFare[k].totalPrice += currentFare.baseFare.subFare[k].totalPrice;
        }
  
        for (let k = 0; k < currentFare.taxs.subFare.length; k++) {
          fareSummaryValue.taxs.subFare[k].totalPrice += currentFare.taxs.subFare[k].totalPrice;
        }
      }
  
      return fareSummaryValue;
    }
  }
  
  export default FlightReviewPageHelper;
  