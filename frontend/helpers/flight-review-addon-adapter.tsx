export class FlightSSRAddonsHelper {

    ssrData: any[] = [
        {
            type: "1",
            icon: "fa fa-cutlery",
            title: "Meals",
            totalPrice: 0,
            selectedCount: 0,
            params:'meals',
            image: 'meals-ssr.jpg',
            values: []
        },
        {
            type: "2",
            icon: "fa fa-suitcase",
            title: "Baggage",
            totalPrice: 0,
            selectedCount: 0,
            params:'baggage',
            image: 'ssr-baggage.jpg',
            values: []
        },
        {
            type: "3",
            icon: "fa fa-futbol-o",
            title: "Sports Equip",
            totalPrice: 0,
            selectedCount: 0,
            params:'sports',
            image: 'sports-ssr.jpg',
            values: []
        },
        {
            type: "7",
            icon: "fa fa-briefcase",
            title: "Priority Baggage",
            totalPrice: 0,
            selectedCount: 0,
            params:'prioritybaggage',
            image: 'priority-check-in-baggage-ssr.png',
            values: []
        },
        {
            type: "8",
            icon: "fa fa-ticket",
            title: "Priority Checkin",
            totalPrice: 0,
            selectedCount: 0,
            params:'prioritycheckin',
            image: 'priority-check-in-baggage-ssr.png',
            values: []
        },
        {
            type: "9",
            icon: "fa fa-wheelchair",
            title: "Seat",
            totalPrice: 0,
            params:'seat',
            selectedCount: 0,
            image: '',
            values: []
        }
    ];

    addonsDataSet(addons: any) {
        if (typeof (localStorage) !== "undefined" && localStorage.getItem("webSettings") != undefined) {
            const Settings = JSON.parse(localStorage.getItem("webSettings")!)
            addons.ssr.forEach((x: any) => {
                const tripData = addons.trip.find((tr: any) => tr.TUI === x.TUI);
                if (tripData) {
                    x.Trips.forEach((y: any, tripId: number) => {
                        if (y?.Journey && y?.Journey[0] && y?.Journey[0]?.Segments.length > 0) {
                            y.Journey[0].Segments.forEach((seg: any, segId: number) => {
                                this.ssrData.forEach((sd: any, i: number) => {
                                    if(this.isAvailableWebSettings(Settings,sd.params,tripData.Trips[tripId].Journey[0].Segments[segId].Flight.MAC,'D')){
                                        if (sd.type === '1') {
                                            const values = seg.SSR.filter((m: any) => m.Type === sd.type);
                                            if (values.length > 0) {
                                                values.forEach((v: any) => {
                                                    v.selectedCount = 0;
                                                })
                                                const ssrValue = {
                                                    from: tripData.Trips[tripId].Journey[0].Segments[segId].Flight.DepAirportName.split('|')[1],
                                                    to: tripData.Trips[tripId].Journey[0].Segments[segId].Flight.ArrAirportName.split('|')[1],
                                                    selectedCount: 0,
                                                    selectedPrice: 0,
                                                    adult: tripData.ADT,
                                                    child: tripData.CHD,
                                                    displayDatas: values,
                                                    extend: false,
                                                    xValueTop: 0,
                                                    yValueTop: 0
                                                }
                                                this.ssrData[i].values.push(ssrValue)
                                            }
                                        } else {
                                            if (segId == 0) {
                                                const values = seg.SSR.filter((m: any) => m.Type === sd.type);
                                                if (values.length > 0) {
                                                    values.forEach((v: any) => {
                                                        v.selectedCount = 0;
                                                    })
                                                    const ssrValue = {
                                                        from: tripData.Trips[tripId].Journey[0].from.split('|')[1],
                                                        to: tripData.Trips[tripId].Journey[0].to.split('|')[1],
                                                        selectedCount: 0,
                                                        selectedPrice: 0,
                                                        adult: tripData.ADT,
                                                        child: tripData.CHD,
                                                        displayDatas: values,
                                                        extend: false,
                                                        xValueTop: 0,
                                                        yValueTop: 0
                                                    }
                                                    this.ssrData[i].values.push(ssrValue)
                                                }
                                            }
                                        }
                                    }
                                })
                            })
                        }
                    })
                }
            });

            addons.seat.forEach((x: any) => {
                const tripData = addons.trip.find((tr: any) => tr.TUI === x.TUI);
                if (tripData) {
                    x.Trips.forEach((y: any, tripId: number) => {
                        if (y?.Journey && y?.Journey[0] && y?.Journey[0]?.Segments.length > 0) {
                            y.Journey[0].Segments.forEach((seg: any, segId: number) => {
                                if(this.isAvailableWebSettings(Settings,'seat',tripData.Trips[tripId].Journey[0].Segments[segId].Flight.MAC,'D')){
                                    let xValueTop = 0;
                                    let yValueTop = 0;
    
                                    let xValueMin = 100;
                                    let yValueMin = 100;
    
                                    seg.Seats.forEach((s: any) => {
                                        if (+s.XValue > xValueTop) {
                                            xValueTop = +s.XValue;
                                        }
                                        if (+s.YValue > yValueTop) {
                                            yValueTop = +s.YValue;
                                        }
    
                                        if (+s.XValue < xValueMin) {
                                            xValueMin = +s.XValue;
                                        }
                                        if (+s.YValue < yValueMin) {
                                            yValueMin = +s.YValue;
                                        }
                                    });
    
                                    xValueMin -= 1;
                                    yValueMin -= 1;
                                    xValueTop -= xValueMin;
                                    yValueTop -= yValueMin;
                                    seg.Seats.forEach((s: any) => {
                                        s.select = false;
                                        s.displayXValue = s.XValue - xValueMin;
                                        s.displayYValue = s.YValue - yValueMin;
                                    })
    
                                    const indx = this.ssrData.findIndex(sd => sd.type == '9');
                                    const ssrValue = {
                                        from: tripData.Trips[tripId].Journey[0].Segments[segId].Flight.DepAirportName.split('|')[1],
                                        to: tripData.Trips[tripId].Journey[0].Segments[segId].Flight.ArrAirportName.split('|')[1],
                                        selectedCount: 0,
                                        selectedPrice: 0,
                                        adult: tripData.ADT,
                                        child: tripData.CHD,
                                        displayDatas: seg.Seats,
                                        extend: false,
                                        xValueTop: xValueTop,
                                        yValueTop: yValueTop
                                    }
                                    this.ssrData[indx].values.push(ssrValue)
                                }
                                
                            })
                        }
                    })
                }
            })

            return this.ssrData;
        } else {
            return this.ssrData;
        }
    }

    isAvailableWebSettings(Settings: any, params: string, MAC: string, sectType: string) {
        let isEnabled: boolean = false;
        let channelCode;
        if (params == "SSR") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSSRDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSSRInt");
            }
        } else if (params == "baggage") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowBaggageDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowBaggageInt");
            }
        }
        else if (params == "sports") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSportsDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSportsInt");
            }
        }
        else if (params == "meals") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowMealsDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowMealsInt");
            }
        } else if (params == "prioritybaggage") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowBaggageOutFirstDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowBaggageOutFirstInt");
            }
        }
        else if (params == "prioritycheckin") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowPriorityCheckinDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowPriorityCheckinInt");
            }
        }else if (params == "seat") {
            if (sectType != "I") {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSeatLayoutDom");
            } else {
                channelCode = Settings.filter((item: any) => item.Key == "ShowSeatLayoutInt");
            }
        }
        if (channelCode.length) {
            channelCode[0].Value.split(',').forEach((code: string) => {
                if (code == MAC) {
                    isEnabled = true;
                }
            })
            return isEnabled;
        }else{
            return false;
        }
    }

    isCheckFlightSeatApiCall(sectType:string,MAC:string){
        let isEnabled = false;
        let channelCode;
        if (typeof (localStorage) !== "undefined" && localStorage.getItem("webSettings") != undefined) {
            const Settings:any = JSON.parse(localStorage.getItem("webSettings")!);
            if (sectType != "I") {
                channelCode = Settings.filter((item:any) => item.Key == "ShowSeatLayoutDom");
            } else {
                channelCode = Settings.filter((item:any) => item.Key == "ShowSeatLayoutDom");
            }
            if (channelCode.length) {
                channelCode[0].Value.split(',').forEach((code: string) => {
                    if (code == MAC) {
                        isEnabled = true;
                    }
                })
                return isEnabled;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    isCheckSSRApiCall(sectType:string,MAC:string){
        let isEnabled = false;
        let channelCode;
        
        if (typeof (localStorage) !== "undefined" && localStorage.getItem("webSettings") != undefined) {
            const Settings:any = JSON.parse(localStorage.getItem("webSettings")!);
            
            if (sectType != "I") {
                channelCode = Settings.filter((item:any) => item.Key == "ShowSSRDom");
            } else {
                channelCode = Settings.filter((item:any) => item.Key == "ShowSSRInt");
            }
            if (channelCode.length) {
                channelCode[0].Value.split(',').forEach((code: string) => {
                    if (code == MAC) {
                        isEnabled = true;
                    }
                })
                return isEnabled;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

}