import { FareSummaryBaseFare, FareSummaryDetails, FlightBaggageInfo, FlightInfoMap, FlightInfoTrip, FlightSsrTrip } from "../models/flight-info-ssr-model";
import  moment from 'moment';

export class FlightInfoFareRuleHelper {

    setTripSegments(response: FlightInfoTrip[]) {
        let trips: FlightInfoMap[] = [];
        response.forEach((x:any) => {
            let trip: FlightInfoMap = {
                from: x.Journey[0].Segments[0].Flight.DepartureCode,
                to: x.Journey[0].Segments[(x.Journey[0].Segments.length - 1)].Flight.ArrivalCode,
                Notices: x.Journey[0].Notices,
                segments: []
            }
            x.Journey[0].Segments.forEach((y:any, i: number) => {
                if (y.Flight.Hops && y.Flight.Hops.length > 0) {
                    y.Flight.Hops.forEach((hops:any, j: number) => {
                        if (y.Flight.Hops?.length == 1) {
                            if (i == 0) {
                                const value = {
                                    isHops: false,
                                    isConnection: false,
                                    connectionAirport: '',
                                    connectionCode: '',
                                    connectionTime: '',
                                    Mac: this.getMainProvider(y.Flight.MAC),
                                    Airline: y.Flight.Airline.split('|')[1],
                                    FlightNo: y.Flight.FlightNo,
                                    AirCraft: y.Flight.AirCraft,
                                    Cabin: this.setCabinClass(y.Flight.Cabin),
                                    DepartureTime: y.Flight.DepartureTime,
                                    DepartureCode: y.Flight.DepartureCode,
                                    DepAirportName: y.Flight.DepAirportName,
                                    DepartureTerminal: y.Flight.DepartureTerminal,
                                    Duration: hops.ArrivalDuration,
                                    ArrivalTime: hops.ArrivalTime,
                                    ArrivalCode: hops.ArrivalCode,
                                    ArrAirportName: hops.ArrAirportName,
                                    ArrivalTerminal: '',
                                }
                                trip.segments.push(value);
                            } else {
                                const value = {
                                    isHops: false,
                                    isConnection: true,
                                    connectionAirport: y.Flight.DepAirportName,
                                    connectionCode: y.Flight.DepartureCode,
                                    connectionTime: this.setLayoutTime(y.Flight.DepartureTime, x.Journey[0].Segments[i - 1].Flight.ArrivalTime),
                                    Mac: this.getMainProvider(y.Flight.MAC),
                                    Airline: y.Flight.Airline.split('|')[1],
                                    FlightNo: y.Flight.FlightNo,
                                    AirCraft: y.Flight.AirCraft,
                                    Cabin: this.setCabinClass(y.Flight.Cabin),
                                    DepartureTime: y.Flight.DepartureTime,
                                    DepartureCode: y.Flight.DepartureCode,
                                    DepAirportName: y.Flight.DepAirportName,
                                    DepartureTerminal: y.Flight.DepartureTerminal,
                                    Duration: hops.ArrivalDuration,
                                    ArrivalTime: hops.ArrivalTime,
                                    ArrivalCode: hops.ArrivalCode,
                                    ArrAirportName: hops.ArrAirportName,
                                    ArrivalTerminal: '',
                                }
                                trip.segments.push(value);
                            }
                            const value2 = {
                                isHops: true,
                                isConnection: false,
                                connectionAirport: hops.ArrAirportName,
                                connectionCode: hops.ArrivalCode,
                                connectionTime: hops.Duration,
                                Mac: this.getMainProvider(y.Flight.MAC),
                                Airline: y.Flight.Airline.split('|')[1],
                                FlightNo: y.Flight.FlightNo,
                                AirCraft: y.Flight.AirCraft,
                                Cabin: this.setCabinClass(y.Flight.Cabin),
                                DepartureTime: hops.DepartureTime,
                                DepartureCode: hops.ArrivalCode,
                                DepAirportName: hops.ArrAirportName,
                                DepartureTerminal: '',
                                Duration: hops.DepartureDuration,
                                ArrivalTime: y.Flight.ArrivalTime,
                                ArrivalCode: y.Flight.ArrivalCode,
                                ArrAirportName: y.Flight.ArrAirportName,
                                ArrivalTerminal: y.Flight.ArrivalTerminal,
                            }
                            trip.segments.push(value2);
                        } else {
                            if (j == 0) {
                                const value = {
                                    isHops: false,
                                    isConnection: false,
                                    connectionAirport: '',
                                    connectionCode: '',
                                    connectionTime: '',
                                    Mac: this.getMainProvider(y.Flight.MAC),
                                    Airline: y.Flight.Airline.split('|')[1],
                                    FlightNo: y.Flight.FlightNo,
                                    AirCraft: y.Flight.AirCraft,
                                    Cabin: this.setCabinClass(y.Flight.Cabin),
                                    DepartureTime: y.Flight.DepartureTime,
                                    DepartureCode: y.Flight.DepartureCode,
                                    DepAirportName: y.Flight.DepAirportName,
                                    DepartureTerminal: y.Flight.DepartureTerminal,
                                    Duration: hops.ArrivalDuration,
                                    ArrivalTime: hops.ArrivalTime,
                                    ArrivalCode: hops.ArrivalCode,
                                    ArrAirportName: hops.ArrAirportName,
                                    ArrivalTerminal: '',
                                }
                                trip.segments.push(value);
                                if (y.Flight.Hops && j + 1 < y.Flight.Hops.length) {
                                    const value2 = {
                                        isHops: true,
                                        isConnection: false,
                                        connectionAirport: hops.ArrAirportName,
                                        connectionCode: hops.ArrivalCode,
                                        connectionTime: hops.Duration,
                                        Mac: this.getMainProvider(y.Flight.MAC),
                                        Airline: y.Flight.Airline.split('|')[1],
                                        FlightNo: y.Flight.FlightNo,
                                        AirCraft: y.Flight.AirCraft,
                                        Cabin: this.setCabinClass(y.Flight.Cabin),
                                        DepartureTime: hops.DepartureTime,
                                        DepartureCode: hops.ArrivalCode,
                                        DepAirportName: hops.ArrAirportName,
                                        DepartureTerminal: '',
                                        Duration: hops.DepartureDuration,
                                        ArrivalTime: y.Flight.Hops[j + 1].ArrivalTime,
                                        ArrivalCode: y.Flight.Hops[j + 1].ArrivalCode,
                                        ArrAirportName: y.Flight.Hops[j + 1].ArrAirportName,
                                        ArrivalTerminal: '',
                                    }
                                    trip.segments.push(value2);
                                }
                            } else {
                                if (y.Flight.Hops && j + 1 < y.Flight.Hops.length) {
                                    const value = {
                                        isHops: true,
                                        isConnection: false,
                                        connectionAirport: hops.ArrAirportName,
                                        connectionCode: hops.ArrivalCode,
                                        connectionTime: hops.Duration,
                                        Mac: this.getMainProvider(y.Flight.MAC),
                                        Airline: y.Flight.Airline.split('|')[1],
                                        FlightNo: y.Flight.FlightNo,
                                        AirCraft: y.Flight.AirCraft,
                                        Cabin: this.setCabinClass(y.Flight.Cabin),
                                        DepartureTime: hops.DepartureTime,
                                        DepartureCode: hops.ArrivalCode,
                                        DepAirportName: hops.ArrAirportName,
                                        DepartureTerminal: '',
                                        Duration: hops.DepartureDuration,
                                        ArrivalTime: y.Flight.Hops[j + 1].ArrivalTime,
                                        ArrivalCode: y.Flight.Hops[j + 1].ArrivalCode,
                                        ArrAirportName: y.Flight.Hops[j + 1].ArrAirportName,
                                        ArrivalTerminal: '',
                                    }
                                    trip.segments.push(value);
                                } else {
                                    const value = {
                                        isHops: true,
                                        isConnection: false,
                                        connectionAirport: hops.ArrAirportName,
                                        connectionCode: hops.ArrivalCode,
                                        connectionTime: hops.Duration,
                                        Mac: this.getMainProvider(y.Flight.MAC),
                                        Airline: y.Flight.Airline.split('|')[1],
                                        FlightNo: y.Flight.FlightNo,
                                        AirCraft: y.Flight.AirCraft,
                                        Cabin: this.setCabinClass(y.Flight.Cabin),
                                        DepartureTime: hops.DepartureTime,
                                        DepartureCode: hops.ArrivalCode,
                                        DepAirportName: hops.ArrAirportName,
                                        DepartureTerminal: '',
                                        Duration: hops.DepartureDuration,
                                        ArrivalTime: y.Flight.ArrivalTime,
                                        ArrivalCode: y.Flight.ArrivalCode,
                                        ArrAirportName: y.Flight.ArrAirportName,
                                        ArrivalTerminal: y.Flight.ArrivalTerminal,
                                    }
                                    trip.segments.push(value);
                                }

                            }

                        }
                    })
                } else {
                    if (i == 0) {
                        const value = {
                            isHops: false,
                            isConnection: false,
                            connectionAirport: '',
                            connectionCode: '',
                            connectionTime: '',
                            Mac: this.getMainProvider(y.Flight.MAC),
                            Airline: y.Flight.Airline.split('|')[1],
                            FlightNo: y.Flight.FlightNo,
                            AirCraft: y.Flight.AirCraft,
                            Cabin: this.setCabinClass(y.Flight.Cabin),
                            DepartureTime: y.Flight.DepartureTime,
                            DepartureCode: y.Flight.DepartureCode,
                            DepAirportName: y.Flight.DepAirportName,
                            DepartureTerminal: y.Flight.DepartureTerminal,
                            Duration: y.Flight.Duration,
                            ArrivalTime: y.Flight.ArrivalTime,
                            ArrivalCode: y.Flight.ArrivalCode,
                            ArrAirportName: y.Flight.ArrAirportName,
                            ArrivalTerminal: y.Flight.ArrivalTerminal,
                        }
                        trip.segments.push(value);
                    } else {
                        const value = {
                            isHops: false,
                            isConnection: true,
                            connectionAirport: y.Flight.DepAirportName,
                            connectionCode: y.Flight.DepartureCode,
                            connectionTime: this.setLayoutTime(y.Flight.DepartureTime, x.Journey[0].Segments[i - 1].Flight.ArrivalTime),
                            Mac: this.getMainProvider(y.Flight.MAC),
                            Airline: y.Flight.Airline.split('|')[1],
                            FlightNo: y.Flight.FlightNo,
                            AirCraft: y.Flight.AirCraft,
                            Cabin: this.setCabinClass(y.Flight.Cabin),
                            DepartureTime: y.Flight.DepartureTime,
                            DepartureCode: y.Flight.DepartureCode,
                            DepAirportName: y.Flight.DepAirportName,
                            DepartureTerminal: y.Flight.DepartureTerminal,
                            Duration: y.Flight.Duration,
                            ArrivalTime: y.Flight.ArrivalTime,
                            ArrivalCode: y.Flight.ArrivalCode,
                            ArrAirportName: y.Flight.ArrAirportName,
                            ArrivalTerminal: y.Flight.ArrivalTerminal,
                        }
                        trip.segments.push(value);
                    }
                }


            });

            trips.push(trip);

        });

        return trips;

    }

    setCabinClass(cabin: string) {
        switch (cabin) {
            case 'E':
                return 'Economy';
            case 'PE':
                return 'Premium Economy';
            case 'B':
                return 'Business';
            default:
                return '';
        }
    }

    setLayoutTime(depTime: any, arrTime: any): string {
        var dt1 = moment(arrTime);
        var dt2 = moment(depTime);

        const duration = moment.duration(dt2.diff(dt1));
        const layOverTym = duration.get("hours").toString().padStart(2, '0') + "h " + duration.get("minutes").toString().padStart(2, '0') + "m";

        return layOverTym;
    }

    getMainProvider(mac: string) {
        if (mac && mac.slice(-2) === '6E') {
            return '6E'
        } else if (mac && mac.slice(-2) === 'SG') {
            return 'SG'
        } else if (mac && mac.slice(-2) === 'G8') {
            return 'G8'
        } else if (mac && mac.slice(-2) === 'AK') {
            return 'AK'
        } else if (mac && mac.slice(-2) === 'I5') {
            return 'I5'
        } else {
            return mac
        }
    }

    setFareSummary(response: FlightInfoTrip[], adult: number, child: number, infant: number) {

        const fareSummary: FareSummaryDetails = {
            baseFare: {
                title: 'Base Fare',
                total: 0,
                subFare: [
                    { title: "Adult", price: 0, id: 'ADT', count: adult, totalPrice: 0 },
                    { title: "Child", price: 0, id: 'CHD', count: child, totalPrice: 0 },
                    { title: "Infant", price: 0, id: 'INF', count: infant, totalPrice: 0 }
                ]
            },
            taxs: {
                title: 'Tax & Charges',
                total: 0,
                subFare: [
                    { title: 'Fuel Surcharge', totalPrice: 0 },
                    { title: 'Psgr Svs. Fee', totalPrice: 0 },
                    { title: 'User Dev. Fee', totalPrice: 0 },
                    { title: 'K3 Tax', totalPrice: 0 },
                    { title: 'Airline Misc', totalPrice: 0 },
                    { title: 'Total VAT', totalPrice: 0 },
                    { title: 'User Dev. Fee – Arrival(UDF)', totalPrice: 0 },
                    { title: 'Service Fee', totalPrice: 0 }
                ]
            },
            addons: {
                title: 'Addons',
                total: 0,
                subFare: []
            },
            totalWithotAddons: 0,
            totalAmount: 0
        };

        response.forEach((trip:any) => {
            const fare = trip.Journey[0].Segments[0].Fares;
            fareSummary.totalAmount += fare.GrossFare;
            fareSummary.totalWithotAddons += fare.GrossFare;
            fareSummary.taxs.total += fare.TotalTax;
            let totalTax = fare.TotalTax;

            fare.PTCFare.forEach((ptc: any) => {
                const otherTaxKey = ptc.OTT ? ptc.OTT.split(',') : [];
                const otherTax = ptc.OT ? ptc.OT.split(',') : [];
                let otherTaxValue = 0;
                let gstValue = 0;
                let UDFValue = 0;

                for (let i = 0; i < otherTaxKey.length; i++) {
                    switch (otherTaxKey[i]) {
                        case 'CGST':
                        case 'SGST':
                        case 'IGST':
                        case 'UGST':
                        case 'JN':
                            gstValue += +otherTax[i];
                            break;
                        case 'AAT':
                            UDFValue += +otherTax[i];
                            break;
                        default:
                            otherTaxValue += +otherTax[i];
                            break;
                    }
                }

                const paxCount = ptc.PTC === 'ADT' ? adult : ptc.PTC === 'CHD' ? child : ptc.PTC === 'INF' ? infant : 0;
                const find = fareSummary.baseFare.subFare.find((x) => x.id === ptc.PTC);

                if (find) {
                    find.price = ptc?.Fare || 0;
                    find.totalPrice = (ptc?.Fare || 0) * paxCount;
                    fareSummary.baseFare.total += find.totalPrice;
                }

                function updateTax(title: any, value: any) {
                    const taxItem = fareSummary.taxs.subFare.find((x) => x.title === title);
                    if (taxItem) {
                        taxItem.totalPrice += value * paxCount;
                        totalTax -= value * paxCount;
                    }
                }

                if (ptc.YQ > 0) {
                    updateTax('Fuel Surcharge', ptc.YQ);
                }
                if (ptc.PSF > 0) {
                    updateTax('Psgr Svs. Fee', ptc.PSF);
                }
                if (ptc.UD > 0) {
                    updateTax('User Dev. Fee', ptc.UD);
                }
                if (ptc.TransactionFee > 0) {
                    updateTax('Service Fee', ptc.TransactionFee);
                }

                if (ptc.K3 > 0 || (ptc.JN && ptc.JN > 0 && gstValue > 0)) {
                    const tot = (gstValue + ptc.K3 + (ptc.JN ? ptc.JN : 0)) * paxCount;
                    updateTax('K3 Tax', tot);
                }

                if (ptc.VATonServiceCharge > 0 || ptc.VATonTransactionFee > 0 || ptc.K7 > 0) {
                    const tot = (ptc.VATonServiceCharge + ptc.VATonTransactionFee + ptc.K7) * paxCount;
                    updateTax('Total VAT', tot);
                }

                if (UDFValue > 0) {
                    updateTax('User Dev. Fee – Arrival(UDF)', UDFValue);
                }
            });

            const valueTax = fareSummary.taxs.subFare.find((x) => x.title === 'Airline Misc');
            if (valueTax) {
                valueTax.totalPrice += totalTax;
            }
        });

        return fareSummary;
    }

    addFareSummaries(fare: FareSummaryDetails[]):any{
        if (fare.length === 0) {
            return;
        }

        let fareSummaryValue = { ...fare[0] };

        for (let i = 1; i < fare.length; i++) {
            const currentFare = fare[i];

            // fareSummaryValue.baseFare.total += currentFare.baseFare.total;
            // fareSummaryValue.taxs.total += currentFare.taxs.total;
            // fareSummaryValue.totalAmount += currentFare.totalAmount;
            // fareSummaryValue.totalWithotAddons += currentFare.totalWithotAddons;

            // for (let k = 0; k < currentFare.baseFare.subFare.length; k++) {
            //     fareSummaryValue.baseFare.subFare[k].price += currentFare.baseFare.subFare[k].price;
            //     fareSummaryValue.baseFare.subFare[k].totalPrice += currentFare.baseFare.subFare[k].totalPrice;
            // }

            // for (let k = 0; k < currentFare.taxs.subFare.length; k++) {
            //     fareSummaryValue.taxs.subFare[k].totalPrice += currentFare.taxs.subFare[k].totalPrice;
            // }
        }

        return fareSummaryValue;
    }

    setBaggageValueFromSSRResponse(trips: FlightSsrTrip[]) {
        let flightBaggae: FlightBaggageInfo[] = [];
        trips.forEach(x => {
            let bagagge: FlightBaggageInfo = {
                from: x.From,
                to: x.To,
                checkin: {
                    adult: '',
                    child: '',
                    infant: ''
                },
                cabin: {
                    adult: '',
                    child: '',
                    infant: ''
                }
            }
            if (x.Journey && x.Journey[0] && x.Journey[0].Segments && x.Journey[0].Segments[0] && x.Journey[0].Segments[0].SSR) {
                x.Journey[0].Segments[0].SSR.forEach(sr => {
                    if (sr.Code == 'BAG') {
                        if (sr.PTC == 'ADT') {
                            bagagge.checkin.adult = sr.Description.split(',')[0]!;
                            bagagge.cabin.adult = sr.Description.split(',')[1]!;
                        }
                        if (sr.PTC == 'CHD') {
                            bagagge.checkin.child = sr.Description.split(',')[0]!;
                            bagagge.cabin.child = sr.Description.split(',')[1]!;
                        }
                        if (sr.PTC == 'INF') {
                            bagagge.checkin.infant = sr.Description.split(',')[0]!;
                            bagagge.cabin.infant = sr.Description.split(',')[1]!;
                        }
                    }
                });
                flightBaggae.push(bagagge);
            }
        });
        return flightBaggae;
    }

}