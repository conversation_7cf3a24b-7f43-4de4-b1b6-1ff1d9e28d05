import { CombaignTrip, CombaignTripMap, CombaignTripMapTrip, Journey } from "models/flight-list-response-model";


export class FlightListHelper {

    oneWayJournyKeySet(list: Journey[]): Journey[] {
        list?.forEach((jrny) => {
            jrny.CreatedJourneyKey = this.getMainProvider(jrny.MAC) + "," + jrny.FlightNo + "," + this.getMainProvider(jrny.VAC) + "," + jrny.To + "," + jrny.From + "," + jrny.ArrivalTime + "," + jrny.DepartureTime;
            jrny.isDisplay = true;
            jrny.isShowFareType = false;
            jrny.isVisible = true;
            jrny.SubFlights = [];
            jrny.MAC = this.getMainProvider(jrny.MAC);
            jrny.VAC = this.getMainProvider(jrny.VAC);
            jrny.OAC = this.getMainProvider(jrny.OAC);
            jrny.isSelect = false;
            // if (environment.NDCProviders.includes(jrny.Provider)) {
            //     jrny.FCType = (jrny.FCType ? jrny.FCType : "Others");
            // } else if (environment.SOTOProviders.includes(jrny.Provider)) {
            //     jrny.FCType = "Soto Fares";
            // } else {
            //     jrny.FCType = jrny.FCType ? jrny.FCType : "Others";
            // }
            if (jrny.Connections && jrny.Connections?.length > 0) {
                const count = jrny.Connections?.length;
                jrny.ConnectionText = count + ' ' + (count > 1 ? 'stops' : 'stop') + ', via ' + jrny.Connections.map(item => item.Airport).join(',');
            } else {
                jrny.ConnectionText = 'Non Stop';
            }
        })
        return list;
    }

    combineSameFlight(list: Journey[]): Journey[] {
        // Group flights by CreatedJourneyKey
        const groupedFlights = new Map<string, Journey[]>();

        // First, group all flights by their CreatedJourneyKey
        list?.forEach(journey => {
            const journeyKey = journey.CreatedJourneyKey || '';

            if (!groupedFlights.has(journeyKey)) {
                groupedFlights.set(journeyKey, []);
            }
            groupedFlights.get(journeyKey)!.push(journey);
        });

        // Process each group
        groupedFlights.forEach((journeys) => {
            if (journeys.length > 1) {
                // Sort by GrossFare (ascending) to find the cheapest
                journeys.sort((a, b) => a.GrossFare - b.GrossFare);

                // Keep the cheapest flight visible, hide others
                const cheapestFlight = journeys[0];
                if (cheapestFlight) {
                    cheapestFlight.isDisplay = true;
                    cheapestFlight.SubFlights = [];

                    // Add all fare types to SubFlights, avoiding duplicates
                    const addedFareTypes = new Set<string>();

                    journeys.forEach(flight => {
                        if (flight && cheapestFlight) {
                            // Create unique key for fare type and price combination
                            const fareTypeKey = `${flight.FCType}_${flight.GrossFare}_${flight.Index}`;

                            if (!addedFareTypes.has(fareTypeKey)) {
                                addedFareTypes.add(fareTypeKey);

                                cheapestFlight.SubFlights!.push({
                                    'Amount': flight.GrossFare,
                                    'Index': flight.Index,
                                    'ChannelCode': flight.ChannelCode,
                                    'Baggage': flight.Inclusions?.Baggage && flight.Inclusions?.Baggage !== '' ? flight.Inclusions?.Baggage : 'Chargeable',
                                    'SeatSelection': flight.Inclusions?.PieceDescription && flight.Inclusions?.PieceDescription !== '' ? flight.Inclusions?.PieceDescription : 'Chargeable',
                                    'Meal': flight.Inclusions?.Meals && flight.Inclusions?.Meals !== '' ? flight.Inclusions?.Meals : 'Chargeable',
                                    'Refund': flight.Refundable,
                                    'FCType': flight.FCType,
                                    'isSelect': false
                                });
                            }

                            // Hide all flights except the cheapest one
                            if (flight !== cheapestFlight) {
                                flight.isDisplay = false;
                            }
                        }
                    });
                }
            } else if (journeys.length === 1) {
                // Single flight case
                const flight = journeys[0];
                if (flight) {
                    flight.isDisplay = true;
                    flight.SubFlights = [{
                        'Amount': flight.GrossFare,
                        'Index': flight.Index,
                        'ChannelCode': flight.ChannelCode,
                        'Baggage': flight.Inclusions?.Baggage && flight.Inclusions?.Baggage !== '' ? flight.Inclusions?.Baggage : 'Chargeable',
                        'SeatSelection': flight.Inclusions?.PieceDescription && flight.Inclusions?.PieceDescription !== '' ? flight.Inclusions?.PieceDescription : 'Chargeable',
                        'Meal': flight.Inclusions?.Meals && flight.Inclusions?.Meals !== '' ? flight.Inclusions?.Meals : 'Chargeable',
                        'Refund': flight.Refundable,
                        'FCType': flight.FCType,
                        'isSelect': false
                    }];
                }
            }
        });

        return list;
    }


    // combineSameFlight(list: Journey[]): Journey[] {
    //     // Group flights by Index
    //     const groupedFlights = new Map<string, Journey[]>();

    //     // First, group all flights by their Index
    //     list?.forEach(journey => {
    //         const journeyKey = journey.Index;

    //         if (!groupedFlights.has(journeyKey)) {
    //             groupedFlights.set(journeyKey, []);
    //         }
    //         // Safe to use non-null assertion since we just set it
    //         groupedFlights.get(journeyKey)!.push(journey);
    //     });

    //     // Process each group
    //     groupedFlights.forEach((journeys) => {
    //         if (journeys.length > 1) {
    //             // Sort by GrossFare (ascending)
    //             journeys.sort((a, b) => a.GrossFare - b.GrossFare);

    //             // Keep the cheapest flight visible, hide others
    //             const cheapestFlight = journeys[0];
    //             if (cheapestFlight) {
    //                 cheapestFlight.isDisplay = true;
    //                 cheapestFlight.SubFlights = [];

    //                 // Add unique fare types to SubFlights, avoiding duplicates
    //                 const addedFareTypes = new Set<string>();

    //                 journeys.forEach(flight => {
    //                     if (flight && cheapestFlight) {
    //                         // Make sure cheapestFlight.SubFlights is initialized
    //                         if (!cheapestFlight.SubFlights) {
    //                             cheapestFlight.SubFlights = [];
    //                         }

    //                         // Only add if this FCType hasn't been added yet
    //                         const fareTypeKey = `${flight.FCType}_${flight.GrossFare}`;
    //                         if (!addedFareTypes.has(fareTypeKey)) {
    //                             addedFareTypes.add(fareTypeKey);

    //                             cheapestFlight.SubFlights.push({
    //                                 'Amount': flight.GrossFare,
    //                                 'Index': flight.Index,
    //                                 'ChannelCode': flight.ChannelCode,
    //                                 'Baggage': flight.Inclusions?.Baggage && flight.Inclusions?.Baggage !== '' ? flight.Inclusions?.Baggage : 'Chargeable',
    //                                 'SeatSelection': flight.Inclusions?.PieceDescription && flight.Inclusions?.PieceDescription !== '' ? flight.Inclusions?.PieceDescription : 'Chargeable',
    //                                 'Meal': flight.Inclusions?.Meals && flight.Inclusions?.Meals !== '' ? flight.Inclusions?.Meals : 'Chargeable',
    //                                 'Refund': flight.Refundable,
    //                                 'FCType': flight.FCType,
    //                                 'isSelect': false
    //                             });
    //                         }

    //                         // Hide all flights except the cheapest one
    //                         if (flight !== cheapestFlight) {
    //                             flight.isDisplay = false;
    //                         }
    //                     }
    //                 });
    //             }
    //         } else if (journeys.length === 1) {
    //             // Single flight case
    //             const flight = journeys[0];
    //             if (flight) {
    //                 flight.SubFlights = [{
    //                     'Amount': flight.GrossFare,
    //                     'Index': flight.Index,
    //                     'ChannelCode': flight.ChannelCode,
    //                     'Baggage': flight.Inclusions?.Baggage && flight.Inclusions?.Baggage !== '' ? flight.Inclusions?.Baggage : 'Chargeable',
    //                     'SeatSelection': flight.Inclusions?.PieceDescription && flight.Inclusions?.PieceDescription !== '' ? flight.Inclusions?.PieceDescription : 'Chargeable',
    //                     'Meal': flight.Inclusions?.Meals && flight.Inclusions?.Meals !== '' ? flight.Inclusions?.Meals : 'Chargeable',
    //                     'Refund': flight.Refundable,
    //                     'FCType': flight.FCType,
    //                     'isSelect': false
    //                 }];
    //             }
    //         }
    //     });

    //     return list;
    // }

    getMainProvider(mac: string) {
        if (mac && mac.slice(-2) === '6E') {
            return '6E'
        } else if (mac && mac.slice(-2) === 'SG') {
            return 'SG'
        } else if (mac && mac.slice(-2) === 'G8') {
            return 'G8'
        } else if (mac && mac.slice(-2) === 'AK') {
            return 'AK'
        } else if (mac && mac.slice(-2) === 'I5') {
            return 'I5'
        } else {
            return mac
        }
    }

    setCombaignViewResponse(trip: CombaignTrip[]) {
        const tripData = trip[0];

        const returnData = tripData!.Journey.map(x => x.ReturnIdentifier)

        const uniqueIdentifireList = Array.from(new Set(returnData));

        let listTrip: CombaignTripMap[] = []

        uniqueIdentifireList.forEach((x) => {
            let value: CombaignTripMap = {
                ReturnIdentifier: x || 0,
                total: 0,
                trips: []
            }
            trip.forEach((trips: CombaignTrip) => {
                let filterData = trips.Journey.filter(f => f.ReturnIdentifier == x && f.Seats != 0);
                if (filterData && filterData.length > 0) {
                    filterData![0]!.isSelect = true;
                    value.total = value.total + filterData![0]!.GrossFare;
                    let tripValue: CombaignTripMapTrip = {
                        from: trips!.Journey![0]!.From || '',
                        to: trips!.Journey[0]!.To || '',
                        journey: filterData
                    }
                    value.trips.push(tripValue)
                }
            });
            if (value.trips.every((item) => item.journey.length !== 0)) {
                listTrip.push(value);
            }
        })

        return listTrip;
    }

}
