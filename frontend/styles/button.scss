@use './variable.scss' as *;
.out-line-button{
    margin-top: 20px;
    background-color: #f8f9fa;
    border: 1px solid $button_color;
    color: $button_color;
    padding: 5px 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    &:hover{
        background-color: $button_color;
        box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
        color: white;
    }
    &:active {
        transform: translateY(0);
        box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    }
}
.mobile-filter-div {
    position: fixed;
    left: 10px;
    bottom: 75px;
    z-index: 1;
    cursor: pointer;
    background-color: $primary_color;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
    animation: rotate-shake 10s ease-in-out infinite;
    .maticon {
      font-size: 25px;
    }
    @keyframes rotate-shake {
        60% {
          transform: rotate(0deg); /* Return to center */
        }
        100% {
          transform: rotate(360deg); /* Full rotation */
        }
      }
}


.close-bttn-container{
  position: fixed;
  height: 50px;
  width: 100%;
  background-color: $primary_color;
  top: 0px;
  z-index: 8;
  display: flex;
  align-items: center;
  justify-content: end;
  padding: 25px;
  .close-bttn{
      height: 35px;
      width: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: white;
      border-radius: 10px;
      border: 1px solid $primary_color;
      color: $button_color;
      font-size: 30px;
      cursor: pointer;
      transition: 0.2s ease-in-out;
      &:hover{
          transform: scale(1.1);
      }
  }
}

.button-container{
  width: 100%;
  display: flex;
  justify-content: end;
  padding: 25px;
}

