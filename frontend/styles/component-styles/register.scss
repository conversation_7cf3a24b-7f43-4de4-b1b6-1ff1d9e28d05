@use 'sass:color';
@use '../../styles/variable.scss' as *;

.container-signup {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f4f4f4;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 9999;
    padding: 20px;
    box-sizing: border-box;

    @media screen and (max-width: $isMobile) {
      padding: 10px;
      align-items: flex-start;
      padding-top: 20px;
    }

    @media screen and (max-width: 480px) {
      padding: 5px;
      padding-top: 10px;
    }
  }

  .signup-container {
    display: flex;
    width: 100%;
    max-width: 900px;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    min-height: 500px;

    @media screen and (max-width: 1024px) {
      width: 90%;
      max-width: 800px;
    }

    @media screen and (max-width: $isMobile) {
      width: 95%;
      flex-direction: column;
      max-width: none;
      min-height: auto;
    }

    @media screen and (max-width: 480px) {
      width: 100%;
      border-radius: 4px;
      margin: 0;
    }
  }
  .left-section {
    flex: 1;
    padding: 40px;
    background-color: $primary_color;
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 300px;

    @media screen and (max-width: 1024px) {
      padding: 30px;
    }

    @media screen and (max-width: $isMobile) {
      padding: 20px;
      min-height: 200px;
      text-align: center;
    }

    @media screen and (max-width: 480px) {
      padding: 15px;
      min-height: 150px;
    }

    h1 {
      font-size: 2.2rem;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media screen and (max-width: $isMobile) {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
      }

      @media screen and (max-width: 480px) {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }
    }

    p {
      font-size: 1rem;
      line-height: 1.5;
      opacity: 0.9;

      @media screen and (max-width: $isMobile) {
        font-size: 0.9rem;
        line-height: 1.4;
      }

      @media screen and (max-width: 480px) {
        font-size: 0.85rem;
        line-height: 1.3;
      }
    }
  }

  .right-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    @media screen and (max-width: 1024px) {
      padding: 30px;
    }

    @media screen and (max-width: $isMobile) {
      padding: 20px;
    }

    @media screen and (max-width: 480px) {
      padding: 15px;
    }
  }
  .signup-form {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;

    @media screen and (max-width: $isMobile) {
      max-width: none;
    }

    h2 {
      margin-bottom: 20px;
      text-align: center;
      font-size: 1.8rem;
      color: $text_color;
      font-weight: 600;

      @media screen and (max-width: $isMobile) {
        font-size: 1.6rem;
        margin-bottom: 15px;
      }

      @media screen and (max-width: 480px) {
        font-size: 1.4rem;
        margin-bottom: 12px;
      }
    }

    input {
      margin-bottom: 15px;
      padding: 14px 16px;
      font-size: 16px;
      border: 1px solid #ddd;
      border-radius: 6px;
      transition: all 0.3s ease;
      box-sizing: border-box;
      width: 100%;

      &:focus {
        outline: none;
        border-color: $primary_color;
        box-shadow: 0 0 0 3px rgba(8, 119, 103, 0.1);
      }

      @media screen and (max-width: $isMobile) {
        padding: 12px 14px;
        font-size: 16px; // Keep 16px to prevent zoom on iOS
        margin-bottom: 12px;
      }

      @media screen and (max-width: 480px) {
        padding: 12px;
        margin-bottom: 10px;
      }

      &::placeholder {
        color: #999;
        font-size: 15px;

        @media screen and (max-width: 480px) {
          font-size: 14px;
        }
      }
    }
  }

  .phone-input {
    display: flex;
    align-items: stretch;
    margin-bottom: 15px;
    width: 100%;

    @media screen and (max-width: $isMobile) {
      margin-bottom: 12px;
    }

    @media screen and (max-width: 480px) {
      margin-bottom: 10px;
    }

    .country-code {
      padding: 14px 12px;
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 6px 0 0 6px;
      font-size: 16px;
      margin-right: -1px;
      margin-bottom: 0;
      color: $text_color;
      font-weight: 500;
      display: flex;
      align-items: center;
      min-width: 60px;
      justify-content: center;

      @media screen and (max-width: $isMobile) {
        padding: 12px 10px;
        min-width: 55px;
      }

      @media screen and (max-width: 480px) {
        padding: 12px 8px;
        min-width: 50px;
        font-size: 15px;
      }
    }

    input {
      flex: 1;
      border-radius: 0 6px 6px 0;
      margin-bottom: 0;
    }
  }
  .terms-checkbox {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 8px;
    font-size: 14px;
    line-height: 1.4;

    @media screen and (max-width: $isMobile) {
      margin-bottom: 15px;
      font-size: 13px;
    }

    @media screen and (max-width: 480px) {
      margin-bottom: 12px;
      font-size: 12px;
      gap: 6px;
    }

    .checkbox {
      margin: 0;
      margin-top: 2px;
      flex-shrink: 0;
      width: 16px;
      height: 16px;

      @media screen and (max-width: 480px) {
        width: 14px;
        height: 14px;
      }
    }

    label {
      color: $text_color;
      cursor: pointer;
      flex: 1;
    }
  }

  .terms-link {
    color: $primary_color;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 500;

    &:hover {
      color: color.scale($primary_color, $lightness: -10%);
    }
  }

  .terms-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    padding: 20px;
    box-sizing: border-box;

    @media screen and (max-width: $isMobile) {
      padding: 10px;
      align-items: flex-start;
      padding-top: 50px;
    }

    @media screen and (max-width: 480px) {
      padding: 5px;
      padding-top: 30px;
    }
  }

  .terms-popup-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 100%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

    @media screen and (max-width: $isMobile) {
      padding: 20px;
      max-height: 85vh;
      border-radius: 6px;
    }

    @media screen and (max-width: 480px) {
      padding: 15px;
      max-height: 90vh;
      border-radius: 4px;
    }

    h2 {
      margin-bottom: 20px;
      font-size: 1.5rem;
      font-weight: 600;
      text-align: center;
      color: $text_color;

      @media screen and (max-width: $isMobile) {
        font-size: 1.3rem;
        margin-bottom: 15px;
      }

      @media screen and (max-width: 480px) {
        font-size: 1.2rem;
        margin-bottom: 12px;
      }
    }

    p {
      margin-bottom: 15px;
      text-align: justify;
      font-size: 14px;
      line-height: 1.5;
      color: $text_color;

      @media screen and (max-width: $isMobile) {
        font-size: 13px;
        margin-bottom: 12px;
      }

      @media screen and (max-width: 480px) {
        font-size: 12px;
        margin-bottom: 10px;
      }
    }

    button {
      padding: 12px 24px;
      background-color: $primary_color;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      transition: background-color 0.3s ease;
      width: 100%;
      margin-top: 10px;

      &:hover {
        background-color: color.scale($primary_color, $lightness: -10%);
      }

      @media screen and (max-width: $isMobile) {
        padding: 10px 20px;
        font-size: 15px;
      }

      @media screen and (max-width: 480px) {
        padding: 10px 16px;
        font-size: 14px;
      }
    }
  }

  // Mobile-responsive OTP styles
  .otp-section {
    .otp-icon {
      width: 60px;
      height: 60px;
      background-color: $primary_color;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 15px;
      font-size: 24px;
      color: white;

      @media screen and (max-width: $isMobile) {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 12px;
      }

      @media screen and (max-width: 480px) {
        width: 45px;
        height: 45px;
        font-size: 18px;
        margin-bottom: 10px;
      }
    }

    .otp-title {
      margin-bottom: 10px;
      font-size: 1.8rem;
      font-weight: 600;
      text-align: center;
      color: $text_color;

      @media screen and (max-width: $isMobile) {
        font-size: 1.5rem;
        margin-bottom: 8px;
      }

      @media screen and (max-width: 480px) {
        font-size: 1.3rem;
        margin-bottom: 6px;
      }
    }

    .otp-description {
      color: #666;
      font-size: 14px;
      text-align: center;
      margin-bottom: 25px;
      line-height: 1.4;

      @media screen and (max-width: $isMobile) {
        font-size: 13px;
        margin-bottom: 20px;
      }

      @media screen and (max-width: 480px) {
        font-size: 12px;
        margin-bottom: 15px;
      }
    }

    .otp-inputs {
      display: flex;
      justify-content: space-between;
      width: 100%;
      max-width: 500px;
      margin: 0 auto 20px auto;
      padding: 0 10px;

      @media screen and (max-width: $isMobile) {
        max-width: 90%;
        margin-bottom: 15px;
        padding: 0 5px;
      }

      @media screen and (max-width: 480px) {
        max-width: 95%;
        margin-bottom: 12px;
        padding: 0;
      }

      @media screen and (max-width: 360px) {
        max-width: 100%;
      }

      input {
        width: 50px;
        height: 50px;
        text-align: center;
        font-size: 20px;
        font-weight: 600;
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        background-color: #fafafa;
        transition: all 0.3s ease;
        margin: 0;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: $primary_color;
          background-color: white;
          box-shadow: 0 0 0 3px rgba(8, 119, 103, 0.1);
          transform: scale(1.05);
        }

        @media screen and (max-width: $isMobile) {
          width: 45px;
          height: 45px;
          font-size: 18px;
          border-radius: 10px;
        }

        @media screen and (max-width: 480px) {
          width: 40px;
          height: 40px;
          font-size: 16px;
          border-radius: 8px;
        }

        @media screen and (max-width: 360px) {
          width: 35px;
          height: 35px;
          font-size: 15px;
          border-radius: 6px;
        }

        @media screen and (max-width: 320px) {
          width: 32px;
          height: 32px;
          font-size: 14px;
          border-radius: 5px;
        }
      }
    }

    .otp-error {
      background-color: #ffebee;
      border: 1px solid #f44336;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 20px;
      text-align: center;

      @media screen and (max-width: $isMobile) {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 6px;
      }

      @media screen and (max-width: 480px) {
        padding: 8px;
        margin-bottom: 12px;
        border-radius: 4px;
      }

      p {
        color: #f44336;
        font-size: 14px;
        margin: 0;

        @media screen and (max-width: $isMobile) {
          font-size: 13px;
        }

        @media screen and (max-width: 480px) {
          font-size: 12px;
        }
      }
    }

    .otp-submit-btn {
      margin-bottom: 15px;
      width: 100%;

      @media screen and (max-width: $isMobile) {
        margin-bottom: 12px;
      }

      @media screen and (max-width: 480px) {
        margin-bottom: 10px;
      }
    }

    .back-to-form-btn {
      background: transparent;
      border: 1px solid $primary_color;
      color: $primary_color;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      width: 100%;

      &:hover {
        background-color: $primary_color;
        color: white;
      }

      @media screen and (max-width: $isMobile) {
        padding: 8px 16px;
        font-size: 13px;
      }

      @media screen and (max-width: 480px) {
        padding: 8px 12px;
        font-size: 12px;
      }
    }
  }

  // Success message styles
  .success-message {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;

    @media screen and (max-width: $isMobile) {
      padding: 12px;
      margin-bottom: 15px;
      border-radius: 6px;
    }

    @media screen and (max-width: 480px) {
      padding: 10px;
      margin-bottom: 12px;
      border-radius: 4px;
    }

    p {
      color: #2e7d32;
      font-size: 14px;
      margin: 0;
      font-weight: 500;

      @media screen and (max-width: $isMobile) {
        font-size: 13px;
      }

      @media screen and (max-width: 480px) {
        font-size: 12px;
      }
    }
  }

  // Toggle button styles
  .mode-toggle-btn {
    background: transparent;
    border: none;
    color: $primary_color;
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    margin-bottom: 20px;
    transition: color 0.3s ease;

    &:hover {
      color: color.scale($primary_color, $lightness: -10%);
    }

    @media screen and (max-width: $isMobile) {
      font-size: 13px;
      margin-bottom: 15px;
    }

    @media screen and (max-width: 480px) {
      font-size: 12px;
      margin-bottom: 12px;
    }
  }