@use 'sass:color';
@use './variable' as *;
@use './component-styles/register.scss';
@use './button.scss';
.dy_primary_bttn {
    padding: 12px 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.2;
    transition-duration: 0.3s;
    background: $button_color;
    color: #fff;
    border-radius: 6px;
    outline: 0;
    border: 1px solid $button_color;
    min-width: 120px;
    cursor: pointer;
    box-sizing: border-box;
    width: 100%;

    @media screen and (max-width: $isMobile) {
        padding: 10px 20px;
        font-size: 15px;
        min-width: 100px;
    }

    @media screen and (max-width: 480px) {
        padding: 10px 16px;
        font-size: 14px;
        min-width: 80px;
    }

    .btn_icon{
        display: none;
        font-size: 20px;

        @media screen and (max-width: 480px) {
            font-size: 18px;
        }
    }
    &.active{
        .btn_icon{
            display: block;
            animation: spin .5s linear infinite;
        }
    }
    &:disabled{
        background-color: #ccc;
        border-color: #ccc;
        cursor: not-allowed;
    }

    &:hover:not(:disabled) {
        background-color: color.scale($button_color, $lightness: -10%);
        border-color: color.scale($button_color, $lightness: -10%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}
.arrow-icon {
    cursor: pointer;
    transition: transform 0.3s ease;
  }
.rotate {
    transform: rotate(180deg);
}

// Spin animation for loading icons
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// Additional mobile-responsive utilities
@media screen and (max-width: $isMobile) {
    .hideOnMobile {
        display: none !important;
    }
}

@media screen and (max-width: 480px) {
    .hideOnSmallMobile {
        display: none !important;
    }
}

.dy_secondary_bttn {
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    font-size: 15px;
    font-weight: 500;
    line-height: 1;
    transition-duration: 0.4s;
    background: #fff;
    color: $primary_color;
    border-radius: 5px;
    outline: 0;
    border: 1px solid $primary_color;
    min-width: 100px;
    cursor: pointer;
}

.container {
    width: 100%;
    max-width: 1300px;
    padding: 0 15px;
    margin: 0 auto;
    box-sizing: border-box;
}

.linkBtn{
    text-decoration: underline;
    font-size: 14px;
    cursor: pointer;
}

.bttn_bg_home{
    background-color: #fcc1a5;
}

.no-data-found-div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    text-align: center;
    color: #333;

  }

  .main-txt {
    font-size: 1.2rem;
    font-weight: bold;
    color: #555;
    margin-bottom: 10px;
  }

  .shine {
    animation: shimmer 1.5s infinite linear;
    background: linear-gradient(
      to right,
      #e0e0e0 0%,
      #f7f7f7 50%,
      #e0e0e0 100%
  );
    background-size: 200% 100%;
    border-radius: 4px;
    background-color: #f0f0f0;
  }

  .input-field {
    position: relative;
    margin: 1rem 0;
    display: flex;
    flex-direction: column;
    width: 100%;

    .input-label {
      position: absolute;
      top: 50%;
      left: 0.75rem;
      transform: translateY(-50%);
      background: white;
      padding: 0 0.25rem;
      font-size: 1rem;
      color: #888;
      transition: all 0.3s ease;
      pointer-events: none;
    }

    .input-element {
      padding: 1.25rem 0.75rem 0.5rem 0.75rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 1rem;
      transition: border-color 0.3s ease;

      &::placeholder {
        color: transparent; // Hides placeholder text initially
      }

      &:focus {
        border-color: $primary_color;
        outline: none;

        & + .input-label {
          top: 0;
          transform: translateY(-50%) scale(0.8);
          color: $primary_color;
        }
      }

      &:not(:placeholder-shown) {
        & + .input-label {
          top: 0;
          transform: translateY(-50%) scale(0.8);
          color: $primary_color;
        }
      }
    }
  }